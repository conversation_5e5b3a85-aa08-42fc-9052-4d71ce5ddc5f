{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "test:tests": "playwright test", "test:local": "BASE_URL=http://localhost:5173 playwright test", "test:production": "BASE_URL=https://thelocaladda.com playwright test", "test:homepage": "playwright test tests/homepage.spec.ts", "test:homepage-elements": "playwright test tests/homepage-elements.spec.ts", "test:search": "playwright test tests/search-functionality.spec.ts", "test:search-validation": "playwright test tests/search-validation.spec.ts", "test:search-all": "playwright test tests/homepage-elements.spec.ts tests/search-functionality.spec.ts tests/search-validation.spec.ts", "test:event-creation": "playwright test tests/event-creation.spec.ts", "test:host-event": "playwright test tests/host-event.spec.ts", "test:host-event-flow": "playwright test tests/host-event.spec.ts --grep \"should complete the host event flow with all required fields\"", "test:host-event-validation": "playwright test tests/host-event.spec.ts --grep \"should validate required fields in host event form\"", "test:host-event-auth": "playwright test tests/host-event.spec.ts --grep \"should handle authentication requirement for host event\"", "test:host-event-valid": "playwright test tests/host-event.spec.ts --grep \"should successfully create a valid event with all required fields filled\"", "test:standard-setup": "playwright test tests/standard-setup.spec.ts", "test:auth": "playwright test tests/standard-setup.spec.ts --headed", "test:discover-events": "playwright test tests/discover-events-comprehensive.spec.ts", "test:discover-events-headed": "playwright test tests/discover-events-comprehensive.spec.ts --headed", "test:discover-events-debug": "playwright test tests/discover-events-comprehensive.spec.ts --headed --debug --timeout=0"}, "dependencies": {"@googlemaps/react-wrapper": "^1.2.0", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@supabase/supabase-js": "^2.49.1", "@tanstack/react-query": "^5.56.2", "@vis.gl/react-google-maps": "^1.5.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.5.0", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.57.0", "react-intersection-observer": "^9.16.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.25.47"}, "devDependencies": {"@eslint/js": "^9.9.0", "@playwright/test": "^1.52.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "dotenv": "^16.5.0", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.8.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}