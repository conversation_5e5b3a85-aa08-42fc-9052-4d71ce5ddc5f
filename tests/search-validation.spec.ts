import { test, expect } from '@playwright/test';
import { performLocationSetupOnly } from './helpers/standard-setup';
import { testSupabase } from './helpers/test-supabase-client';
import { SEARCH_TERMS } from './config/search-terms';

/**
 * Search Result Validation Tests
 * 
 * These tests focus on validating the accuracy and consistency of search results:
 * - Data consistency between UI and database
 * - Search result content validation
 * - Filtering accuracy
 * - Search ranking and relevance
 */

test.describe('Search Result Validation', () => {

  // Test 1: Validate search result content matches database
  test('should display accurate event information in search results', async ({ page }) => {
    console.log('🧪 Starting search result content validation test...');
    test.setTimeout(35000);
    
    try {
      await performLocationSetupOnly(page);
      
      // Perform a search that should return specific results
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const searchInput = page.locator('input[type="search"], input[placeholder*="search" i], input[placeholder*="find" i]').first();
      await expect(searchInput).toBeVisible();
      
      const searchTerm = SEARCH_TERMS.CONTENT_VALIDATION;
      await searchInput.fill(searchTerm);
      await searchInput.press('Enter');
      
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Get events from database
      const { data: dbEvents } = await testSupabase
        .from('events')
        .select(`
          id,
          title,
          start_date
        `)
        .or(
          `title.ilike.%${searchTerm}%,` +
          `description.ilike.%${searchTerm}%,` + 
          `tags.cs.{${searchTerm}}`
        )
        .eq('approval_status', 'approved')
        .order('start_date', { ascending: true });
      
      if (dbEvents && dbEvents.length > 0) {
        console.log(`📊 Found ${dbEvents.length} events in database`);
        
        // Get UI event cards
        const eventCards = page.locator('div.group.w-full.rounded-2xl[class*="shadow-smooth"]');
        const uiCount = await eventCards.count();
        
        console.log(`🔍 Found ${uiCount} event cards in UI`);
        expect(uiCount).toBe(dbEvents.length);
        
        // Validate first few events' content
        const eventsToValidate = Math.min(3, dbEvents.length);
        
        for (let i = 0; i < eventsToValidate; i++) {
          const dbEvent = dbEvents[i];
          const uiCard = eventCards.nth(i);
          
          console.log(`🔍 Validating event ${i + 1}: "${dbEvent.title}"`);
          
          // Check if event title is displayed
          const titleElement = uiCard.locator('h3, h2, h1').first();
          const uiTitle = await titleElement.textContent();
          
          if (uiTitle) {
            expect(uiTitle.toLowerCase()).toContain(dbEvent.title.toLowerCase().substring(0, 10));
            console.log(`✅ Title matches: "${uiTitle}" contains "${dbEvent.title}"`);
          }
          
          // Check if date is displayed (simplified validation)
          const dateElements = uiCard.locator('[class*="calendar"], [class*="date"], text=/today|tomorrow|mon|tue|wed|thu|fri|sat|sun/i');
          const dateCount = await dateElements.count();
          if (dateCount > 0) {
            console.log(`✅ Date information displayed for event ${i + 1}`);
          }
          

        }
        
        console.log('✅ Search result content validation completed');
      } else {
        console.log('ℹ️ No events found in database for validation');
      }
      
      console.log('🎉 Search result content validation test completed successfully');
    } catch (error) {
      console.error('❌ Search result content validation test failed:', error);
      throw error;
    }
  });

  // Test 2: Test search with multiple filters
  test('should handle combined search and filter operations', async ({ page }) => {
    console.log('🧪 Starting combined search and filter test...');
    test.setTimeout(40000);
    
    try {
      await performLocationSetupOnly(page);
      
      // Navigate to events page where filters are available
      await page.goto('/events');
      await page.waitForLoadState('networkidle');
      
      // First, apply a search term
      const searchInput = page.locator('input[type="search"], input[placeholder*="search" i], input[placeholder*="find" i]').first();
      if (await searchInput.count() > 0) {
        await searchInput.fill(SEARCH_TERMS.COMBINED_FILTERS);
        await searchInput.press('Enter');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        console.log('✅ Applied search term: "workshop"');
        
        // Count results after search
        const eventCardsAfterSearch = page.locator('div.group.w-full.rounded-2xl[class*="shadow-smooth"]');
        const searchResultCount = await eventCardsAfterSearch.count();
        console.log(`📊 Results after search: ${searchResultCount}`);
        
        // Try to apply additional filters if available
        const filterButtons = page.locator('button[class*="filter"], [class*="category"]').first();
        if (await filterButtons.count() > 0) {
          await filterButtons.click();
          await page.waitForTimeout(2000);
          await page.waitForLoadState('networkidle');
          
          const eventCardsAfterFilter = page.locator('div.group.w-full.rounded-2xl[class*="shadow-smooth"]');
          const filterResultCount = await eventCardsAfterFilter.count();
          console.log(`📊 Results after additional filter: ${filterResultCount}`);
          
          // Results should be same or fewer after additional filtering
          expect(filterResultCount).toBeLessThanOrEqual(searchResultCount);
          console.log('✅ Combined filtering works correctly');
        } else {
          console.log('ℹ️ No additional filters found to test');
        }
        
        // Verify URL contains both search and filter parameters
        const finalUrl = page.url();
        expect(finalUrl).toContain(`search=${SEARCH_TERMS.COMBINED_FILTERS}`);
        console.log(`✅ URL correctly maintains search parameters: ${finalUrl}`);
      } else {
        console.log('⚠️ Search input not found on events page');
      }
      
      console.log('🎉 Combined search and filter test completed successfully');
    } catch (error) {
      console.error('❌ Combined search and filter test failed:', error);
      throw error;
    }
  });

  // Test 3: Search result ordering and relevance
  test('should display search results in logical order', async ({ page }) => {
    console.log('🧪 Starting search result ordering test...');
    test.setTimeout(30000);
    
    try {
      await performLocationSetupOnly(page);
      
      // Search for a term that should return multiple results
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const searchInput = page.locator('input[type="search"], input[placeholder*="search" i], input[placeholder*="find" i]').first();
      await expect(searchInput).toBeVisible();
      
      await searchInput.fill(SEARCH_TERMS.RESULT_ORDERING);
      await searchInput.press('Enter');
      
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Get event cards
      const eventCards = page.locator('div.group.w-full.rounded-2xl[class*="shadow-smooth"]');
      const cardCount = await eventCards.count();
      
      if (cardCount > 1) {
        console.log(`📊 Found ${cardCount} events to check ordering`);
        
        // Extract dates from the first few events to check chronological ordering
        const eventDates: string[] = [];
        const eventsToCheck = Math.min(5, cardCount);
        
        for (let i = 0; i < eventsToCheck; i++) {
          const card = eventCards.nth(i);
          const titleElement = card.locator('h3, h2, h1').first();
          const title = await titleElement.textContent() || `Event ${i + 1}`;
          
          // Look for date indicators
          const dateElement = card.locator('text=/today|tomorrow|this weekend|mon|tue|wed|thu|fri|sat|sun/i').first();
          const dateText = await dateElement.textContent() || 'Unknown date';
          
          eventDates.push(dateText);
          console.log(`📅 Event ${i + 1}: "${title.substring(0, 30)}..." - ${dateText}`);
        }
        
        // Check if "Today" and "Tomorrow" events appear first (if any)
        const todayIndex = eventDates.findIndex(date => date.toLowerCase().includes('today'));
        const tomorrowIndex = eventDates.findIndex(date => date.toLowerCase().includes('tomorrow'));
        
        if (todayIndex >= 0 && tomorrowIndex >= 0) {
          expect(todayIndex).toBeLessThan(tomorrowIndex);
          console.log('✅ "Today" events appear before "Tomorrow" events');
        }
        
        console.log('✅ Search result ordering appears logical');
      } else {
        console.log('ℹ️ Not enough results to test ordering');
      }
      
      console.log('🎉 Search result ordering test completed successfully');
    } catch (error) {
      console.error('❌ Search result ordering test failed:', error);
      throw error;
    }
  });

  // Test 4: Search with no results validation
  test('should handle no search results gracefully', async ({ page }) => {
    console.log('🧪 Starting no results handling test...');
    test.setTimeout(25000);
    
    try {
      await performLocationSetupOnly(page);
      
      const noResultTerms = SEARCH_TERMS.NO_RESULTS;
      
      for (const searchTerm of noResultTerms) {
        console.log(`🔍 Testing no results for: "${searchTerm}"`);
        
        await page.goto('/');
        await page.waitForLoadState('networkidle');
        
        const searchInput = page.locator('input[type="search"], input[placeholder*="search" i], input[placeholder*="find" i]').first();
        await expect(searchInput).toBeVisible();
        
        await searchInput.fill(searchTerm);
        await searchInput.press('Enter');
        
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        // Verify no event cards are shown
        const eventCards = page.locator('div.group.w-full.rounded-2xl[class*="shadow-smooth"]');
        const cardCount = await eventCards.count();
        expect(cardCount).toBe(0);
        
        // Look for appropriate no results messaging
        const noResultsMessages = page.locator('text=/no events|no results|nothing found|try different|no matches/i');
        const messageCount = await noResultsMessages.count();
        
        if (messageCount > 0) {
          const messageText = await noResultsMessages.first().textContent();
          console.log(`✅ Appropriate no results message: "${messageText}"`);
        } else {
          console.log('⚠️ No explicit "no results" message found');
        }
        
        // Verify page doesn't show error state
        const pageTitle = await page.title();
        expect(pageTitle).toContain('TheLocalAdda');
        console.log(`✅ No results handled gracefully for "${searchTerm}"`);
      }
      
      console.log('🎉 No results handling test completed successfully');
    } catch (error) {
      console.error('❌ No results handling test failed:', error);
      throw error;
    }
  });

});
