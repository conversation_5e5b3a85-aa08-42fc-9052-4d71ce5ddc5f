/**
 * Secure Test Credentials Configuration
 * 
 * This file loads test credentials from environment variables.
 * The actual credentials are stored in .env.test which is gitignored.
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Load test environment variables
config({ path: resolve(__dirname, '../../.env.test') });

export interface TestCredentials {
  email: string;
  password: string;
}

/**
 * Get Google OAuth test credentials
 * Throws an error if credentials are not configured
 */
export function getGoogleTestCredentials(): TestCredentials {
  const email = process.env.TEST_GOOGLE_EMAIL;
  const password = process.env.TEST_GOOGLE_PASSWORD;

  if (!email || !password) {
    throw new Error(
      'Google test credentials not found. Please ensure .env.test file exists with TEST_GOOGLE_EMAIL and TEST_GOOGLE_PASSWORD'
    );
  }

  return {
    email,
    password
  };
}

/**
 * Check if test credentials are available
 */
export function hasTestCredentials(): boolean {
  try {
    getGoogleTestCredentials();
    return true;
  } catch {
    return false;
  }
}

/**
 * Get test configuration
 */
export function getTestConfig() {
  return {
    timeout: parseInt(process.env.TEST_TIMEOUT || '30000'),
    headless: process.env.TEST_HEADLESS !== 'false',
    credentials: hasTestCredentials() ? getGoogleTestCredentials() : null
  };
}
