// Test data scenarios for event creation form validation testing

export interface EventTestData {
  scenario: string;
  expectError: boolean;
  errorFields?: string[];
  data: {
    title?: string;
    description?: string;
    category?: string;
    startDate?: string;
    startTime?: string;
    venue?: string;
    organizerName?: string;
    organizerEmail?: string;
    organizerPhone?: string;
    tags?: string;
    isFree?: boolean;
  };
}

export const eventTestScenarios: EventTestData[] = [
  {
    scenario: "Valid complete event data",
    expectError: false,
    data: {
      title: "Community Cooking Workshop - Valid Test",
      description: "Join us for an amazing community cooking workshop where families can learn to prepare delicious traditional dishes together.",
      category: "Food",
      startDate: "2024-06-25", // 4 days from now (adjust as needed)
      startTime: "18:00",
      venue: "bipin",
      organizerName: "Test Event Organizer",
      organizerEmail: "<EMAIL>",
      organizerPhone: "+91 9876543210",
      tags: "cooking, workshop, community, family, food",
      isFree: true
    }
  },
  {
    scenario: "Missing required title",
    expectError: true,
    errorFields: ["title"],
    data: {
      title: "", // Empty title should cause error
      description: "This event has no title and should fail validation.",
      category: "Food",
      startDate: "2024-06-25",
      startTime: "18:00",
      venue: "bipin",
      organizerName: "Test Organizer",
      organizerEmail: "<EMAIL>",
      organizerPhone: "+91 9876543210",
      isFree: true
    }
  },
  {
    scenario: "Invalid email format",
    expectError: true,
    errorFields: ["organizerEmail"],
    data: {
      title: "Event with Invalid Email",
      description: "This event has an invalid email format.",
      category: "Food",
      startDate: "2024-06-25",
      startTime: "18:00",
      venue: "bipin",
      organizerName: "Test Organizer",
      organizerEmail: "invalid-email-format", // Invalid email
      organizerPhone: "+91 9876543210",
      isFree: true
    }
  },
  {
    scenario: "Missing required description",
    expectError: true,
    errorFields: ["description"],
    data: {
      title: "Event with No Description",
      description: "", // Empty description should cause error
      category: "Food",
      startDate: "2024-06-25",
      startTime: "18:00",
      venue: "bipin",
      organizerName: "Test Organizer",
      organizerEmail: "<EMAIL>",
      organizerPhone: "+91 9876543210",
      isFree: true
    }
  },
  {
    scenario: "Invalid phone number format",
    expectError: true,
    errorFields: ["organizerPhone"],
    data: {
      title: "Event with Invalid Phone",
      description: "This event has an invalid phone number format.",
      category: "Food",
      startDate: "2024-06-25",
      startTime: "18:00",
      venue: "bipin",
      organizerName: "Test Organizer",
      organizerEmail: "<EMAIL>",
      organizerPhone: "invalid-phone", // Invalid phone format
      isFree: true
    }
  },
  {
    scenario: "Past date validation",
    expectError: true,
    errorFields: ["startDate"],
    data: {
      title: "Event with Past Date",
      description: "This event has a date in the past and should fail validation.",
      category: "Food",
      startDate: "2023-01-01", // Past date should cause error
      startTime: "18:00",
      venue: "bipin",
      organizerName: "Test Organizer",
      organizerEmail: "<EMAIL>",
      organizerPhone: "+91 9876543210",
      isFree: true
    }
  },
  {
    scenario: "Multiple validation errors",
    expectError: true,
    errorFields: ["title", "organizerEmail", "organizerPhone"],
    data: {
      title: "", // Empty title
      description: "This event has multiple validation errors.",
      category: "Food",
      startDate: "2024-06-25",
      startTime: "18:00",
      venue: "bipin",
      organizerName: "Test Organizer",
      organizerEmail: "bad-email", // Invalid email
      organizerPhone: "123", // Invalid phone
      isFree: true
    }
  },
  {
    scenario: "Extremely long title validation",
    expectError: true,
    errorFields: ["title"],
    data: {
      title: "This is an extremely long event title that should exceed the maximum character limit for event titles and trigger a validation error because it's way too long for any reasonable event title field and should be rejected by the form validation system",
      description: "This event has an extremely long title.",
      category: "Food",
      startDate: "2024-06-25",
      startTime: "18:00",
      venue: "bipin",
      organizerName: "Test Organizer",
      organizerEmail: "<EMAIL>",
      organizerPhone: "+91 9876543210",
      isFree: true
    }
  },
  {
    scenario: "Special characters in fields",
    expectError: false, // Assuming special characters are allowed
    data: {
      title: "Event with Special Characters: @#$%^&*()",
      description: "This event tests special characters in description: !@#$%^&*()_+-=[]{}|;':\",./<>?",
      category: "Food",
      startDate: "2024-06-25",
      startTime: "18:00",
      venue: "bipin",
      organizerName: "Test Organizer with Symbols !@#",
      organizerEmail: "<EMAIL>",
      organizerPhone: "+91 9876543210",
      isFree: true
    }
  },
  {
    scenario: "Minimum valid data",
    expectError: false,
    data: {
      title: "Min Event",
      description: "Min desc",
      category: "Food",
      startDate: "2024-06-25",
      startTime: "18:00",
      venue: "bipin",
      organizerName: "Test",
      organizerEmail: "<EMAIL>",
      organizerPhone: "+91 9876543210",
      isFree: true
    }
  }
];

// Helper function to get current date + days for dynamic date testing
export function getFutureDate(daysFromNow: number): string {
  const date = new Date();
  date.setDate(date.getDate() + daysFromNow);
  return date.toISOString().split('T')[0]; // YYYY-MM-DD format
}

// Helper function to get past date for validation testing
export function getPastDate(daysAgo: number): string {
  const date = new Date();
  date.setDate(date.getDate() - daysAgo);
  return date.toISOString().split('T')[0]; // YYYY-MM-DD format
}

// Update scenarios with dynamic dates
eventTestScenarios.forEach(scenario => {
  if (scenario.data.startDate === "2024-06-25") {
    scenario.data.startDate = getFutureDate(4); // 4 days from now
  } else if (scenario.data.startDate === "2023-01-01") {
    scenario.data.startDate = getPastDate(30); // 30 days ago
  }
});
