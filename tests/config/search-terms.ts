/**
 * Centralized Search Terms Configuration
 * 
 * This file contains all search terms used across the test suite.
 * Modify these arrays to customize search testing for your specific event data.
 */

export const SEARCH_TERMS = {
  // Basic search terms - should return results in your database
  BASIC: [
    'cook', // Known to have results
    'workshop', // Known to have results
    'food', // Known to have results
    'music', // Specific term with manageable results
    'fitness' // Specific term with manageable results
  ],

  // Single term for count verification - should have known results
  COUNT_VERIFICATION: 'cook',

  // Performance testing terms - mix of common and specific terms
  PERFORMANCE: [
    'music',
    'workshop', 
    'food',
    'art',
    'dance'
  ],

  // Special characters - test encoding and special character handling
  SPECIAL_CHARACTERS: [
    'café',
    'music & dance',
    'art@gallery', 
    'workshop-2024',
    'event#1',
    'fitness/yoga',
    'tech+startup'
  ],

  // Edge cases - should return no results
  EDGE_CASES: [
    { term: '', description: 'empty search' },
    { term: '   ', description: 'whitespace only' },
    { term: 'xyz123nonexistent', description: 'non-existent term' },
    { term: 'a', description: 'single character' },
    { term: 'very long search term that probably does not exist in any event database anywhere', description: 'very long term' }
  ],

  // No results terms - guaranteed to return no results
  NO_RESULTS: [
    'xyznoresults123',
    'impossibleeventname999',
    'thisdoesnotexistanywhere',
    'zzznonexistentevent',
    'invalidtermfortest'
  ],

  // URL parameter testing - with encoding
  URL_PARAMETERS: [
    { url: '/events?search=music', expectedTerm: 'music' },
    { url: '/events?search=workshop%20art', expectedTerm: 'workshop art' },
    { url: '/events?search=caf%C3%A9', expectedTerm: 'café' },
    { url: '/events?search=tech%2Bstartup', expectedTerm: 'tech+startup' }
  ],

  // Content validation - should have specific known events
  CONTENT_VALIDATION: 'cook',

  // Combined filters testing
  COMBINED_FILTERS: 'workshop',

  // Result ordering testing - should return multiple results
  RESULT_ORDERING: 'event',

  // Interaction testing - should return clickable results
  INTERACTION: 'event'
};

/**
 * Customize these terms based on your actual event data:
 * 
 * 1. BASIC terms should match events in your database
 * 2. COUNT_VERIFICATION should have a known number of results
 * 3. NO_RESULTS should definitely return no results
 * 4. SPECIAL_CHARACTERS should test your search encoding
 * 
 * To find good search terms for your database:
 * 1. Query your events table: SELECT DISTINCT title FROM events LIMIT 20;
 * 2. Look for common words in titles and descriptions
 * 3. Test terms manually on your website first
 */

export default SEARCH_TERMS;
