import { test, expect, Page } from '@playwright/test';
import { performLocationSetupOnly } from './helpers/standard-setup';

test.describe('Debug Discover Events Page', () => {
  let page: Page;

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    await performLocationSetupOnly(page);
    
    // Navigate to discover events page
    console.log('🔍 Navigating to discover events page...');
    await page.goto('/events');
    await page.waitForLoadState('networkidle');
    console.log('✅ Successfully navigated to /events page');
  });

  test.afterEach(async () => {
    await page.close();
  });

  test('should debug page structure', async () => {
    console.log('🔍 Debugging page structure...');
    
    // Take a screenshot
    await page.screenshot({ path: 'debug-discover-page.png', fullPage: true });
    
    // Get page title
    const title = await page.title();
    console.log(`📄 Page title: ${title}`);
    
    // Find all h1 elements
    const h1Elements = await page.locator('h1').allTextContents();
    console.log(`📋 H1 elements: ${JSON.stringify(h1Elements)}`);
    
    // Find all h2 elements
    const h2Elements = await page.locator('h2').allTextContents();
    console.log(`📋 H2 elements: ${JSON.stringify(h2Elements)}`);
    
    // Find all h3 elements
    const h3Elements = await page.locator('h3').allTextContents();
    console.log(`📋 H3 elements: ${JSON.stringify(h3Elements)}`);
    
    // Find all buttons
    const buttonTexts = await page.locator('button').allTextContents();
    console.log(`🔘 Button texts: ${JSON.stringify(buttonTexts.slice(0, 10))}...`);
    
    // Find all input elements
    const inputs = await page.locator('input').count();
    console.log(`📝 Input count: ${inputs}`);
    
    // Get input placeholders
    const inputPlaceholders = await page.locator('input').evaluateAll(inputs => 
      inputs.map(input => input.getAttribute('placeholder')).filter(Boolean)
    );
    console.log(`📝 Input placeholders: ${JSON.stringify(inputPlaceholders)}`);
    
    // Check for filter-related text
    const filterTexts = await page.locator('text=filter').allTextContents();
    console.log(`🎛️ Filter texts: ${JSON.stringify(filterTexts)}`);
    
    // Check for Categories text
    const categoryTexts = await page.locator('text=Categories').allTextContents();
    console.log(`📂 Category texts: ${JSON.stringify(categoryTexts)}`);
    
    // Check for Venues text
    const venueTexts = await page.locator('text=Venues').allTextContents();
    console.log(`🏢 Venue texts: ${JSON.stringify(venueTexts)}`);
    
    // Check screen size
    const screenSize = await page.evaluate(() => ({
      width: window.innerWidth,
      height: window.innerHeight
    }));
    console.log(`📱 Screen size: ${screenSize.width}x${screenSize.height}`);
    
    // Check if filter panel is visible
    const filterPanelVisible = await page.locator('h2:text("Filters")').isVisible();
    console.log(`🎛️ Filter panel visible: ${filterPanelVisible}`);
    
    console.log('✅ Debug completed');
  });
});
