import { test, expect } from '@playwright/test';
import { performStandardSetup } from './helpers/standard-setup';
import { eventTestScenarios, EventTestData } from './config/event-test-data';

// Helper function to fill event form with provided data
async function fillEventForm(page: any, data: EventTestData['data']) {
  console.log('📝 Starting to fill event form with provided data...');

  // Helper function to handle location popup interruptions
  const handleLocationPopup = async () => {
    try {
      const locationPopupSelectors = [
        '[role="dialog"]',
        '.modal',
        '.popup',
        'div:has-text("Select location")',
        'div:has-text("Choose location")',
        'button:has-text("Back")',
        'button:has-text("Close")',
        '[aria-label="Close"]'
      ];

      for (const selector of locationPopupSelectors) {
        const element = page.locator(selector);
        if (await element.count() > 0 && await element.first().isVisible()) {
          console.log(`🔍 Location popup detected with selector: ${selector}`);
          if (selector.includes('Back') || selector.includes('Close')) {
            await element.first().click();
            console.log(`✅ Clicked to close location popup`);
            await page.waitForTimeout(1000);
            return true;
          } else {
            const closeButton = element.locator('button:has-text("Back"), button:has-text("Close"), [aria-label="Close"]');
            if (await closeButton.count() > 0) {
              await closeButton.first().click();
              console.log(`✅ Closed location popup using close button`);
              await page.waitForTimeout(1000);
              return true;
            }
          }
        }
      }
      return false;
    } catch (error) {
      console.log(`⚠️ Error handling location popup: ${error.message}`);
      return false;
    }
  };

  // 1. Fill Event Title
  if (data.title !== undefined) {
    console.log(`📝 Filling title: "${data.title}"`);
    const titleSelectors = [
      'input[name="title"]',
      'input[placeholder*="title" i]',
      '#title',
      'input[id*="title"]'
    ];

    let titleFilled = false;
    for (const selector of titleSelectors) {
      try {
        const element = page.locator(selector);
        if (await element.count() > 0 && await element.first().isVisible()) {
          await element.first().fill(data.title);
          console.log(`✅ Title filled with selector: ${selector}`);
          titleFilled = true;
          break;
        }
      } catch (error) {
        continue;
      }
    }
    if (!titleFilled && data.title) {
      console.log('⚠️ Could not fill title field');
    }
    await handleLocationPopup();
  }

  // 2. Fill Event Description
  if (data.description !== undefined) {
    console.log(`📝 Filling description: "${data.description.substring(0, 50)}..."`);
    const descriptionSelectors = [
      'textarea[name="description"]',
      'textarea[placeholder*="description" i]',
      '#description',
      'textarea[id*="description"]',
      'textarea'
    ];

    let descriptionFilled = false;
    for (const selector of descriptionSelectors) {
      try {
        const element = page.locator(selector);
        if (await element.count() > 0 && await element.first().isVisible()) {
          await element.first().fill(data.description);
          console.log(`✅ Description filled with selector: ${selector}`);
          descriptionFilled = true;
          break;
        }
      } catch (error) {
        continue;
      }
    }
    if (!descriptionFilled && data.description) {
      console.log('⚠️ Could not fill description field');
    }
    await handleLocationPopup();
  }

  // 3. Select Category
  if (data.category) {
    console.log(`📝 Selecting category: "${data.category}"`);
    const categorySelectors = [
      'select[name="category_id"]',
      'select[name="category"]',
      'button[role="combobox"]',
      '[data-testid="category-select"]',
      'div:has-text("Select a category")',
      'button:has-text("Select category")'
    ];

    let categorySelected = false;
    for (const selector of categorySelectors) {
      try {
        const element = page.locator(selector);
        if (await element.count() > 0 && await element.first().isVisible()) {
          if (selector.includes('select')) {
            await element.first().selectOption({ index: 1 });
          } else {
            await element.first().click();
            await page.waitForTimeout(1000);

            // Look for the specific category option
            const categoryOptions = [
              `text="${data.category}"`,
              `[role="option"]:has-text("${data.category}")`,
              `li:has-text("${data.category}")`
            ];

            for (const optionSelector of categoryOptions) {
              const option = page.locator(optionSelector);
              if (await option.count() > 0 && await option.first().isVisible()) {
                await option.first().click();
                console.log(`✅ Category selected: ${data.category}`);
                categorySelected = true;
                break;
              }
            }
          }
          if (categorySelected) break;
        }
      } catch (error) {
        continue;
      }
    }
    if (!categorySelected) {
      console.log(`⚠️ Could not select category: ${data.category}`);
    }
    await handleLocationPopup();
  }

  // 4. Fill Start Date
  if (data.startDate) {
    console.log(`📝 Setting start date: "${data.startDate}"`);
    const startDateSelectors = [
      'input[name="start_date"]',
      'input[name="startDate"]',
      'input[name="date"]',
      'input[type="date"]',
      'input[placeholder*="start date" i]',
      'input[placeholder*="event date" i]',
      'input[placeholder*="date" i]'
    ];

    let dateFilled = false;
    for (const selector of startDateSelectors) {
      try {
        const elements = page.locator(selector);
        const count = await elements.count();

        for (let i = 0; i < count; i++) {
          const element = elements.nth(i);
          if (await element.isVisible()) {
            try {
              await element.fill(data.startDate);
              console.log(`✅ Start date filled: ${data.startDate}`);
              dateFilled = true;
              break;
            } catch (fillError) {
              continue;
            }
          }
        }
        if (dateFilled) break;
      } catch (error) {
        continue;
      }
    }
    if (!dateFilled) {
      console.log(`⚠️ Could not fill start date: ${data.startDate}`);
    }
    await handleLocationPopup();
  }

  // 5. Fill Start Time
  if (data.startTime) {
    console.log(`📝 Setting start time: "${data.startTime}"`);
    const startTimeSelectors = [
      'input[name="start_time"]',
      'input[type="time"]',
      'input[placeholder*="time" i]'
    ];

    for (const selector of startTimeSelectors) {
      try {
        const element = page.locator(selector);
        if (await element.count() > 0 && await element.first().isVisible()) {
          await element.first().fill(data.startTime);
          console.log(`✅ Start time filled: ${data.startTime}`);
          break;
        }
      } catch (error) {
        continue;
      }
    }
    await handleLocationPopup();
  }

  // 6. Fill Venue/Location
  if (data.venue) {
    console.log(`📝 Setting venue: "${data.venue}"`);
    const venueSelectors = [
      'input[name="venue"]',
      'input[name="location"]',
      'input[name="address"]',
      'input[placeholder*="venue" i]',
      'input[placeholder*="location" i]',
      'input[placeholder*="address" i]'
    ];

    let venueFilled = false;
    for (const selector of venueSelectors) {
      try {
        const elements = page.locator(selector);
        const count = await elements.count();

        for (let i = 0; i < count; i++) {
          const element = elements.nth(i);
          if (await element.isVisible()) {
            try {
              await element.fill(data.venue);
              console.log(`✅ Venue filled: ${data.venue}`);

              // Wait for autocomplete suggestions
              await page.waitForTimeout(2000);
              const suggestions = page.locator('[role="option"], .suggestion, li').filter({ hasText: new RegExp(data.venue, 'i') });
              if (await suggestions.count() > 0) {
                await suggestions.first().click();
                console.log('✅ Venue suggestion selected');
              }

              venueFilled = true;
              break;
            } catch (fillError) {
              continue;
            }
          }
        }
        if (venueFilled) break;
      } catch (error) {
        continue;
      }
    }
    await handleLocationPopup();
  }

  // 7. Fill Organizer Name
  if (data.organizerName) {
    console.log(`📝 Setting organizer name: "${data.organizerName}"`);
    const organizerNameSelectors = [
      'input[name="organizer_name"]',
      'input[placeholder*="organizer" i]',
      'input[placeholder*="your name" i]',
      'input[id*="organizer"]'
    ];

    for (const selector of organizerNameSelectors) {
      try {
        const element = page.locator(selector);
        if (await element.count() > 0 && await element.first().isVisible()) {
          await element.first().fill(data.organizerName);
          console.log(`✅ Organizer name filled: ${data.organizerName}`);
          break;
        }
      } catch (error) {
        continue;
      }
    }
    await handleLocationPopup();
  }

  // 8. Fill Organizer Email
  if (data.organizerEmail !== undefined) {
    console.log(`📝 Setting organizer email: "${data.organizerEmail}"`);
    const organizerEmailSelectors = [
      'input[name="organizer_email"]',
      'input[type="email"]',
      'input[placeholder*="email" i]',
      'input[id*="email"]'
    ];

    for (const selector of organizerEmailSelectors) {
      try {
        const element = page.locator(selector);
        if (await element.count() > 0 && await element.first().isVisible()) {
          await element.first().fill(data.organizerEmail);
          console.log(`✅ Organizer email filled: ${data.organizerEmail}`);
          break;
        }
      } catch (error) {
        continue;
      }
    }
    await handleLocationPopup();
  }

  // 9. Fill Organizer Phone
  if (data.organizerPhone !== undefined) {
    console.log(`📝 Setting organizer phone: "${data.organizerPhone}"`);
    const organizerPhoneSelectors = [
      'input[name="organizer_phone"]',
      'input[type="tel"]',
      'input[placeholder*="phone" i]',
      'input[placeholder*="mobile" i]',
      'input[id*="phone"]'
    ];

    for (const selector of organizerPhoneSelectors) {
      try {
        const element = page.locator(selector);
        if (await element.count() > 0 && await element.first().isVisible()) {
          await element.first().fill(data.organizerPhone);
          console.log(`✅ Organizer phone filled: ${data.organizerPhone}`);
          break;
        }
      } catch (error) {
        continue;
      }
    }
    await handleLocationPopup();
  }

  // 10. Fill Tags
  if (data.tags) {
    console.log(`📝 Setting tags: "${data.tags}"`);
    const tagsInput = page.locator('input[name="tags"]');
    if (await tagsInput.count() > 0 && await tagsInput.first().isVisible()) {
      await tagsInput.first().fill(data.tags);
      console.log(`✅ Tags filled: ${data.tags}`);
    }
    await handleLocationPopup();
  }

  // 11. Set Free Event Toggle
  if (data.isFree !== undefined) {
    console.log(`📝 Setting free event: ${data.isFree}`);
    const freeEventSelectors = [
      'input[type="checkbox"][name*="free"]',
      'input[type="checkbox"][id*="free"]',
      'button:has-text("Free Event")',
      'label:has-text("Free Event")'
    ];

    for (const selector of freeEventSelectors) {
      try {
        const element = page.locator(selector);
        if (await element.count() > 0 && await element.first().isVisible()) {
          if (selector.includes('input')) {
            if (data.isFree) {
              await element.first().check();
            } else {
              await element.first().uncheck();
            }
          } else {
            await element.first().click();
          }
          console.log(`✅ Free event set to: ${data.isFree}`);
          break;
        }
      } catch (error) {
        continue;
      }
    }
    await handleLocationPopup();
  }

  console.log('✅ Event form filling completed');
}

test.describe('Host Event Flow Tests', () => {

  // Data-driven test for different input scenarios
  eventTestScenarios.forEach((testData) => {
    test(`should handle event creation: ${testData.scenario}`, async ({ page }) => {
      console.log(`🎯 Starting test scenario: ${testData.scenario}`);
      console.log(`📊 Expected result: ${testData.expectError ? 'VALIDATION ERROR' : 'SUCCESS'}`);
      test.setTimeout(120000); // 2 minutes for each scenario

      try {
        // Step 1: Complete authentication setup
        console.log('🔐 Performing authentication setup...');
        await performStandardSetup(page);

        // Step 2: Navigate to Host Event page
        console.log('🏠 Navigating to Host Event page...');
        await page.goto('/create');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);

        // Verify we're on the create event page
        const currentUrl = page.url();
        console.log(`📍 Current URL: ${currentUrl}`);
        expect(currentUrl).toContain('/create');

        // Step 3: Fill form with test data
        console.log('📝 Filling form with test data...');
        await fillEventForm(page, testData.data);

        // Step 4: Submit the form
        console.log('🚀 Submitting event creation form...');
        const submitButton = page.locator('button[type="submit"], button:has-text("Create Event"), button:has-text("Submit")').first();

        if (await submitButton.count() > 0) {
          await submitButton.click();
          console.log('✅ Submit button clicked');

          // Wait for form processing
          await page.waitForTimeout(3000);

          if (testData.expectError) {
            // Step 5a: Verify validation errors appear
            console.log('🔍 Checking for expected validation errors...');

            const validationSelectors = [
              '.error',
              '.invalid',
              '[role="alert"]',
              'text="required"',
              'text="This field is required"',
              '.text-red-500',
              '.text-destructive',
              '[data-testid*="error"]',
              '.field-error'
            ];

            let validationFound = false;
            let errorMessages: string[] = [];

            for (const selector of validationSelectors) {
              const elements = page.locator(selector);
              const count = await elements.count();
              if (count > 0) {
                console.log(`✅ Found ${count} validation error(s) with selector: ${selector}`);
                validationFound = true;

                // Collect error messages
                for (let i = 0; i < Math.min(count, 5); i++) {
                  const text = await elements.nth(i).textContent();
                  if (text && text.trim()) {
                    errorMessages.push(text.trim());
                    console.log(`   Error ${i + 1}: "${text}"`);
                  }
                }
                break;
              }
            }

            if (validationFound) {
              console.log(`✅ SUCCESS: Validation errors detected as expected for scenario: ${testData.scenario}`);
              console.log(`📋 Error messages found: ${errorMessages.join(', ')}`);

              // Verify we're still on the create page (not redirected)
              const finalUrl = page.url();
              expect(finalUrl).toContain('/create');
              console.log('✅ Form correctly prevented submission and stayed on create page');
            } else {
              console.log(`❌ FAILURE: Expected validation errors but none were found for scenario: ${testData.scenario}`);

              // Take screenshot for debugging
              await page.screenshot({ path: `validation-error-missing-${testData.scenario.replace(/\s+/g, '-')}.png`, fullPage: true });

              // Check if form was unexpectedly submitted successfully
              const finalUrl = page.url();
              if (!finalUrl.includes('/create')) {
                throw new Error(`Form was submitted successfully when validation errors were expected. Final URL: ${finalUrl}`);
              } else {
                throw new Error('Expected validation errors were not found on the form');
              }
            }

          } else {
            // Step 5b: Verify successful submission
            console.log('🔍 Checking for successful submission...');

            await page.waitForLoadState('networkidle');
            await page.waitForTimeout(5000);

            // Check for success indicators
            const successIndicators = [
              'text="Event created successfully"',
              'text="Event submitted"',
              'text="Success"',
              '.success',
              '.alert-success',
              '[data-testid*="success"]'
            ];

            let successFound = false;
            for (const indicator of successIndicators) {
              const element = page.locator(indicator);
              if (await element.count() > 0) {
                console.log(`✅ Success indicator found: ${indicator}`);
                successFound = true;
                break;
              }
            }

            // Check if redirected to a success page
            const finalUrl = page.url();
            console.log(`📍 Final URL after submission: ${finalUrl}`);

            if (successFound || finalUrl.includes('/events/') || finalUrl.includes('/my-events') || finalUrl.includes('/dashboard')) {
              console.log(`✅ SUCCESS: Event creation completed successfully for scenario: ${testData.scenario}`);
            } else {
              console.log(`⚠️ WARNING: Success confirmation not clearly detected for scenario: ${testData.scenario}`);

              // Take screenshot for review
              await page.screenshot({ path: `success-unclear-${testData.scenario.replace(/\s+/g, '-')}.png`, fullPage: true });

              // Don't fail the test, just log the uncertainty
              console.log('📸 Screenshot saved for manual review');
            }
          }

        } else {
          throw new Error('Could not find submit button to complete event creation');
        }

        console.log(`🎉 Test scenario completed: ${testData.scenario}`);

      } catch (error) {
        console.error(`❌ Test scenario failed: ${testData.scenario}`, error);

        // Take error screenshot
        await page.screenshot({ path: `scenario-error-${testData.scenario.replace(/\s+/g, '-')}.png`, fullPage: true });
        console.log('📸 Error screenshot saved');

        throw error;
      }
    });
  });

  // Quick test for a single scenario (useful for debugging)
  test('should test single validation scenario - invalid email', async ({ page }) => {
    console.log('🎯 Testing single scenario: Invalid email validation');
    test.setTimeout(60000);

    const testData = eventTestScenarios.find(scenario => scenario.scenario.includes('Invalid email'));
    if (!testData) {
      throw new Error('Test scenario not found');
    }

    try {
      // Complete authentication setup
      await performStandardSetup(page);

      // Navigate to create event page
      await page.goto('/create');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);

      // Fill form with invalid email data
      await fillEventForm(page, testData.data);

      // Submit form
      const submitButton = page.locator('button[type="submit"], button:has-text("Create Event")').first();
      if (await submitButton.count() > 0) {
        await submitButton.click();
        await page.waitForTimeout(3000);

        // Check for validation errors
        const errorElements = page.locator('.error, .invalid, [role="alert"], .text-red-500');
        const errorCount = await errorElements.count();

        if (errorCount > 0) {
          console.log(`✅ SUCCESS: Found ${errorCount} validation errors as expected`);
          for (let i = 0; i < Math.min(errorCount, 3); i++) {
            const errorText = await errorElements.nth(i).textContent();
            console.log(`   Error ${i + 1}: "${errorText}"`);
          }
        } else {
          console.log('❌ FAILURE: No validation errors found');
          await page.screenshot({ path: 'single-test-no-errors.png', fullPage: true });
        }

        // Verify still on create page
        const currentUrl = page.url();
        expect(currentUrl).toContain('/create');
      }

    } catch (error) {
      console.error('❌ Single scenario test failed:', error);
      await page.screenshot({ path: 'single-test-error.png', fullPage: true });
      throw error;
    }
  });

  test('should complete the host event flow with all required fields', async ({ page }) => {
    console.log('🎯 Starting Host Event flow test...');
    test.setTimeout(120000); // 2 minutes for complete flow

    try {
      // Step 1: Complete authentication setup
      console.log('🔐 Performing authentication setup...');
      await performStandardSetup(page);
      
      // Step 2: Navigate to Host Event page
      console.log('🏠 Navigating to Host Event page...');
      
      // Look for "Host Event" button/link on homepage
      const hostEventSelectors = [
        'a:has-text("Host Event")',
        'button:has-text("Host Event")',
        'a[href*="/create"]',
        'button[onclick*="create"]',
        '[data-testid="host-event"]'
      ];
      
      let hostEventButton = null;
      for (const selector of hostEventSelectors) {
        const element = page.locator(selector);
        if (await element.count() > 0 && await element.first().isVisible()) {
          hostEventButton = element.first();
          console.log(`✅ Found Host Event button with selector: ${selector}`);
          break;
        }
      }
      
      if (!hostEventButton) {
        // Try navigating directly to create page
        console.log('⚠️ Host Event button not found, navigating directly to /create');
        await page.goto('/create');
      } else {
        await hostEventButton.click();
      }
      
      // Wait for create event page to load
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
      
      // Verify we're on the create event page
      const currentUrl = page.url();
      console.log(`📍 Current URL: ${currentUrl}`);
      expect(currentUrl).toContain('/create');
      
      // Step 3: Fill out the event creation form
      console.log('📝 Filling out event creation form...');
      
      // Basic Information Section
      console.log('📋 Filling Basic Information...');
      
      // Event Title with unique timestamp
      const now = new Date();
      const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
      const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS
      const uniqueTitle = `Test Automation Event - ${dateStr} ${timeStr}`;

      const titleInput = page.locator('input[name="title"], input[placeholder*="title" i], #title');
      await titleInput.fill(uniqueTitle);
      console.log(`✅ Event title filled: "${uniqueTitle}"`);
      
      // Event Description
      const descriptionSelectors = [
        'textarea[name="description"]',
        'textarea[placeholder*="description" i]',
        '#description',
        'textarea'
      ];
      
      let descriptionInput = null;
      for (const selector of descriptionSelectors) {
        const element = page.locator(selector);
        if (await element.count() > 0) {
          descriptionInput = element.first();
          break;
        }
      }
      
      if (descriptionInput) {
        await descriptionInput.fill('This is a test event created by automation testing. Join us for an amazing experience!');
        console.log('✅ Event description filled');
      }
      
      // Category Selection
      console.log('📂 Selecting event category...');
      const categorySelectors = [
        'select[name="category"]',
        'select[name="category_id"]',
        '[data-testid="category-select"]',
        'button:has-text("Select category")',
        'div:has-text("Category")'
      ];
      
      let categorySelected = false;
      for (const selector of categorySelectors) {
        try {
          const element = page.locator(selector);
          if (await element.count() > 0 && await element.first().isVisible()) {
            if (selector.includes('select')) {
              await element.selectOption({ index: 1 }); // Select first available option
            } else {
              await element.click();
              // Look for category options
              await page.waitForTimeout(1000);
              const categoryOptions = page.locator('[role="option"], li, button').filter({ hasText: /culture|education|food|sports/i });
              if (await categoryOptions.count() > 0) {
                await categoryOptions.first().click();
              }
            }
            console.log(`✅ Category selected with selector: ${selector}`);
            categorySelected = true;
            break;
          }
        } catch (error) {
          continue;
        }
      }
      
      if (!categorySelected) {
        console.log('⚠️ Category selection not found or failed');
      }
      
      // Date and Time Section
      console.log('📅 Setting event date and time...');
      
      // Start Date
      const startDateInput = page.locator('input[name="start_date"], input[type="date"], input[placeholder*="date" i]').first();
      if (await startDateInput.count() > 0) {
        // Set date to tomorrow
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const dateString = tomorrow.toISOString().split('T')[0]; // YYYY-MM-DD format
        await startDateInput.fill(dateString);
        console.log(`✅ Start date set to: ${dateString}`);
      }
      
      // Start Time
      const startTimeInput = page.locator('input[name="start_time"], input[type="time"], input[placeholder*="time" i]').first();
      if (await startTimeInput.count() > 0) {
        await startTimeInput.fill('18:00'); // 6 PM
        console.log('✅ Start time set to 18:00');
      }
      
      // Location/Venue Section
      console.log('📍 Setting event location...');
      
      // Look for venue/location input
      const venueSelectors = [
        'input[name="venue"]',
        'input[placeholder*="venue" i]',
        'input[placeholder*="location" i]',
        'input[placeholder*="address" i]',
        '[data-testid="venue-input"]'
      ];
      
      let venueInput = null;
      for (const selector of venueSelectors) {
        const element = page.locator(selector);
        if (await element.count() > 0 && await element.first().isVisible()) {
          venueInput = element.first();
          break;
        }
      }
      
      if (venueInput) {
        await venueInput.fill('bipin');
        console.log('✅ Venue/location filled');
        
        // Wait for any autocomplete suggestions and select first one if available
        await page.waitForTimeout(2000);
        const suggestions = page.locator('[role="option"], .suggestion, li').filter({ hasText: /bipin/i });
        if (await suggestions.count() > 0) {
          await suggestions.first().click();
          console.log('✅ Venue suggestion selected');
        }
      }
      
      // Organizer Information Section
      console.log('👤 Filling organizer information...');
      
      // Organizer Name
      const organizerNameInput = page.locator('input[name="organizer_name"], input[placeholder*="organizer" i], input[placeholder*="name" i]').first();
      if (await organizerNameInput.count() > 0) {
        await organizerNameInput.fill('Test Organizer');
        console.log('✅ Organizer name filled');
      }
      
      // Organizer Email
      const organizerEmailInput = page.locator('input[name="organizer_email"], input[type="email"], input[placeholder*="email" i]').first();
      if (await organizerEmailInput.count() > 0) {
        await organizerEmailInput.fill('<EMAIL>');
        console.log('✅ Organizer email filled');
      }
      
      // Organizer Phone
      const organizerPhoneInput = page.locator('input[name="organizer_phone"], input[type="tel"], input[placeholder*="phone" i]').first();
      if (await organizerPhoneInput.count() > 0) {
        await organizerPhoneInput.fill('+91 9876543210');
        console.log('✅ Organizer phone filled');
      }
      
      // Pricing Section (if present)
      console.log('💰 Setting event pricing...');
      
      // Check if it's a free event toggle
      const freeEventToggle = page.locator('input[type="checkbox"][name*="free"], button:has-text("Free Event")');
      if (await freeEventToggle.count() > 0) {
        await freeEventToggle.check();
        console.log('✅ Set as free event');
      }
      
      // Step 4: Submit the form
      console.log('🚀 Submitting event creation form...');
      
      // Look for submit button
      const submitSelectors = [
        'button[type="submit"]',
        'button:has-text("Create Event")',
        'button:has-text("Submit")',
        'button:has-text("Publish")',
        'input[type="submit"]'
      ];
      
      let submitButton = null;
      for (const selector of submitSelectors) {
        const element = page.locator(selector);
        if (await element.count() > 0 && await element.first().isVisible()) {
          submitButton = element.first();
          console.log(`✅ Found submit button with selector: ${selector}`);
          break;
        }
      }
      
      if (submitButton) {
        await submitButton.click();
        console.log('✅ Submit button clicked');
        
        // Wait for form submission
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(5000);
        
        // Check for success indicators
        const successIndicators = [
          'text="Event created successfully"',
          'text="Event submitted"',
          'text="Success"',
          '.success',
          '.alert-success'
        ];
        
        let successFound = false;
        for (const indicator of successIndicators) {
          const element = page.locator(indicator);
          if (await element.count() > 0) {
            console.log(`✅ Success indicator found: ${indicator}`);
            successFound = true;
            break;
          }
        }
        
        // Check if redirected to a success page or event details
        const finalUrl = page.url();
        console.log(`📍 Final URL after submission: ${finalUrl}`);
        
        if (successFound || finalUrl.includes('/events/') || finalUrl.includes('/my-events') || finalUrl.includes('/dashboard')) {
          console.log('✅ Event creation appears successful');
        } else {
          console.log('⚠️ Success confirmation not clearly detected, but form was submitted');
        }
        
      } else {
        console.log('❌ Submit button not found');
        throw new Error('Could not find submit button to complete event creation');
      }
      
      // Step 5: Verify event creation
      console.log('🔍 Verifying event creation...');
      
      // Take a screenshot for verification
      await page.screenshot({ path: 'host-event-completion.png', fullPage: true });
      console.log('📸 Screenshot saved: host-event-completion.png');
      
      console.log('🎉 Host Event flow test completed successfully!');
      
    } catch (error) {
      console.error('❌ Host Event flow test failed:', error);
      
      // Take error screenshot
      await page.screenshot({ path: 'host-event-error.png', fullPage: true });
      console.log('📸 Error screenshot saved: host-event-error.png');
      
      throw error;
    }
  });

  test('should validate required fields in host event form', async ({ page }) => {
    console.log('🧪 Starting Host Event form validation test...');
    test.setTimeout(60000);

    try {
      // Complete authentication setup
      await performStandardSetup(page);

      // Navigate to create event page
      await page.goto('/create');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);

      console.log('🔍 Testing form validation for required fields...');

      // Try to submit form without filling required fields
      const submitButton = page.locator('button[type="submit"], button:has-text("Create Event")').first();

      if (await submitButton.count() > 0) {
        await submitButton.click();
        console.log('✅ Clicked submit with empty form');

        // Wait for validation messages
        await page.waitForTimeout(2000);

        // Check for validation error messages
        const validationSelectors = [
          '.error',
          '.invalid',
          '[role="alert"]',
          'text="required"',
          'text="This field is required"',
          '.text-red-500',
          '.text-destructive'
        ];

        let validationFound = false;
        for (const selector of validationSelectors) {
          const elements = page.locator(selector);
          const count = await elements.count();
          if (count > 0) {
            console.log(`✅ Found ${count} validation error(s) with selector: ${selector}`);
            validationFound = true;

            // Log first few validation messages
            for (let i = 0; i < Math.min(count, 3); i++) {
              const text = await elements.nth(i).textContent();
              console.log(`   Validation ${i + 1}: "${text}"`);
            }
            break;
          }
        }

        if (validationFound) {
          console.log('✅ Form validation is working correctly');
        } else {
          console.log('⚠️ No validation errors detected - form might allow empty submission');
        }

        // Check that we're still on the create page (not redirected)
        const currentUrl = page.url();
        expect(currentUrl).toContain('/create');
        console.log('✅ Form correctly prevented submission and stayed on create page');

      } else {
        console.log('⚠️ Submit button not found for validation testing');
      }

      console.log('🎯 Form validation test completed');

    } catch (error) {
      console.error('❌ Form validation test failed:', error);
      await page.screenshot({ path: 'host-event-validation-error.png', fullPage: true });
      throw error;
    }
  });

  test('should handle authentication requirement for host event', async ({ page }) => {
    console.log('🔐 Testing authentication requirement for host event...');
    test.setTimeout(30000);

    try {
      // Navigate to site without authentication
      await page.goto('/');
      await page.waitForLoadState('networkidle');

      // Try to access create event page directly
      await page.goto('/create');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);

      const currentUrl = page.url();
      console.log(`📍 URL after trying to access /create: ${currentUrl}`);

      // Check if redirected to auth page or if auth is required
      if (currentUrl.includes('/auth') || currentUrl.includes('/login') || currentUrl.includes('/signin')) {
        console.log('✅ Correctly redirected to authentication page');
      } else {
        // Check if there's an auth requirement message on the page
        const authRequiredSelectors = [
          'text="Sign in required"',
          'text="Please sign in"',
          'text="Authentication required"',
          'button:has-text("Sign in")',
          'a:has-text("Sign in")'
        ];

        let authRequiredFound = false;
        for (const selector of authRequiredSelectors) {
          const element = page.locator(selector);
          if (await element.count() > 0) {
            console.log(`✅ Found authentication requirement with selector: ${selector}`);
            authRequiredFound = true;
            break;
          }
        }

        if (authRequiredFound) {
          console.log('✅ Authentication requirement is properly enforced');
        } else {
          console.log('⚠️ No clear authentication requirement detected');
        }
      }

      console.log('🎯 Authentication requirement test completed');

    } catch (error) {
      console.error('❌ Authentication requirement test failed:', error);
      await page.screenshot({ path: 'host-event-auth-error.png', fullPage: true });
      throw error;
    }
  });

  test('should successfully create a valid event with all required fields filled', async ({ page }) => {
    console.log('🎯 Starting valid event creation test...');
    test.setTimeout(120000); // 2 minutes for complete flow

    try {
      // Step 1: Try authentication setup, but continue if it fails
      console.log('🔐 Attempting authentication setup...');
      try {
        await performStandardSetup(page);
        console.log('✅ Authentication completed successfully');
      } catch (authError) {
        console.log('⚠️ Authentication failed, trying direct navigation to create page...');
        console.log(`Auth error: ${authError.message}`);

        // Try direct navigation to create page
        await page.goto('/');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
      }

      // Step 2: Navigate to Host Event page
      console.log('🏠 Navigating to Host Event page...');
      await page.goto('/create');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);

      // Verify we're on the create event page
      const currentUrl = page.url();
      console.log(`📍 Current URL: ${currentUrl}`);
      expect(currentUrl).toContain('/create');

      // Step 3: Debug - Check what fields are available
      console.log('🔍 Debugging available form fields...');

      const allInputs = page.locator('input, textarea, select');
      const inputCount = await allInputs.count();
      console.log(`📝 Found ${inputCount} form fields on page:`);

      for (let i = 0; i < Math.min(inputCount, 15); i++) {
        try {
          const input = allInputs.nth(i);
          const tagName = await input.evaluate(el => el.tagName.toLowerCase());
          const type = await input.getAttribute('type');
          const name = await input.getAttribute('name');
          const placeholder = await input.getAttribute('placeholder');
          const id = await input.getAttribute('id');
          const isVisible = await input.isVisible();

          console.log(`  Field ${i + 1}: ${tagName}[type="${type}"] name="${name}" placeholder="${placeholder}" id="${id}" visible=${isVisible}`);
        } catch (error) {
          console.log(`  Field ${i + 1}: Error getting details - ${error.message}`);
        }
      }

      // Helper function to handle location popup interruptions
      const handleLocationPopup = async () => {
        try {
          // Check for location popup/modal
          const locationPopupSelectors = [
            '[role="dialog"]',
            '.modal',
            '.popup',
            'div:has-text("Select location")',
            'div:has-text("Choose location")',
            'button:has-text("Back")',
            'button:has-text("Close")',
            '[aria-label="Close"]'
          ];

          for (const selector of locationPopupSelectors) {
            const element = page.locator(selector);
            if (await element.count() > 0 && await element.first().isVisible()) {
              console.log(`🔍 Location popup detected with selector: ${selector}`);

              // Try to close it
              if (selector.includes('Back') || selector.includes('Close')) {
                await element.first().click();
                console.log(`✅ Clicked to close location popup`);
                await page.waitForTimeout(1000);
                return true;
              } else {
                // Look for close button within the popup
                const closeButton = element.locator('button:has-text("Back"), button:has-text("Close"), [aria-label="Close"]');
                if (await closeButton.count() > 0) {
                  await closeButton.first().click();
                  console.log(`✅ Closed location popup using close button`);
                  await page.waitForTimeout(1000);
                  return true;
                }
              }
            }
          }
          return false;
        } catch (error) {
          console.log(`⚠️ Error handling location popup: ${error.message}`);
          return false;
        }
      };

      // Step 4: Fill out ALL required fields systematically, section by section
      console.log('📝 Filling out complete event creation form section by section...');

      // SECTION 1: BASIC EVENT INFORMATION
      console.log('📋 SECTION 1: BASIC EVENT INFORMATION');

      // Check for location popup before starting
      await handleLocationPopup();

      // 1.1 Event Title (Required)
      console.log('📋 1.1 Filling Event Title...');

      // Create unique title with today's date and timestamp
      const now = new Date();
      const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
      const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS
      const uniqueTitle = `Automation Test Event - Community Cooking Workshop - ${dateStr} ${timeStr}`;

      const titleSelectors = [
        'input[name="title"]',
        'input[placeholder*="title" i]',
        '#title',
        'input[id*="title"]'
      ];

      let titleFilled = false;
      for (const selector of titleSelectors) {
        try {
          const element = page.locator(selector);
          if (await element.count() > 0 && await element.first().isVisible()) {
            await element.first().fill(uniqueTitle);
            console.log(`✅ Event title filled with selector: ${selector}`);
            console.log(`📝 Unique title: "${uniqueTitle}"`);
            titleFilled = true;
            break;
          }
        } catch (error) {
          continue;
        }
      }

      if (!titleFilled) {
        throw new Error('Could not find or fill event title field');
      }

      // Check for location popup after title
      await handleLocationPopup();

      // 1.2 Event Description (Required)
      console.log('📋 1.2 Filling Event Description...');
      const descriptionSelectors = [
        'textarea[name="description"]',
        'textarea[placeholder*="description" i]',
        '#description',
        'textarea[id*="description"]',
        'textarea'
      ];

      let descriptionFilled = false;
      for (const selector of descriptionSelectors) {
        try {
          const element = page.locator(selector);
          if (await element.count() > 0 && await element.first().isVisible()) {
            await element.first().fill('Join us for an amazing community cooking workshop where families can learn to prepare delicious traditional dishes together. This hands-on experience will bring neighbors closer while sharing culinary traditions. Perfect for all skill levels!');
            console.log(`✅ Event description filled with selector: ${selector}`);
            descriptionFilled = true;
            break;
          }
        } catch (error) {
          continue;
        }
      }

      if (!descriptionFilled) {
        throw new Error('Could not find or fill event description field');
      }

      // Check for location popup after description
      await handleLocationPopup();

      // 1.3 Category Selection (Required)
      console.log('📋 1.3 Selecting Event Category...');
      const categorySelectors = [
        'select[name="category_id"]',
        'select[name="category"]',
        'button[role="combobox"]',
        '[data-testid="category-select"]',
        'div:has-text("Select a category")',
        'button:has-text("Select category")'
      ];

      let categorySelected = false;
      for (const selector of categorySelectors) {
        try {
          const element = page.locator(selector);
          if (await element.count() > 0 && await element.first().isVisible()) {
            console.log(`🔍 Trying category selector: ${selector}`);

            if (selector.includes('select')) {
              // Handle HTML select dropdown
              await element.first().selectOption({ index: 1 }); // Select first available option
              console.log(`✅ Category selected via select dropdown`);
              categorySelected = true;
              break;
            } else {
              // Handle custom dropdown/combobox
              await element.first().click();
              await page.waitForTimeout(1000);

              // Look for category options
              const categoryOptions = [
                'text="Food"',
                'text="Culture"',
                'text="Education"',
                'text="Families"',
                '[role="option"]:has-text("Food")',
                '[role="option"]:has-text("Culture")',
                'li:has-text("Food")',
                'li:has-text("Culture")'
              ];

              for (const optionSelector of categoryOptions) {
                const option = page.locator(optionSelector);
                if (await option.count() > 0 && await option.first().isVisible()) {
                  await option.first().click();
                  console.log(`✅ Category selected: ${optionSelector}`);
                  categorySelected = true;
                  break;
                }
              }

              if (categorySelected) {
                // Wait a moment for the selection to register and close any dropdowns
                await page.waitForTimeout(1000);

                // Click elsewhere to close any open dropdowns/modals
                await page.click('body');
                await page.waitForTimeout(500);

                console.log(`✅ Category selection completed and dropdown closed`);
                break;
              }
            }
          }
        } catch (error) {
          console.log(`⚠️ Category selector failed: ${selector} - ${error.message}`);
          continue;
        }
      }

      if (!categorySelected) {
        console.log('⚠️ Category selection failed, but continuing with test...');
      } else {
        console.log('✅ Category selection process completed, moving to next field...');
      }

      // Check for location popup after category selection
      await handleLocationPopup();

      // 1.4 Fill Tags field (discovered in debug)
      console.log('📋 1.4 Filling Event Tags...');
      const tagsInput = page.locator('input[name="tags"]');
      if (await tagsInput.count() > 0 && await tagsInput.first().isVisible()) {
        await tagsInput.first().fill('cooking, workshop, community, family, food');
        console.log('✅ Event tags filled: cooking, workshop, community, family, food');
      } else {
        console.log('⚠️ Tags input not found');
      }

      // Check for location popup after tags
      await handleLocationPopup();

      console.log('✅ SECTION 1: BASIC EVENT INFORMATION - COMPLETED');

      // SECTION 2: EVENT DETAILS (DATE & TIME)
      console.log('📋 SECTION 2: EVENT DETAILS (DATE & TIME)');

      // Check for location popup before starting this section
      await handleLocationPopup();

      // 2.1 Expand Event Details section first
      console.log('📋 2.1 Expanding Event Details section...');

      const eventDetailsSections = [
        'button:has-text("Event Details")',
        'button:has-text("Select date")',
        'button:has-text("Date")',
        'button:has-text("Schedule")',
        'button:has-text("When")'
      ];

      let eventDetailsExpanded = false;
      for (const sectionSelector of eventDetailsSections) {
        try {
          const sections = page.locator(sectionSelector);
          const sectionCount = await sections.count();

          for (let i = 0; i < sectionCount; i++) {
            const section = sections.nth(i);
            if (await section.isVisible()) {
              const sectionText = await section.textContent();
              console.log(`🔍 Expanding Event Details section: "${sectionText}"`);

              try {
                await section.click({ timeout: 5000 });
                await page.waitForTimeout(1500);
                console.log(`✅ Expanded Event Details section: "${sectionText}"`);
                eventDetailsExpanded = true;

                // Check for location popup after expanding
                await handleLocationPopup();
                break;
              } catch (clickError) {
                console.log(`⚠️ Could not expand section "${sectionText}": ${clickError.message}`);
                continue;
              }
            }
          }

          if (eventDetailsExpanded) break;
        } catch (error) {
          continue;
        }
      }

      // 2.2 Setting Start Date (4 days from now)
      console.log('📋 2.2 Setting Start Date...');

      // Calculate date 4 days from now
      const startDate = new Date();
      startDate.setDate(startDate.getDate() + 4);
      const dateString = startDate.toISOString().split('T')[0]; // YYYY-MM-DD format
      console.log(`📅 Target start date (4 days from now): ${dateString}`);

      let startDateFilled = false;

      // Check for location popup before date filling
      await handleLocationPopup();

      // First, try to expand sections that might contain date fields
      console.log('🔍 Looking for expandable sections containing date fields...');

      const expandableSections = [
        // Look for sections with "Event Details", "Date", "Schedule", "When" etc.
        'button:has-text("Event Details")',
        'button:has-text("Date")',
        'button:has-text("Schedule")',
        'button:has-text("When")',
        'button:has-text("Time")',
        'button:has-text("Details")',
        // Look for accordion-style buttons with arrows
        'button[aria-expanded="false"]:has-text("Event")',
        'button[aria-expanded="false"]:has-text("Date")',
        'button[aria-expanded="false"]:has-text("Details")',
        // Generic expandable elements
        '[aria-expanded="false"]',
        'button[data-state="closed"]',
        '.accordion-trigger',
        '.collapsible-trigger'
      ];

      let sectionsExpanded = 0;
      const maxSectionsToExpand = 5; // Limit to prevent infinite loops

      for (const sectionSelector of expandableSections) {
        if (sectionsExpanded >= maxSectionsToExpand) {
          console.log(`⚠️ Reached maximum section expansion limit (${maxSectionsToExpand}), stopping...`);
          break;
        }

        try {
          const sections = page.locator(sectionSelector);
          const sectionCount = await sections.count();

          for (let i = 0; i < Math.min(sectionCount, 2); i++) { // Limit to first 2 sections per selector
            if (sectionsExpanded >= maxSectionsToExpand) break;

            const section = sections.nth(i);
            if (await section.isVisible()) {
              const sectionText = await section.textContent();
              console.log(`🔍 Found expandable section: "${sectionText}" with selector: ${sectionSelector}`);

              try {
                await section.click({ timeout: 5000 }); // Add timeout to click
                await page.waitForTimeout(1500); // Wait for section to expand
                console.log(`✅ Clicked to expand section: "${sectionText}"`);
                sectionsExpanded++;

                // Check if date fields appeared after expanding
                const dateFields = page.locator('input[type="date"], input[placeholder*="date" i], input[name*="date" i]');
                const dateFieldCount = await dateFields.count();

                if (dateFieldCount > 0) {
                  console.log(`🎯 Found ${dateFieldCount} date fields after expanding "${sectionText}"`);

                  // Try to fill the first visible date field
                  for (let j = 0; j < dateFieldCount; j++) {
                    const dateField = dateFields.nth(j);
                    if (await dateField.isVisible()) {
                      try {
                        await dateField.fill(dateString);
                        console.log(`✅ Start date set to: ${dateString} in expanded section "${sectionText}"`);
                        startDateFilled = true;
                        break;
                      } catch (fillError) {
                        console.log(`⚠️ Failed to fill date field ${j + 1}: ${fillError.message}`);
                        continue;
                      }
                    }
                  }

                  if (startDateFilled) break;
                }
              } catch (clickError) {
                console.log(`⚠️ Could not click section "${sectionText}": ${clickError.message}`);
                continue;
              }
            }
          }

          if (startDateFilled) break;
        } catch (error) {
          continue;
        }
      }

      console.log(`📊 Total sections expanded: ${sectionsExpanded}`);

      // If still not found, try direct date field selectors
      if (!startDateFilled) {
        console.log('🔍 Trying direct date field selectors...');

        const startDateSelectors = [
          'input[name="start_date"]',
          'input[name="startDate"]',
          'input[name="date"]',
          'input[type="date"]',
          'input[placeholder*="start date" i]',
          'input[placeholder*="event date" i]',
          'input[placeholder*="date" i]',
          'input[id*="start"]',
          'input[id*="date"]',
          'input[class*="date"]'
        ];

        for (const selector of startDateSelectors) {
          try {
            const elements = page.locator(selector);
            const count = await elements.count();

            for (let i = 0; i < count; i++) {
              const element = elements.nth(i);
              if (await element.isVisible()) {
                try {
                  await element.fill(dateString);
                  console.log(`✅ Start date set to: ${dateString} with selector: ${selector} (element ${i + 1})`);
                  startDateFilled = true;
                  break;
                } catch (fillError) {
                  continue;
                }
              }
            }

            if (startDateFilled) break;
          } catch (error) {
            continue;
          }
        }
      }

      if (!startDateFilled) {
        console.log('⚠️ Start date field not found after expanding sections, but continuing with test...');
      }

      // Check for location popup after date section
      await handleLocationPopup();

      console.log('✅ SECTION 2: EVENT DETAILS (DATE & TIME) - COMPLETED');

      // SECTION 3: LOCATION & VENUE
      console.log('📋 SECTION 3: LOCATION & VENUE');

      // Check for location popup before starting this section
      await handleLocationPopup();

      // 3.1 Expand Location & Venue section
      console.log('📋 3.1 Expanding Location & Venue section...');

      const locationSections = [
        'button:has-text("Location & Venue")',
        'button:has-text("Location")',
        'button:has-text("Venue")',
        'button:has-text("Where")',
        'button:has-text("Address")'
      ];

      let locationSectionExpanded = false;
      for (const sectionSelector of locationSections) {
        try {
          const sections = page.locator(sectionSelector);
          const sectionCount = await sections.count();

          for (let i = 0; i < sectionCount; i++) {
            const section = sections.nth(i);
            if (await section.isVisible()) {
              const sectionText = await section.textContent();
              console.log(`� Expanding Location section: "${sectionText}"`);

              try {
                await section.click({ timeout: 5000 });
                await page.waitForTimeout(1500);
                console.log(`✅ Expanded Location section: "${sectionText}"`);
                locationSectionExpanded = true;

                // Check for location popup after expanding
                await handleLocationPopup();
                break;
              } catch (clickError) {
                console.log(`⚠️ Could not expand section "${sectionText}": ${clickError.message}`);
                continue;
              }
            }
          }

          if (locationSectionExpanded) break;
        } catch (error) {
          continue;
        }
      }

      // 3.2 Fill Venue/Location field
      console.log('📋 3.2 Setting Event Venue...');

      // Check for location popup before venue filling
      await handleLocationPopup();

      const venueSelectors = [
        'input[name="venue"]',
        'input[name="location"]',
        'input[name="address"]',
        'input[placeholder*="venue" i]',
        'input[placeholder*="location" i]',
        'input[placeholder*="address" i]',
        'input[placeholder*="where" i]',
        '[data-testid="venue-input"]',
        'input[id*="venue"]',
        'input[id*="location"]'
      ];

      let venueFilled = false;
      for (const selector of venueSelectors) {
        try {
          const elements = page.locator(selector);
          const count = await elements.count();

          for (let i = 0; i < count; i++) {
            const element = elements.nth(i);
            if (await element.isVisible()) {
              try {
                await element.fill('bipin');
                console.log(`✅ Venue filled with selector: ${selector} (element ${i + 1})`);

                // Wait for autocomplete suggestions and select if available
                await page.waitForTimeout(2000);
                const suggestions = page.locator('[role="option"], .suggestion, li').filter({ hasText: /bipin/i });
                if (await suggestions.count() > 0) {
                  await suggestions.first().click();
                  console.log('✅ Venue suggestion selected');
                }

                venueFilled = true;
                break;
              } catch (fillError) {
                console.log(`⚠️ Failed to fill venue field ${i + 1}: ${fillError.message}`);
                continue;
              }
            }
          }

          if (venueFilled) break;
        } catch (error) {
          continue;
        }
      }

      if (!venueFilled) {
        console.log('⚠️ Venue field not found, but continuing with test...');
      }

      // Check for location popup after venue filling
      await handleLocationPopup();

      console.log('✅ SECTION 3: LOCATION & VENUE - COMPLETED');

      // 6. End Date (if required)
      console.log('📋 6. Setting End Date...');
      const endDateSelectors = [
        'input[name="end_date"]',
        'input[placeholder*="end date" i]',
        'input[id*="end"]'
      ];

      for (const selector of endDateSelectors) {
        try {
          const element = page.locator(selector);
          if (await element.count() > 0 && await element.first().isVisible()) {
            // Set end date same as start date
            const nextWeek = new Date();
            nextWeek.setDate(nextWeek.getDate() + 7);
            const dateString = nextWeek.toISOString().split('T')[0];

            await element.first().fill(dateString);
            console.log(`✅ End date set to: ${dateString} with selector: ${selector}`);
            break;
          }
        } catch (error) {
          continue;
        }
      }

      // 7. End Time (if required)
      console.log('📋 7. Setting End Time...');
      const endTimeSelectors = [
        'input[name="end_time"]',
        'input[placeholder*="end time" i]'
      ];

      for (const selector of endTimeSelectors) {
        try {
          const element = page.locator(selector);
          if (await element.count() > 0 && await element.first().isVisible()) {
            await element.first().fill('17:00'); // 5 PM
            console.log(`✅ End time set to 17:00 with selector: ${selector}`);
            break;
          }
        } catch (error) {
          continue;
        }
      }

      console.log('📍 Moving to Location/Venue section...');

      // Re-scan for all form fields after filling basic information
      console.log('🔍 Re-scanning for all form fields after basic info...');

      const allFieldsAfterBasic = page.locator('input, textarea, select');
      const fieldCountAfterBasic = await allFieldsAfterBasic.count();
      console.log(`📝 Found ${fieldCountAfterBasic} form fields after basic info:`);

      for (let i = 0; i < Math.min(fieldCountAfterBasic, 20); i++) {
        try {
          const field = allFieldsAfterBasic.nth(i);
          const tagName = await field.evaluate(el => el.tagName.toLowerCase());
          const type = await field.getAttribute('type');
          const name = await field.getAttribute('name');
          const placeholder = await field.getAttribute('placeholder');
          const id = await field.getAttribute('id');
          const isVisible = await field.isVisible();
          const value = await field.inputValue().catch(() => '');

          console.log(`  Field ${i + 1}: ${tagName}[type="${type}"] name="${name}" placeholder="${placeholder}" id="${id}" visible=${isVisible} value="${value}"`);
        } catch (error) {
          console.log(`  Field ${i + 1}: Error getting details - ${error.message}`);
        }
      }

      // SECTION 4: ORGANIZER DETAILS
      console.log('📋 SECTION 4: ORGANIZER DETAILS');

      // Check for location popup before starting this section
      await handleLocationPopup();

      // 4.1 Expand Organizer Details section
      console.log('📋 4.1 Expanding Organizer Details section...');

      const organizerSections = [
        'button:has-text("Organizer Details")',
        'button:has-text("Organizer")',
        'button:has-text("Contact")',
        'button:has-text("Host")',
        'button:has-text("Who")'
      ];

      let organizerSectionExpanded = false;
      for (const sectionSelector of organizerSections) {
        try {
          const sections = page.locator(sectionSelector);
          const sectionCount = await sections.count();

          for (let i = 0; i < sectionCount; i++) {
            const section = sections.nth(i);
            if (await section.isVisible()) {
              const sectionText = await section.textContent();
              console.log(`🔍 Expanding Organizer section: "${sectionText}"`);

              try {
                await section.click({ timeout: 5000 });
                await page.waitForTimeout(1500);
                console.log(`✅ Expanded Organizer section: "${sectionText}"`);
                organizerSectionExpanded = true;

                // Check for location popup after expanding
                await handleLocationPopup();
                break;
              } catch (clickError) {
                console.log(`⚠️ Could not expand section "${sectionText}": ${clickError.message}`);
                continue;
              }
            }
          }

          if (organizerSectionExpanded) break;
        } catch (error) {
          continue;
        }
      }

      // 4.2 Organizer Name (Required)
      console.log('📋 4.2 Setting Organizer Name...');

      // Check for location popup before filling organizer name
      await handleLocationPopup();
      const organizerNameSelectors = [
        'input[name="organizer_name"]',
        'input[placeholder*="organizer" i]',
        'input[placeholder*="your name" i]',
        'input[id*="organizer"]'
      ];

      let organizerNameFilled = false;
      for (const selector of organizerNameSelectors) {
        try {
          const element = page.locator(selector);
          if (await element.count() > 0 && await element.first().isVisible()) {
            await element.first().fill('Test Event Organizer');
            console.log(`✅ Organizer name filled with selector: ${selector}`);
            organizerNameFilled = true;
            break;
          }
        } catch (error) {
          continue;
        }
      }

      if (!organizerNameFilled) {
        console.log('⚠️ Organizer name field not found, but continuing with test...');
      }

      // 4.3 Organizer Email (Required)
      console.log('📋 4.3 Setting Organizer Email...');

      // Check for location popup before filling email
      await handleLocationPopup();
      const organizerEmailSelectors = [
        'input[name="organizer_email"]',
        'input[type="email"]',
        'input[placeholder*="email" i]',
        'input[id*="email"]'
      ];

      let organizerEmailFilled = false;
      for (const selector of organizerEmailSelectors) {
        try {
          const element = page.locator(selector);
          if (await element.count() > 0 && await element.first().isVisible()) {
            await element.first().fill('<EMAIL>');
            console.log(`✅ Organizer email filled with selector: ${selector}`);
            organizerEmailFilled = true;
            break;
          }
        } catch (error) {
          continue;
        }
      }

      if (!organizerEmailFilled) {
        console.log('⚠️ Organizer email field not found, but continuing with test...');
      }

      // 4.4 Organizer Phone (Required)
      console.log('📋 4.4 Setting Organizer Phone...');

      // Check for location popup before filling phone
      await handleLocationPopup();
      const organizerPhoneSelectors = [
        'input[name="organizer_phone"]',
        'input[type="tel"]',
        'input[placeholder*="phone" i]',
        'input[placeholder*="mobile" i]',
        'input[id*="phone"]'
      ];

      let organizerPhoneFilled = false;
      for (const selector of organizerPhoneSelectors) {
        try {
          const element = page.locator(selector);
          if (await element.count() > 0 && await element.first().isVisible()) {
            await element.first().fill('+91 9876543210');
            console.log(`✅ Organizer phone filled with selector: ${selector}`);
            organizerPhoneFilled = true;
            break;
          }
        } catch (error) {
          continue;
        }
      }

      if (!organizerPhoneFilled) {
        console.log('⚠️ Organizer phone field not found, but continuing with test...');
      }

      // Check for location popup after organizer details
      await handleLocationPopup();

      console.log('✅ SECTION 4: ORGANIZER DETAILS - COMPLETED');

      // SECTION 5: FINAL DETAILS & SUBMISSION
      console.log('📋 SECTION 5: FINAL DETAILS & SUBMISSION');

      // Check for location popup before final section
      await handleLocationPopup();

      // 5.1 Set as Free Event (to avoid pricing complications)
      console.log('📋 5.1 Setting Event as Free...');
      const freeEventSelectors = [
        'input[type="checkbox"][name*="free"]',
        'input[type="checkbox"][id*="free"]',
        'button:has-text("Free Event")',
        'label:has-text("Free Event")'
      ];

      for (const selector of freeEventSelectors) {
        try {
          const element = page.locator(selector);
          if (await element.count() > 0 && await element.first().isVisible()) {
            if (selector.includes('input')) {
              await element.first().check();
            } else {
              await element.first().click();
            }
            console.log(`✅ Set as free event with selector: ${selector}`);
            break;
          }
        } catch (error) {
          continue;
        }
      }

      // 13. Comprehensive field filling - fill any remaining empty required fields
      console.log('📋 13. Filling any remaining empty fields...');

      // Find all visible input fields that are empty
      const allVisibleInputs = page.locator('input:visible, textarea:visible, select:visible');
      const visibleCount = await allVisibleInputs.count();
      console.log(`🔍 Found ${visibleCount} visible form fields, checking which need values...`);

      for (let i = 0; i < visibleCount; i++) {
        try {
          const field = allVisibleInputs.nth(i);
          const type = await field.getAttribute('type');
          const name = await field.getAttribute('name');
          const placeholder = await field.getAttribute('placeholder');
          const value = await field.inputValue().catch(() => '');
          const tagName = await field.evaluate(el => el.tagName.toLowerCase());

          // Skip if already filled or if it's a file input
          if (value || type === 'file' || type === 'submit' || type === 'button') {
            continue;
          }

          console.log(`🔍 Empty field found: ${tagName}[type="${type}"] name="${name}" placeholder="${placeholder}"`);

          // Fill based on field characteristics
          if (type === 'email' || name?.includes('email') || placeholder?.toLowerCase().includes('email')) {
            await field.fill('<EMAIL>');
            console.log(`✅ Filled email field: ${name || placeholder}`);
          } else if (type === 'tel' || name?.includes('phone') || placeholder?.toLowerCase().includes('phone')) {
            await field.fill('+91 9876543210');
            console.log(`✅ Filled phone field: ${name || placeholder}`);
          } else if (type === 'date' || name?.includes('date') || placeholder?.toLowerCase().includes('date')) {
            const nextWeek = new Date();
            nextWeek.setDate(nextWeek.getDate() + 7);
            const dateString = nextWeek.toISOString().split('T')[0];
            await field.fill(dateString);
            console.log(`✅ Filled date field: ${name || placeholder} with ${dateString}`);
          } else if (type === 'time' || name?.includes('time') || placeholder?.toLowerCase().includes('time')) {
            await field.fill('14:00');
            console.log(`✅ Filled time field: ${name || placeholder} with 14:00`);
          } else if (name?.includes('venue') || name?.includes('location') || name?.includes('address') ||
                     placeholder?.toLowerCase().includes('venue') || placeholder?.toLowerCase().includes('location') || placeholder?.toLowerCase().includes('address')) {
            await field.fill('bipin');
            console.log(`✅ Filled venue field: ${name || placeholder}`);
          } else if (name?.includes('organizer') || name?.includes('contact') || placeholder?.toLowerCase().includes('organizer') || placeholder?.toLowerCase().includes('contact')) {
            await field.fill('Test Event Organizer');
            console.log(`✅ Filled organizer field: ${name || placeholder}`);
          } else if (type === 'text' || type === 'url' || tagName === 'textarea') {
            // Generic text field
            await field.fill('Test Value');
            console.log(`✅ Filled text field: ${name || placeholder} with generic value`);
          }

          // Small delay between field fills
          await page.waitForTimeout(500);

        } catch (error) {
          console.log(`⚠️ Error filling field ${i + 1}: ${error.message}`);
          continue;
        }
      }

      // 14. Force reveal all form sections by scrolling and clicking everything (limited)
      console.log('📋 14. Attempting to reveal all form sections...');

      // Scroll to bottom to trigger any lazy-loaded sections
      await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
      await page.waitForTimeout(2000);

      // Look for and click specific form section buttons that we know work
      const formSectionButtons = [
        'button:has-text("Event Details")',
        'button:has-text("Organizer Details")',
        'button:has-text("Location")',
        'button:has-text("Venue")',
        'button:has-text("Date")',
        'button:has-text("Time")'
      ];

      let buttonsClicked = 0;
      const maxButtonsToClick = 3; // Limit to prevent infinite loops

      for (const buttonSelector of formSectionButtons) {
        if (buttonsClicked >= maxButtonsToClick) {
          console.log(`⚠️ Reached maximum button click limit (${maxButtonsToClick}), stopping...`);
          break;
        }

        try {
          const buttons = page.locator(buttonSelector);
          const buttonCount = await buttons.count();

          for (let i = 0; i < Math.min(buttonCount, 1); i++) { // Only click first button of each type
            if (buttonsClicked >= maxButtonsToClick) break;

            const button = buttons.nth(i);
            if (await button.isVisible()) {
              const buttonText = await button.textContent();
              console.log(`🔍 Clicking form section: "${buttonText}"`);

              try {
                await button.click({ timeout: 5000 });
                await page.waitForTimeout(1000);
                buttonsClicked++;

                // Check if new fields appeared
                const newFieldCount = await page.locator('input:visible, textarea:visible, select:visible').count();
                console.log(`📝 Field count after clicking "${buttonText}": ${newFieldCount}`);
              } catch (clickError) {
                console.log(`⚠️ Could not click "${buttonText}": ${clickError.message}`);
              }
            }
          }
        } catch (error) {
          continue;
        }
      }

      console.log(`📊 Total form section buttons clicked: ${buttonsClicked}`);

      // Look for any collapsible sections or accordions
      const expandableElements = [
        '[aria-expanded="false"]',
        '.collapsed',
        '.accordion',
        '.collapsible',
        'details',
        'summary'
      ];

      for (const selector of expandableElements) {
        try {
          const elements = page.locator(selector);
          const count = await elements.count();

          for (let i = 0; i < count; i++) {
            const element = elements.nth(i);
            if (await element.isVisible()) {
              await element.click();
              await page.waitForTimeout(1000);
              console.log(`✅ Expanded section with selector: ${selector}`);
            }
          }
        } catch (error) {
          continue;
        }
      }

      // Final comprehensive field scan after all expansion attempts
      console.log('🔍 Final comprehensive field scan...');
      try {
        const finalFields = page.locator('input, textarea, select');
        const finalFieldCount = await finalFields.count();
        console.log(`📝 Final field count: ${finalFieldCount}`);

      for (let i = 0; i < Math.min(finalFieldCount, 25); i++) {
        try {
          const field = finalFields.nth(i);
          const tagName = await field.evaluate(el => el.tagName.toLowerCase());
          const type = await field.getAttribute('type');
          const name = await field.getAttribute('name');
          const placeholder = await field.getAttribute('placeholder');
          const isVisible = await field.isVisible();
          const value = await field.inputValue().catch(() => '');

          if (isVisible && !value && type !== 'file' && type !== 'submit' && type !== 'button') {
            console.log(`🔍 Empty visible field: ${tagName}[type="${type}"] name="${name}" placeholder="${placeholder}"`);

            // Try to fill this field
            try {
              if (type === 'checkbox') {
                await field.check();
                console.log(`✅ Checked checkbox: ${name || placeholder}`);
              } else if (name?.includes('date') || placeholder?.toLowerCase().includes('date')) {
                const nextWeek = new Date();
                nextWeek.setDate(nextWeek.getDate() + 7);
                const dateString = nextWeek.toISOString().split('T')[0];
                await field.fill(dateString);
                console.log(`✅ Filled date field: ${name || placeholder}`);
              } else if (name?.includes('time') || placeholder?.toLowerCase().includes('time')) {
                await field.fill('14:00');
                console.log(`✅ Filled time field: ${name || placeholder}`);
              } else if (name?.includes('venue') || name?.includes('location') || placeholder?.toLowerCase().includes('venue')) {
                await field.fill('bipin');
                console.log(`✅ Filled venue field: ${name || placeholder}`);
              } else if (name?.includes('email') || placeholder?.toLowerCase().includes('email')) {
                await field.fill('<EMAIL>');
                console.log(`✅ Filled email field: ${name || placeholder}`);
              } else if (name?.includes('phone') || placeholder?.toLowerCase().includes('phone')) {
                await field.fill('+91 9876543210');
                console.log(`✅ Filled phone field: ${name || placeholder}`);
              } else if (type === 'text' || type === 'url' || tagName === 'textarea') {
                await field.fill('Test Value');
                console.log(`✅ Filled text field: ${name || placeholder}`);
              }

              await page.waitForTimeout(300);
            } catch (fillError) {
              console.log(`⚠️ Could not fill field ${name || placeholder}: ${fillError.message}`);
            }
          }
        } catch (error) {
          continue;
        }
      }

      } catch (scanError) {
        console.log(`⚠️ Error during field scan: ${scanError.message}`);
      }

      // 15. Accept Terms and Conditions (comprehensive search)
      console.log('📋 15. Accepting Terms and Conditions...');
      const termsSelectors = [
        'input[type="checkbox"]',
        'input[type="checkbox"][name*="terms"]',
        'input[type="checkbox"][name*="agree"]',
        'input[type="checkbox"][name*="consent"]',
        'input[type="checkbox"][name*="accept"]',
        'input[type="checkbox"][id*="terms"]',
        'input[type="checkbox"][id*="agree"]',
        'label:has-text("terms") input[type="checkbox"]',
        'label:has-text("agree") input[type="checkbox"]',
        'label:has-text("accept") input[type="checkbox"]',
        'label:has-text("consent") input[type="checkbox"]'
      ];

      let checkedBoxes = 0;
      for (const selector of termsSelectors) {
        try {
          const elements = page.locator(selector);
          const count = await elements.count();

          for (let i = 0; i < count; i++) {
            const element = elements.nth(i);
            if (await element.isVisible() && !(await element.isChecked())) {
              await element.check();
              checkedBoxes++;
              console.log(`✅ Terms checkbox ${checkedBoxes} checked with selector: ${selector}`);
            }
          }
        } catch (error) {
          continue;
        }
      }

      console.log(`✅ Total checkboxes checked: ${checkedBoxes}`);

      // 14. Submit the form
      console.log('🚀 Submitting the complete event creation form...');

      // Take a screenshot before submission
      await page.screenshot({ path: 'host-event-before-submission.png', fullPage: true });
      console.log('📸 Screenshot saved before submission');

      // Find and click submit button
      const submitSelectors = [
        'button[type="submit"]',
        'button:has-text("Create Event")',
        'button:has-text("Submit")',
        'button:has-text("Publish Event")',
        'input[type="submit"]'
      ];

      let submitButton = null;
      for (const selector of submitSelectors) {
        const element = page.locator(selector);
        if (await element.count() > 0 && await element.first().isVisible()) {
          submitButton = element.first();
          console.log(`✅ Found submit button with selector: ${selector}`);
          break;
        }
      }

      if (!submitButton) {
        throw new Error('Could not find submit button');
      }

      // Click submit button
      await submitButton.click();
      console.log('✅ Submit button clicked');

      // Wait for form submission and potential redirect
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(5000);

      // Check for success indicators
      console.log('🔍 Checking for success indicators...');

      const successIndicators = [
        'text="Event created successfully"',
        'text="Event submitted successfully"',
        'text="Event submitted for approval"',
        'text="Success"',
        'text="Thank you"',
        '.success',
        '.alert-success',
        '[role="alert"]:has-text("success")',
        '[class*="toast"]:has-text("success")'
      ];

      let successFound = false;
      let successMessage = '';

      for (const indicator of successIndicators) {
        try {
          const element = page.locator(indicator);
          if (await element.count() > 0 && await element.first().isVisible()) {
            successMessage = await element.first().textContent() || '';
            console.log(`✅ Success indicator found: ${indicator} - "${successMessage}"`);
            successFound = true;
            break;
          }
        } catch (error) {
          continue;
        }
      }

      // Check final URL
      const finalUrl = page.url();
      console.log(`📍 Final URL after submission: ${finalUrl}`);

      // Determine if submission was successful
      const urlIndicatesSuccess = finalUrl.includes('/events/') ||
                                 finalUrl.includes('/my-events') ||
                                 finalUrl.includes('/dashboard') ||
                                 finalUrl.includes('/success');

      if (successFound || urlIndicatesSuccess) {
        console.log('🎉 Event creation appears successful!');
        if (successMessage) {
          console.log(`📝 Success message: "${successMessage}"`);
        }
        if (urlIndicatesSuccess) {
          console.log(`📍 URL indicates success: ${finalUrl}`);
        }
      } else {
        console.log('⚠️ Success confirmation not clearly detected');
        console.log('📍 Checking if still on create page...');

        if (finalUrl.includes('/create')) {
          // Check for validation errors
          const validationErrors = page.locator('[role="alert"], .error, .text-red-500');
          const errorCount = await validationErrors.count();

          if (errorCount > 0) {
            console.log(`❌ Found ${errorCount} validation errors after submission`);
            for (let i = 0; i < Math.min(errorCount, 3); i++) {
              const errorText = await validationErrors.nth(i).textContent();
              console.log(`   Error ${i + 1}: "${errorText}"`);
            }
            throw new Error('Form submission failed due to validation errors');
          } else {
            console.log('⚠️ Still on create page but no validation errors detected');
          }
        }
      }

      // Take final screenshot
      await page.screenshot({ path: 'host-event-valid-creation-complete.png', fullPage: true });
      console.log('📸 Final screenshot saved: host-event-valid-creation-complete.png');

      console.log('🎉 Valid event creation test completed successfully!');

    } catch (error) {
      console.error('❌ Valid event creation test failed:', error);
      await page.screenshot({ path: 'host-event-valid-creation-error.png', fullPage: true });
      throw error;
    }
  });

  test('should systematically fill all sections of event creation form', async ({ page }) => {
    console.log('🎯 Starting systematic section-by-section event creation test...');
    test.setTimeout(180000); // 3 minutes for complete systematic flow

    try {
      // Step 1: Complete authentication setup
      console.log('🔐 Attempting authentication setup...');
      try {
        await performStandardSetup(page);
        console.log('✅ Authentication completed successfully');
      } catch (authError) {
        console.log('⚠️ Authentication failed, trying direct navigation to create page...');
        await page.goto('/');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
      }

      // Step 2: Navigate to Host Event page
      console.log('🏠 Navigating to Host Event page...');
      await page.goto('/create');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);

      // Helper function to handle location popup interruptions
      const handleLocationPopup = async () => {
        try {
          const locationPopupSelectors = [
            '[role="dialog"]',
            '.modal',
            '.popup',
            'div:has-text("Select location")',
            'div:has-text("Choose location")',
            'button:has-text("Back")',
            'button:has-text("Close")',
            '[aria-label="Close"]'
          ];

          for (const selector of locationPopupSelectors) {
            const element = page.locator(selector);
            if (await element.count() > 0 && await element.first().isVisible()) {
              console.log(`🔍 Location popup detected with selector: ${selector}`);
              if (selector.includes('Back') || selector.includes('Close')) {
                await element.first().click();
                console.log(`✅ Clicked to close location popup`);
                await page.waitForTimeout(1000);
                return true;
              } else {
                const closeButton = element.locator('button:has-text("Back"), button:has-text("Close"), [aria-label="Close"]');
                if (await closeButton.count() > 0) {
                  await closeButton.first().click();
                  console.log(`✅ Closed location popup using close button`);
                  await page.waitForTimeout(1000);
                  return true;
                }
              }
            }
          }
          return false;
        } catch (error) {
          console.log(`⚠️ Error handling location popup: ${error.message}`);
          return false;
        }
      };

      // Helper function to systematically process each section
      async function processSection(sectionName: string, sectionSelectors: string[], fieldMappings: any) {
        console.log(`\n📋 ========== SECTION: ${sectionName} ==========`);

        // Step 1: Try to expand the section (skip if empty selector provided)
        console.log(`🔍 Step 1: Attempting to expand ${sectionName} section...`);
        let sectionExpanded = false;

        // Filter out empty selectors
        const validSectionSelectors = sectionSelectors.filter(selector => selector.trim() !== '');

        if (validSectionSelectors.length === 0) {
          console.log(`   ⚠️ No section selectors provided, skipping expansion for ${sectionName}`);
          sectionExpanded = true; // Consider it "expanded" so we proceed to field scanning
        } else {
          for (const sectionSelector of validSectionSelectors) {
            try {
              const sections = page.locator(sectionSelector);
              const sectionCount = await sections.count();
              console.log(`   Found ${sectionCount} elements for selector: ${sectionSelector}`);

              for (let i = 0; i < sectionCount; i++) {
                const section = sections.nth(i);
                if (await section.isVisible()) {
                  const sectionText = await section.textContent();
                  console.log(`   🎯 Attempting to expand: "${sectionText}"`);

                  try {
                    await section.click({ timeout: 5000 });
                    await page.waitForTimeout(2000);
                    console.log(`   ✅ Successfully expanded: "${sectionText}"`);
                    sectionExpanded = true;
                    await handleLocationPopup();
                    break;
                  } catch (clickError) {
                    console.log(`   ⚠️ Could not expand "${sectionText}": ${clickError.message}`);
                    continue;
                  }
                }
              }
              if (sectionExpanded) break;
            } catch (error) {
              console.log(`   ⚠️ Error with selector ${sectionSelector}: ${error.message}`);
              continue;
            }
          }
        }

        if (sectionExpanded) {
          console.log(`✅ ${sectionName} section expanded successfully`);
        } else {
          console.log(`⚠️ Could not expand ${sectionName} section, but continuing...`);
        }

        // Step 2: Scan for all visible fields in this section
        console.log(`🔍 Step 2: Scanning for all visible fields in ${sectionName}...`);
        const allFields = page.locator('input:visible, textarea:visible, select:visible');
        const fieldCount = await allFields.count();
        console.log(`   📝 Found ${fieldCount} visible form fields`);

        // Debug: Log details of all visible fields
        console.log(`   🔍 Debugging all visible fields:`);
        for (let i = 0; i < Math.min(fieldCount, 10); i++) {
          try {
            const field = allFields.nth(i);
            const tagName = await field.evaluate(el => el.tagName.toLowerCase());
            const type = await field.getAttribute('type');
            const name = await field.getAttribute('name');
            const placeholder = await field.getAttribute('placeholder');
            const id = await field.getAttribute('id');
            const className = await field.getAttribute('class');
            console.log(`      Field ${i + 1}: ${tagName}[type="${type}"] name="${name}" placeholder="${placeholder}" id="${id}" class="${className}"`);
          } catch (error) {
            console.log(`      Field ${i + 1}: Error getting details - ${error.message}`);
          }
        }

        // Step 3: Fill each type of field based on mappings
        console.log(`📝 Step 3: Filling fields in ${sectionName}...`);

        for (const [fieldType, config] of Object.entries(fieldMappings)) {
          const fieldConfig = config as { selectors: string[], value: string, type: string, hasAutocomplete?: boolean };
          console.log(`   🎯 Processing ${fieldType}...`);

          let fieldFilled = false;
          for (const selector of fieldConfig.selectors) {
            try {
              const elements = page.locator(selector);
              const count = await elements.count();

              for (let i = 0; i < count; i++) {
                const element = elements.nth(i);
                if (await element.isVisible()) {
                  try {
                    console.log(`      Attempting to fill ${fieldType} with selector: ${selector}`);

                    if (fieldConfig.type === 'select') {
                      await element.selectOption({ index: 1 });
                    } else if (fieldConfig.type === 'checkbox') {
                      await element.check();
                    } else if (fieldConfig.type === 'checkbox-uncheck') {
                      // Special handling for terms and conditions checkbox
                      if (fieldType.includes('Enable Organizer') && selector === 'input[type="checkbox"]') {
                        // For generic checkbox selector, check surrounding text to ensure it's the "Enable" checkbox, NOT "I agree"
                        const parentElement = element.locator('..');
                        const grandParentElement = element.locator('../..');
                        const parentText = await parentElement.textContent().catch(() => '');
                        const grandParentText = await grandParentElement.textContent().catch(() => '');
                        const combinedText = (parentText + ' ' + grandParentText).toLowerCase();

                        // Must contain "enable" and NOT contain "i agree" to be the right checkbox
                        const isEnableCheckbox = combinedText.includes('enable') &&
                                               !combinedText.includes('i agree') &&
                                               !combinedText.includes('agree to');

                        if (!isEnableCheckbox) {
                          console.log(`      ⚠️ Skipping checkbox - not "Enable Organizer" checkbox: "${parentText.substring(0, 50)}..."`);
                          continue; // Skip this checkbox, try next one
                        }
                        console.log(`      🎯 Found "Enable Organizer" checkbox with text: "${parentText.substring(0, 50)}..."`);
                      }

                      // FORCE UNCHECK the "Enable Organizer Terms and Conditions" checkbox
                      console.log(`      🔧 Force unchecking ${fieldType} checkbox...`);
                      try {
                        const isChecked = await element.isChecked();
                        console.log(`      📋 Current state of ${fieldType}: ${isChecked ? 'checked' : 'unchecked'}`);

                        if (isChecked) {
                          await element.uncheck();
                          console.log(`      ✅ ${fieldType} FORCE UNCHECKED (set to false)`);
                        } else {
                          console.log(`      ✅ ${fieldType} already unchecked (false)`);
                        }

                        // Verify it's actually unchecked
                        const finalState = await element.isChecked();
                        console.log(`      🔍 Final state of ${fieldType}: ${finalState ? 'checked' : 'unchecked'}`);
                      } catch (error) {
                        console.log(`      ⚠️ Error force unchecking ${fieldType}: ${error.message}`);
                      }
                    } else if (fieldConfig.type === 'slider-off') {
                      // Special handling for sliders/switches that need to be turned OFF (like "Enable Organizer Terms")
                      console.log(`      🔧 Setting ${fieldType} slider/switch to OFF (false)...`);
                      try {
                        // Check if it's a button-based switch/slider
                        const tagName = await element.evaluate(el => el.tagName.toLowerCase());

                        if (tagName === 'button') {
                          // Handle button-based switch (Switch component)
                          const ariaChecked = await element.getAttribute('aria-checked');
                          const dataState = await element.getAttribute('data-state');
                          const isOn = ariaChecked === 'true' || dataState === 'checked';

                          console.log(`      📋 Current switch state: ${isOn ? 'ON (enabled)' : 'OFF (disabled)'}`);

                          // ALWAYS try to click to ensure it's OFF, even if it appears to be OFF already
                          if (isOn) {
                            console.log(`      🎯 Switch is ON - clicking to turn OFF...`);
                            await element.click({ force: true });
                            console.log(`      ✅ ${fieldType} switch clicked to turn OFF`);
                          } else {
                            console.log(`      🎯 Switch appears OFF - clicking once to ensure it's truly OFF...`);
                            await element.click({ force: true });
                            await page.waitForTimeout(300);
                            // Check if it turned ON (meaning it was actually OFF), if so click again to turn OFF
                            const afterClickAriaChecked = await element.getAttribute('aria-checked');
                            const afterClickDataState = await element.getAttribute('data-state');
                            const afterClickIsOn = afterClickAriaChecked === 'true' || afterClickDataState === 'checked';
                            if (afterClickIsOn) {
                              console.log(`      🎯 Switch turned ON after click - clicking again to turn OFF...`);
                              await element.click({ force: true });
                              console.log(`      ✅ ${fieldType} switch clicked again to turn OFF`);
                            } else {
                              console.log(`      ✅ ${fieldType} switch confirmed OFF after click`);
                            }
                          }

                          // Wait for state change
                          await page.waitForTimeout(500);

                          // Verify final state
                          const finalAriaChecked = await element.getAttribute('aria-checked');
                          const finalDataState = await element.getAttribute('data-state');
                          const finalIsOn = finalAriaChecked === 'true' || finalDataState === 'checked';
                          console.log(`      🔍 Final switch state: ${finalIsOn ? 'ON (enabled)' : 'OFF (disabled)'}`);

                          if (finalIsOn) {
                            console.log(`      ⚠️ Warning: Switch is still ON after attempts to turn OFF`);
                          }
                        } else if (tagName === 'input') {
                          // Handle input-based switch/slider (fallback)
                          const isChecked = await element.isChecked();
                          console.log(`      📋 Current input state: ${isChecked ? 'checked' : 'unchecked'}`);

                          if (isChecked) {
                            await element.uncheck({ force: true });
                            console.log(`      ✅ ${fieldType} input unchecked (set to false)`);
                          } else {
                            console.log(`      ✅ ${fieldType} input already unchecked (false)`);
                          }

                          // Verify final state
                          const finalState = await element.isChecked();
                          console.log(`      🔍 Final input state: ${finalState ? 'checked' : 'unchecked'}`);
                        } else {
                          console.log(`      ⚠️ Unknown element type for ${fieldType}: ${tagName}`);
                        }
                      } catch (error) {
                        console.log(`      ⚠️ Error setting ${fieldType} slider/switch to OFF: ${error.message}`);
                      }
                    } else if (fieldConfig.type === 'slider-on') {
                      // Special handling for sliders that need to be turned ON (like "I Agree" sliders)
                      console.log(`      🔧 Setting ${fieldType} slider to ON (true)...`);
                      try {
                        // Check if it's a button-based slider
                        const tagName = await element.evaluate(el => el.tagName.toLowerCase());

                        if (tagName === 'button') {
                          // Handle button-based slider
                          const ariaChecked = await element.getAttribute('aria-checked');
                          const dataState = await element.getAttribute('data-state');
                          const isOn = ariaChecked === 'true' || dataState === 'checked';

                          console.log(`      📋 Current slider state: ${isOn ? 'ON (enabled)' : 'OFF (disabled)'}`);

                          if (!isOn) {
                            await element.click();
                            console.log(`      ✅ ${fieldType} slider turned ON (set to true)`);
                          } else {
                            console.log(`      ✅ ${fieldType} slider already ON (true)`);
                          }

                          // Verify final state
                          const finalAriaChecked = await element.getAttribute('aria-checked');
                          const finalDataState = await element.getAttribute('data-state');
                          const finalIsOn = finalAriaChecked === 'true' || finalDataState === 'checked';
                          console.log(`      🔍 Final slider state: ${finalIsOn ? 'ON (enabled)' : 'OFF (disabled)'}`);
                        } else {
                          // Handle checkbox-based slider (fallback)
                          const isChecked = await element.isChecked();
                          console.log(`      📋 Current checkbox state: ${isChecked ? 'checked' : 'unchecked'}`);

                          if (!isChecked) {
                            await element.check();
                            console.log(`      ✅ ${fieldType} checkbox checked (set to true)`);
                          } else {
                            console.log(`      ✅ ${fieldType} checkbox already checked (true)`);
                          }

                          // Verify final state
                          const finalState = await element.isChecked();
                          console.log(`      🔍 Final checkbox state: ${finalState ? 'checked' : 'unchecked'}`);
                        }
                      } catch (error) {
                        console.log(`      ⚠️ Error setting ${fieldType} slider to ON: ${error.message}`);
                      }
                    } else if (fieldConfig.type === 'checkbox-agreement-terms-label' || fieldConfig.type === 'checkbox-agreement-privacy-label') {
                      // Special handling for agreement checkboxes by clicking their labels
                      console.log(`      🔧 Clicking ${fieldType} label/container...`);
                      try {
                        // For label-based selectors, just click the element directly
                        await element.click({ force: true });
                        console.log(`      ✅ ${fieldType} label clicked successfully`);

                        // Wait a moment for the state to update
                        await page.waitForTimeout(300);

                        // Try to verify the checkbox state by looking for the actual checkbox
                        try {
                          const checkbox = element.locator('input[type="checkbox"]').first();
                          if (await checkbox.count() > 0) {
                            const isChecked = await checkbox.isChecked();
                            console.log(`      🔍 Final checkbox state: ${isChecked ? 'checked' : 'unchecked'}`);
                          } else {
                            console.log(`      🔍 Checkbox state verification: Label clicked (checkbox not directly accessible)`);
                          }
                        } catch (verifyError) {
                          console.log(`      🔍 Checkbox state verification: Label clicked (verification not possible)`);
                        }
                      } catch (error) {
                        console.log(`      ⚠️ Error clicking ${fieldType} label: ${error.message}`);
                      }
                    } else if (fieldConfig.type === 'checkbox-agreement-terms' || fieldConfig.type === 'checkbox-agreement-privacy') {
                      // Special handling for agreement checkboxes (I agree to terms/privacy)
                      if (fieldConfig.type === 'checkbox-agreement-terms' || fieldConfig.type === 'checkbox-agreement-privacy') {
                        if (selector === 'input[type="checkbox"]') {
                          // For generic checkbox selector, check surrounding text
                          const parentElement = element.locator('..');
                          const grandParentElement = element.locator('../..');
                          const greatGrandParentElement = element.locator('../../..');
                          const parentText = await parentElement.textContent().catch(() => '');
                          const grandParentText = await grandParentElement.textContent().catch(() => '');
                          const greatGrandParentText = await greatGrandParentElement.textContent().catch(() => '');
                          const combinedText = (parentText + ' ' + grandParentText + ' ' + greatGrandParentText).toLowerCase();

                          // More specific text matching to distinguish between Terms and Privacy checkboxes
                          let isCorrectCheckbox = false;

                          if (fieldConfig.type === 'checkbox-agreement-terms') {
                            // For Terms checkbox: check if ANY parent text contains "terms and conditions" but NOT "privacy"
                            isCorrectCheckbox = (parentText.toLowerCase().includes('terms and conditions') ||
                                               grandParentText.toLowerCase().includes('terms and conditions') ||
                                               greatGrandParentText.toLowerCase().includes('terms and conditions')) &&
                                              !parentText.toLowerCase().includes('privacy policy') &&
                                              !combinedText.includes('enable organizer') &&
                                              !combinedText.includes('this is a free event');
                          } else if (fieldConfig.type === 'checkbox-agreement-privacy') {
                            // For Privacy checkbox: check if ANY parent text contains "privacy policy" but NOT "terms and conditions"
                            isCorrectCheckbox = (parentText.toLowerCase().includes('privacy policy') ||
                                               grandParentText.toLowerCase().includes('privacy policy') ||
                                               greatGrandParentText.toLowerCase().includes('privacy policy')) &&
                                              !parentText.toLowerCase().includes('terms and conditions') &&
                                              !combinedText.includes('enable organizer') &&
                                              !combinedText.includes('this is a free event');
                          }

                          if (!isCorrectCheckbox) {
                            console.log(`      ⚠️ Skipping checkbox - not ${fieldType} related: "${parentText.substring(0, 80)}..."`);
                            console.log(`      🔍 Combined text for debugging: "${combinedText.substring(0, 150)}..."`);
                            continue; // Skip this checkbox, try next one
                          }
                          console.log(`      🎯 Found ${fieldType} checkbox with text: "${parentText.substring(0, 80)}..."`);
                        }
                      }

                      // CHECK the agreement checkbox (only if not already checked)
                      console.log(`      🔧 Checking ${fieldType} checkbox...`);
                      try {
                        const isChecked = await element.isChecked();
                        console.log(`      📋 Current state of ${fieldType}: ${isChecked ? 'checked' : 'unchecked'}`);

                        if (!isChecked) {
                          // Try multiple approaches to check the agreement checkbox
                          let checkSuccess = false;

                          // Approach 1: Direct force check
                          try {
                            await element.check({ force: true });
                            await page.waitForTimeout(300);
                            const state1 = await element.isChecked();
                            if (state1) {
                              console.log(`      ✅ ${fieldType} CHECKED (agreed) with direct force check`);
                              checkSuccess = true;
                            }
                          } catch (error) {
                            console.log(`      ⚠️ Direct force check failed: ${error.message}`);
                          }

                          // Approach 2: Click the checkbox directly if direct check failed
                          if (!checkSuccess) {
                            try {
                              await element.click({ force: true });
                              await page.waitForTimeout(300);
                              const state2 = await element.isChecked();
                              if (state2) {
                                console.log(`      ✅ ${fieldType} CHECKED via direct force click`);
                                checkSuccess = true;
                              }
                            } catch (error) {
                              console.log(`      ⚠️ Direct force click failed: ${error.message}`);
                            }
                          }

                          // Approach 3: Try clicking parent label if direct methods failed
                          if (!checkSuccess) {
                            try {
                              const parentLabel = element.locator('..').locator('label').first();
                              if (await parentLabel.count() > 0) {
                                await parentLabel.click({ force: true });
                                await page.waitForTimeout(300);
                                const state3 = await element.isChecked();
                                if (state3) {
                                  console.log(`      ✅ ${fieldType} CHECKED via parent label click`);
                                  checkSuccess = true;
                                }
                              }
                            } catch (error) {
                              console.log(`      ⚠️ Parent label click failed: ${error.message}`);
                            }
                          }

                          // Approach 4: Try clicking grandparent container
                          if (!checkSuccess) {
                            try {
                              const grandParent = element.locator('../..');
                              await grandParent.click({ force: true });
                              await page.waitForTimeout(300);
                              const state4 = await element.isChecked();
                              if (state4) {
                                console.log(`      ✅ ${fieldType} CHECKED via grandparent container click`);
                                checkSuccess = true;
                              }
                            } catch (error) {
                              console.log(`      ⚠️ Grandparent click failed: ${error.message}`);
                            }
                          }

                          // Verify final state
                          const finalState = await element.isChecked();
                          console.log(`      🔍 Final state of ${fieldType}: ${finalState ? 'checked' : 'unchecked'}`);

                          if (!finalState) {
                            console.log(`      ⚠️ Warning: ${fieldType} could not be checked despite multiple attempts`);
                          }
                        } else {
                          // Even if already checked, ensure it's properly set by unchecking and rechecking
                          console.log(`      🔄 ${fieldType} already checked - ensuring it's properly set...`);
                          try {
                            await element.uncheck({ force: true });
                            await page.waitForTimeout(200);
                            await element.check({ force: true });
                            await page.waitForTimeout(300);
                            const reCheckState = await element.isChecked();
                            console.log(`      ✅ ${fieldType} RE-CHECKED to ensure proper state: ${reCheckState ? 'checked' : 'unchecked'}`);
                          } catch (recheckError) {
                            console.log(`      ⚠️ Could not re-check ${fieldType}, but it was already checked: ${recheckError.message}`);
                          }
                        }
                      } catch (error) {
                        console.log(`      ⚠️ Error checking ${fieldType}: ${error.message}`);
                      }
                    } else if (fieldConfig.type === 'radio') {
                      await element.click();
                    } else if (fieldConfig.type === 'date') {
                      // Special handling for date fields
                      const tagName = await element.evaluate(el => el.tagName.toLowerCase());
                      const inputType = await element.getAttribute('type');

                      if (tagName === 'input' && inputType === 'date') {
                        // Direct date input
                        await element.fill(fieldConfig.value);
                        console.log(`      ✅ ${fieldType} filled with date: ${fieldConfig.value}`);
                      } else if (tagName === 'button' || selector.includes('button')) {
                        // Date picker button - click to open
                        await element.click();
                        await page.waitForTimeout(1000);
                        console.log(`      🗓️ ${fieldType} date picker opened`);

                        // Try to find and click today + 4 days
                        const targetDate = new Date();
                        targetDate.setDate(targetDate.getDate() + 4);
                        const dayOfMonth = targetDate.getDate();

                        // Look for the date in the calendar
                        const dateSelectors = [
                          `button:has-text("${dayOfMonth}")`,
                          `[role="gridcell"]:has-text("${dayOfMonth}")`,
                          `td:has-text("${dayOfMonth}")`,
                          `div:has-text("${dayOfMonth}")`
                        ];

                        for (const dateSelector of dateSelectors) {
                          const dateElements = page.locator(dateSelector);
                          const dateCount = await dateElements.count();
                          if (dateCount > 0) {
                            // Click the last occurrence (likely the correct month)
                            await dateElements.last().click();
                            console.log(`      ✅ ${fieldType} date selected: ${dayOfMonth}`);
                            break;
                          }
                        }
                      } else {
                        // Try as regular input
                        await element.fill(fieldConfig.value);
                      }
                    } else {
                      await element.fill(fieldConfig.value);
                    }

                    console.log(`      ✅ ${fieldType} filled successfully`);
                    fieldFilled = true;

                    // Handle autocomplete if needed
                    if (fieldConfig.hasAutocomplete) {
                      await page.waitForTimeout(2000);
                      const suggestions = page.locator('[role="option"], .suggestion, li');
                      if (await suggestions.count() > 0) {
                        await suggestions.first().click();
                        console.log(`      ✅ ${fieldType} autocomplete suggestion selected`);
                      }
                    }
                    break;
                  } catch (fillError) {
                    console.log(`      ⚠️ Failed to fill ${fieldType}: ${fillError.message}`);
                    continue;
                  }
                }
              }
              if (fieldFilled) break;
            } catch (error) {
              console.log(`      ⚠️ Error with selector ${selector}: ${error.message}`);
              continue;
            }
          }

          if (!fieldFilled) {
            console.log(`   ⚠️ ${fieldType} field not found or could not be filled`);
          }

          await handleLocationPopup();
          await page.waitForTimeout(500);
        }

        console.log(`✅ ========== ${sectionName} - COMPLETED ==========\n`);
        await handleLocationPopup();
      }

      // Take initial screenshot
      await page.screenshot({ path: 'systematic-host-event-start.png', fullPage: true });
      console.log('📸 Initial screenshot saved');
      await handleLocationPopup();

      // INITIAL SCAN: Check what fields are already visible on the page
      console.log('\n🔍 ========== INITIAL FIELD SCAN ==========');
      const initialFields = page.locator('input:visible, textarea:visible, select:visible');
      const initialFieldCount = await initialFields.count();
      console.log(`📝 Found ${initialFieldCount} visible form fields on initial page load`);

      // Log details of all initially visible fields
      for (let i = 0; i < Math.min(initialFieldCount, 15); i++) {
        try {
          const field = initialFields.nth(i);
          const tagName = await field.evaluate(el => el.tagName.toLowerCase());
          const type = await field.getAttribute('type');
          const name = await field.getAttribute('name');
          const placeholder = await field.getAttribute('placeholder');
          const id = await field.getAttribute('id');
          console.log(`   Field ${i + 1}: ${tagName}[type="${type}"] name="${name}" placeholder="${placeholder}" id="${id}"`);
        } catch (error) {
          console.log(`   Field ${i + 1}: Error getting details - ${error.message}`);
        }
      }
      console.log('✅ ========== INITIAL FIELD SCAN COMPLETED ==========\n');

      // SECTION 1: BASIC EVENT INFORMATION - Fill visible fields FIRST (don't expand anything)
      await processSection(
        'BASIC EVENT INFORMATION',
        [
          // ONLY use empty string to skip section expansion - fields are already visible!
          ''  // Empty string to skip section expansion and just scan all visible fields
          // DO NOT try to expand sections as this hides the basic fields
        ],
        {
          'Event Title': {
            selectors: [
              'input[name="title"]',  // ✅ This is the exact field name we found!
              'input[name="eventTitle"]',
              'input[name="event_title"]',
              'input[name="name"]',
              'input[placeholder*="catchy title" i]',  // ✅ Match the exact placeholder
              'input[placeholder*="title" i]',
              'input[placeholder*="event name" i]',
              'input[placeholder*="name of" i]',
              '[data-testid="event-title"]',
              'input[id*="title"]',
              'input[id*="name"]'
            ],
            value: `Test Community Event - Systematic Section Test - ${new Date().toISOString().slice(0, 19).replace(/T/, ' ').replace(/:/g, '-')}`,
            type: 'text'
          },
          'Event Description': {
            selectors: [
              'textarea[name="description"]',  // ✅ This is the exact field name we found!
              'textarea[name="eventDescription"]',
              'textarea[name="event_description"]',
              'textarea[placeholder*="Describe your event in detail" i]',  // ✅ Match the exact placeholder
              'textarea[placeholder*="description" i]',
              'textarea[placeholder*="details" i]',
              'textarea[placeholder*="about" i]',
              '[data-testid="event-description"]',
              'textarea[id*="description"]',
              'div[contenteditable="true"]'
            ],
            value: 'This is a comprehensive test event for our community. Join us for an amazing experience with activities, networking, and fun. This event is designed to bring people together and create lasting memories.',
            type: 'textarea'
          },
          'Event Category': {
            selectors: [
              'select',  // ✅ We found a select element, let's try the generic selector first
              'select[name="category"]',
              'select[name="categoryId"]',
              'select[name="category_id"]',
              'select[name="event_category"]',
              '[data-testid="category-select"]',
              'input[name="category"]',
              'select[id*="category"]',
              'button[role="combobox"]'
            ],
            value: 'Community',
            type: 'select'
          },
          'Event Tags': {
            selectors: [
              'input[name="tags"]',  // ✅ This is the exact field name we found!
              'input[name="eventTags"]',
              'input[name="event_tags"]',
              'input[placeholder*="music, live, indie, jazz" i]',  // ✅ Match the exact placeholder
              'input[placeholder*="tags" i]',
              'input[placeholder*="keywords" i]',
              'input[id*="tags"]'
            ],
            value: 'cooking, workshop, community, family, food',
            type: 'text'
          }
        }
      );

      // SECTION 2: DATE & TIME
      const startDate = new Date();
      startDate.setDate(startDate.getDate() + 4);
      const dateString = startDate.toISOString().split('T')[0];

      await processSection(
        'DATE & TIME',
        [
          'button:has-text("Date & Time")',
          'button:has-text("When")',
          'button:has-text("Schedule")',
          'button:has-text("Event Date")',
          'button:has-text("Date")',
          'button:has-text("Time")',
          'button:has-text("Event Details")',
          '[data-testid="date-time-section"]',
          'h3:has-text("Date")',
          'h3:has-text("Time")',
          'div[role="button"]:has-text("Date")',
          'div[role="button"]:has-text("Time")'
        ],
        {
          'Start Date': {
            selectors: [
              'input[name="startDate"]',  // Try camelCase first
              'input[name="start_date"]',
              'input[name="date"]',
              'input[name="eventDate"]',
              'input[type="date"]',
              'input[placeholder*="start date" i]',
              'input[placeholder*="event date" i]',
              'input[placeholder*="date" i]',
              'input[placeholder*="select date" i]',
              'input[placeholder*="choose date" i]',
              'input[id*="start"]',
              'input[id*="date"]',
              'input[class*="date"]',
              '[data-testid*="date"]',
              'button:has-text("Select date")',
              'button[aria-label*="date" i]',
              // Look for date picker triggers
              'div[role="button"]:has-text("Select")',
              'div[role="button"]:has-text("Date")'
            ],
            value: dateString,
            type: 'date'
          },
          'Start Time': {
            selectors: [
              'input[name="start_time"]',
              'input[name="startTime"]',
              'input[type="time"]',
              'input[placeholder*="start time" i]',
              'input[placeholder*="time" i]',
              'input[id*="time"]'
            ],
            value: '15:00',
            type: 'time'
          },
          'End Date': {
            selectors: [
              'input[name="endDate"]',  // Try camelCase first
              'input[name="end_date"]',
              'input[name="eventEndDate"]',
              'input[type="date"]:not([name*="start"])',  // Any date input that's not start date
              'input[placeholder*="end date" i]',
              'input[placeholder*="finish date" i]',
              'input[id*="end"]',
              'input[id*="finish"]',
              '[data-testid*="end-date"]',
              'button:has-text("End date")',
              'button[aria-label*="end date" i]'
            ],
            value: dateString,
            type: 'date'
          },
          'End Time': {
            selectors: [
              'input[name="end_time"]',
              'input[name="endTime"]',
              'input[placeholder*="end time" i]'
            ],
            value: '17:00',
            type: 'time'
          }
        }
      );

      // SECTION 3: LOCATION & VENUE
      console.log('\n📋 ========== SECTION: LOCATION & VENUE ==========');

      // First, expand the location section
      const locationSectionSelectors = [
        'button:has-text("Location & Venue")',
        'button:has-text("Location")',
        'button:has-text("Venue")',
        'button:has-text("Where")',
        'button:has-text("Address")',
        'h3:has-text("Location")',
        'div[role="button"]:has-text("Location")'
      ];

      console.log('🔍 Step 1: Attempting to expand LOCATION & VENUE section...');
      let locationSectionExpanded = false;

      for (const selector of locationSectionSelectors) {
        try {
          const elements = page.locator(selector);
          const count = await elements.count();
          console.log(`   Found ${count} elements for selector: ${selector}`);

          if (count > 0) {
            const element = elements.first();
            if (await element.isVisible()) {
              console.log(`   🎯 Attempting to expand: "${await element.textContent()}"`);
              await element.click();
              await page.waitForTimeout(1000);
              locationSectionExpanded = true;
              console.log(`   ✅ Successfully expanded: "${await element.textContent()}"`);
              break;
            }
          }
        } catch (error) {
          continue;
        }
      }

      if (locationSectionExpanded) {
        console.log('✅ LOCATION & VENUE section expanded successfully');
      } else {
        console.log('⚠️ Could not expand LOCATION & VENUE section, but continuing...');
      }

      // Step 2: Look for venue search input and dropdown
      console.log('🔍 Step 2: Looking for venue selection options...');

      // First try to find venue search input (based on test output showing "Search venues..." placeholder)
      const venueSearchSelectors = [
        'input[placeholder="Search venues..."]',  // Exact match first
        'input[placeholder*="Search venues" i]',
        'input[placeholder*="venue" i]',
        'input[placeholder*="search" i]',
        'input[name="venue"]',
        'input[name="location"]',
        'input[name="address"]'
      ];

      let venueSelected = false;

      // Try venue search input first
      for (const selector of venueSearchSelectors) {
        try {
          const elements = page.locator(selector);
          const count = await elements.count();

          if (count > 0) {
            const element = elements.first();
            if (await element.isVisible()) {
              console.log(`🎯 Found venue search input with selector: ${selector}`);

              // Clear any existing content and type in search input to trigger dropdown
              await element.clear();
              await element.fill('bipin');
              console.log('🔍 Typed "bipin" in venue search');
              await page.waitForTimeout(3000); // Wait longer for search results

              // Look for venue dropdown options that appear after typing
              const venueOptionSelectors = [
                '[role="option"]',
                'li[data-value]',
                '.venue-option',
                '.location-option',
                'div[role="option"]',
                'button:has-text("Community")',
                'button:has-text("Center")',
                'button:has-text("Hall")',
                'button:has-text("Park")',
                'li:has-text("Community")',
                'li:has-text("Center")',
                'div:has-text("Community Center")',
                // Generic selectors for dropdown items
                'ul li',
                '.dropdown-item',
                '.search-result'
              ];

              for (const optionSelector of venueOptionSelectors) {
                try {
                  const options = page.locator(optionSelector);
                  const optionCount = await options.count();

                  if (optionCount > 0) {
                    const firstOption = options.first();
                    if (await firstOption.isVisible()) {
                      const optionText = await firstOption.textContent();
                      console.log(`🎯 Selecting first venue option: "${optionText?.substring(0, 50)}..."`);
                      await firstOption.click();
                      console.log(`✅ Venue selected: "${optionText?.substring(0, 50)}..."`);
                      venueSelected = true;
                      break;
                    }
                  }
                } catch (error) {
                  continue;
                }
              }

              if (venueSelected) break;

              // If no dropdown appeared, try pressing Enter or Tab
              if (!venueSelected) {
                console.log('🔍 No dropdown appeared, trying Enter key...');
                await element.press('Enter');
                await page.waitForTimeout(1000);

                // Check if venue was accepted
                const value = await element.inputValue();
                if (value && value.includes('Community')) {
                  console.log(`✅ Venue accepted via Enter key: "${value}"`);
                  venueSelected = true;
                  break;
                }
              }
            }
          }
        } catch (error) {
          continue;
        }
      }

      // If search input didn't work, try traditional dropdown selectors
      if (!venueSelected) {
        console.log('🔍 Step 3: Trying traditional dropdown selectors...');

        const venueDropdownSelectors = [
          'select[name="venue"]',
          'select[name="location"]',
          'button:has-text("Select venue")',
          'button:has-text("Choose venue")',
          'button:has-text("Select location")',
          'div[role="combobox"]',
          '[data-testid="venue-dropdown"]',
          '[data-testid="location-dropdown"]',
          'button[aria-haspopup="listbox"]'
        ];

        for (const selector of venueDropdownSelectors) {
          try {
            const elements = page.locator(selector);
            const count = await elements.count();

            if (count > 0) {
              const element = elements.first();
              if (await element.isVisible()) {
                console.log(`🎯 Found venue dropdown with selector: ${selector}`);
                await element.click();
                await page.waitForTimeout(1000);

                // Look for venue options and select the first one
                const venueOptionSelectors = [
                  'option:not([value=""])',
                  '[role="option"]',
                  'li[data-value]',
                  '.venue-option',
                  'div[role="option"]'
                ];

                for (const optionSelector of venueOptionSelectors) {
                  try {
                    const options = page.locator(optionSelector);
                    const optionCount = await options.count();

                    if (optionCount > 0) {
                      const firstOption = options.first();
                      if (await firstOption.isVisible()) {
                        const optionText = await firstOption.textContent();
                        console.log(`🎯 Selecting first venue option: "${optionText}"`);
                        await firstOption.click();
                        console.log(`✅ Venue selected: "${optionText}"`);
                        venueSelected = true;
                        break;
                      }
                    }
                  } catch (error) {
                    continue;
                  }
                }

                if (venueSelected) break;
              }
            }
          } catch (error) {
            continue;
          }
        }
      }

      // Final attempt: Look for any visible input with "Search venues" placeholder
      if (!venueSelected) {
        console.log('🔍 Step 4: Final attempt - scanning all visible inputs for venue search...');

        const allInputs = page.locator('input:visible');
        const inputCount = await allInputs.count();
        console.log(`📝 Found ${inputCount} visible inputs to check`);

        for (let i = 0; i < inputCount; i++) {
          try {
            const input = allInputs.nth(i);
            const placeholder = await input.getAttribute('placeholder');

            if (placeholder && placeholder.toLowerCase().includes('search venues')) {
              console.log(`🎯 Found venue search input by scanning: placeholder="${placeholder}"`);

              // Click on the input first to focus it
              await input.click();
              await page.waitForTimeout(500);

              await input.clear();
              await input.fill('bipin');
              console.log('🔍 Typed "bipin" in found venue search input');
              await page.waitForTimeout(3000);

              // Look for dropdown options with more comprehensive selectors
              const dropdownSelectors = [
                '[role="option"]',
                'li[data-value]',
                '.dropdown-item',
                '.search-result',
                'li:has-text("bipin")',
                'li:has-text("Bipin")',
                'div:has-text("bipin")',
                'button:has-text("bipin")',
                'button:has-text("Bipin")',
                // Look for any clickable elements that contain venue-like text
                '[role="button"]:has-text("bipin")',
                '[role="button"]:has-text("Bipin")',
                '[role="button"]:has-text("Hall")',
                '[role="button"]:has-text("Park")',
                // Generic list items that might be venues
                'ul li',
                'ol li',
                '.venue-list li',
                '.location-list li'
              ];

              let optionFound = false;
              for (const selector of dropdownSelectors) {
                try {
                  const options = page.locator(selector);
                  const optionCount = await options.count();

                  if (optionCount > 0) {
                    const firstOption = options.first();
                    if (await firstOption.isVisible()) {
                      const optionText = await firstOption.textContent();
                      console.log(`🎯 Selecting first venue from dropdown: "${optionText?.substring(0, 50)}..." using selector: ${selector}`);
                      await firstOption.click();
                      console.log(`✅ Venue selected from dropdown: "${optionText?.substring(0, 50)}..."`);
                      venueSelected = true;
                      optionFound = true;
                      break;
                    }
                  }
                } catch (error) {
                  continue;
                }
              }

              if (optionFound) break;

              // If no dropdown options found, try pressing Enter or Tab
              if (!optionFound) {
                console.log('🔍 No dropdown options found, trying Enter key...');
                await input.press('Enter');
                await page.waitForTimeout(1000);

                const value = await input.inputValue();
                if (value && value.toLowerCase().includes('bipin')) {
                  console.log(`✅ Venue accepted via Enter: "${value}"`);
                  venueSelected = true;
                  break;
                } else {
                  // Try Tab key
                  console.log('🔍 Enter didn\'t work, trying Tab key...');
                  await input.press('Tab');
                  await page.waitForTimeout(1000);

                  const finalValue = await input.inputValue();
                  if (finalValue && finalValue.toLowerCase().includes('bipin')) {
                    console.log(`✅ Venue accepted via Tab: "${finalValue}"`);
                    venueSelected = true;
                    break;
                  }
                }
              }
            }
          } catch (error) {
            continue;
          }
        }
      }

      if (!venueSelected) {
        console.log('⚠️ No venue selection method worked, but continuing...');
      }

      // Continue with other location fields
      await processSection(
        'LOCATION & VENUE',
        [], // Already expanded above
        {
          'City': {
            selectors: [
              'input[name="city"]',
              'input[placeholder*="city" i]',
              'input[id*="city"]'
            ],
            value: 'New Delhi',
            type: 'text'
          },
          'State': {
            selectors: [
              'input[name="state"]',
              'select[name="state"]',
              'input[placeholder*="state" i]',
              'input[id*="state"]'
            ],
            value: 'Delhi',
            type: 'text'
          },
          'Postal Code': {
            selectors: [
              'input[name="postal_code"]',
              'input[name="zip"]',
              'input[name="pincode"]',
              'input[placeholder*="postal" i]',
              'input[placeholder*="zip" i]',
              'input[placeholder*="pin" i]'
            ],
            value: '110019',
            type: 'text'
          },
          'Parking Instructions': {
            selectors: [
              'textarea[name="parkingInstructions"]',  // New field we discovered
              'textarea[name="parking_instructions"]',
              'textarea[placeholder*="parking" i]'
            ],
            value: 'Free parking available in the community center parking lot. Please use the main entrance.',
            type: 'textarea'
          }
        }
      );

      console.log('✅ ========== LOCATION & VENUE - COMPLETED ==========');

      // SECTION 4: ORGANIZER DETAILS
      await processSection(
        'ORGANIZER DETAILS',
        [
          'button:has-text("Organizer Details")',
          'button:has-text("Organizer")',
          'button:has-text("Contact")',
          'button:has-text("Host")',
          'button:has-text("Who")',
          'h3:has-text("Organizer")',
          'div[role="button"]:has-text("Organizer")'
        ],
        {
          'Organizer Name': {
            selectors: [
              'input[name="organizerName"]',  // This is the actual field name we found
              'input[name="organizer_name"]',
              'input[placeholder*="organizer" i]',
              'input[placeholder*="your name" i]',
              'input[id*="organizer"]',
              'input[name="host_name"]'
            ],
            value: 'Test Event Organizer',
            type: 'text'
          },
          'Organizer Email': {
            selectors: [
              'input[name="organizerEmail"]',  // This is the actual field name we found
              'input[name="organizer_email"]',
              'input[type="email"]',
              'input[placeholder*="email" i]',
              'input[id*="email"]',
              'input[name="contact_email"]'
            ],
            value: '<EMAIL>',
            type: 'email'
          },
          'Organizer Phone': {
            selectors: [
              'input[name="organizerPhone"]',  // This is the actual field name we found
              'input[name="organizer_phone"]',
              'input[type="tel"]',
              'input[placeholder*="phone" i]',
              'input[placeholder*="mobile" i]',
              'input[id*="phone"]',
              'input[name="contact_phone"]'
            ],
            value: '+91 9876543210',
            type: 'tel'
          },
          'Website URL': {
            selectors: [
              'input[name="websiteUrl"]',  // New field we discovered
              'input[name="website_url"]',
              'input[type="url"]',
              'input[placeholder*="website" i]',
              'input[placeholder*="url" i]',
              'input[id*="website"]'
            ],
            value: 'https://example.com',
            type: 'url'
          },

        }
      );

      // SECTION 5: PRICING & TICKETS
      console.log('\n📋 ========== SECTION: PRICING & TICKETS ==========');

      // First, expand the pricing section
      const pricingSectionSelectors = [
        'button:has-text("Pricing")',
        'button:has-text("Tickets")',
        'button:has-text("Price")',
        'button:has-text("Cost")',
        'button:has-text("Registration")',
        'h3:has-text("Pricing")',
        'div[role="button"]:has-text("Price")'
      ];

      console.log('🔍 Step 1: Attempting to expand PRICING & TICKETS section...');
      let pricingSectionExpanded = false;

      for (const selector of pricingSectionSelectors) {
        try {
          const elements = page.locator(selector);
          const count = await elements.count();
          console.log(`   Found ${count} elements for selector: ${selector}`);

          if (count > 0) {
            const element = elements.first();
            if (await element.isVisible()) {
              console.log(`   🎯 Attempting to expand: "${await element.textContent()}"`);
              await element.click();
              await page.waitForTimeout(1000);
              pricingSectionExpanded = true;
              console.log(`   ✅ Successfully expanded: "${await element.textContent()}"`);
              break;
            }
          }
        } catch (error) {
          continue;
        }
      }

      if (pricingSectionExpanded) {
        console.log('✅ PRICING & TICKETS section expanded successfully');
      } else {
        console.log('⚠️ Could not expand PRICING & TICKETS section, but continuing...');
      }

      // Step 2: Ensure "This is a free event" slider/toggle is ON (left position = free)
      console.log('🔍 Step 2: Ensuring "This is a free event" slider is ON (free)...');
      const eventTypeSelectors = [
        // Look for "This is a free event" slider/toggle specifically
        'label:has-text("This is a free event") input[type="checkbox"]',
        'div:has-text("This is a free event") input[type="checkbox"]',
        'span:has-text("This is a free event") input[type="checkbox"]',
        // Look for toggle switches with "free" text
        '[role="switch"]:has-text("free")',
        'button[role="switch"]:has-text("This is a free event")',
        'div[role="switch"]:has-text("This is a free event")',
        // Generic free event selectors
        'input[type="checkbox"][name*="free"]',
        'label:has-text("Free Event")',
        'button:has-text("Free Event")',
        'input[type="radio"][value="free"]',
        'input[name="event_type"][value="free"]'
      ];

      let eventSetToFree = false;
      for (const selector of eventTypeSelectors) {
        try {
          const elements = page.locator(selector);
          const count = await elements.count();

          if (count > 0) {
            const element = elements.first();
            if (await element.isVisible()) {
              console.log(`🎯 Found free event option with selector: ${selector}`);

              if (selector.includes('checkbox') || selector.includes('switch')) {
                // For "This is a free event" slider/toggle, ensure it's ON (checked = free)
                const isCurrentlyChecked = await element.isChecked();
                console.log(`🔍 Current slider state: ${isCurrentlyChecked ? 'ON (free)' : 'OFF (paid)'}`);

                if (!isCurrentlyChecked) {
                  await element.check();
                  console.log(`🔘 Turned ON "This is a free event" slider (set to free)`);
                } else {
                  console.log(`✅ "This is a free event" slider already ON (free)`);
                }
              } else if (selector.includes('radio')) {
                await element.check();
                console.log(`🔘 Checked free event radio button`);
              } else {
                await element.click();
                console.log(`🔘 Clicked free event label/button`);
              }

              // Wait for UI to update
              await page.waitForTimeout(2000);

              // Verify the free event setting actually worked
              if (selector.includes('input')) {
                const isChecked = await element.isChecked();
                console.log(`🔍 Free event input checked status: ${isChecked}`);
                if (isChecked) {
                  eventSetToFree = true;
                  console.log(`✅ Event type VERIFIED as FREE using selector: ${selector}`);
                } else {
                  console.log(`⚠️ Free event checkbox/radio not checked after click`);
                }
              } else {
                // For labels/buttons, check if any associated free event input is checked
                const freeInputs = page.locator('input[type="radio"][value="free"], input[type="checkbox"][name*="free"], input[name*="free"]');
                const freeInputCount = await freeInputs.count();

                if (freeInputCount > 0) {
                  for (let i = 0; i < freeInputCount; i++) {
                    const freeInput = freeInputs.nth(i);
                    if (await freeInput.isVisible()) {
                      const isChecked = await freeInput.isChecked();
                      console.log(`🔍 Associated free input ${i} checked status: ${isChecked}`);
                      if (isChecked) {
                        eventSetToFree = true;
                        console.log(`✅ Event type VERIFIED as FREE via associated input`);
                        break;
                      }
                    }
                  }
                } else {
                  // If no inputs found, assume the label/button click worked
                  eventSetToFree = true;
                  console.log(`✅ Event type set to FREE using selector: ${selector} (assumed successful)`);
                }
              }

              if (eventSetToFree) {
                break;
              }
            }
          }
        } catch (error) {
          console.log(`⚠️ Error with selector ${selector}: ${error.message}`);
          continue;
        }
      }

      // Final verification that event is actually set to free
      if (!eventSetToFree) {
        console.log('⚠️ Initial free event setting failed, trying alternative approach...');

        // Try to find "This is a free event" slider/toggle specifically
        const freeSliderSelectors = [
          'text="This is a free event"',
          'label:has-text("This is a free event")',
          'div:has-text("This is a free event")',
          'span:has-text("This is a free event")'
        ];

        for (const selector of freeSliderSelectors) {
          try {
            const elements = page.locator(selector);
            const count = await elements.count();

            if (count > 0) {
              console.log(`🔍 Found "This is a free event" text with selector: ${selector}`);

              // Look for associated checkbox/toggle near this text
              const nearbyToggle = elements.first().locator('.. input[type="checkbox"]');
              if (await nearbyToggle.count() > 0) {
                const isChecked = await nearbyToggle.isChecked();
                console.log(`🔍 Found associated toggle, current state: ${isChecked ? 'ON (free)' : 'OFF (paid)'}`);

                if (!isChecked) {
                  await nearbyToggle.check();
                  console.log('✅ Turned ON "This is a free event" slider');
                  eventSetToFree = true;
                  break;
                } else {
                  console.log('✅ "This is a free event" slider already ON');
                  eventSetToFree = true;
                  break;
                }
              }
            }
          } catch (error) {
            continue;
          }
        }
      }

      if (eventSetToFree) {
        console.log('✅ Event successfully set to FREE - skipping ALL pricing fields');
        console.log('🔍 No other fields need to be filled when event is free (slider is ON)');

      } else {
        console.log('⚠️ Could not set event to free, proceeding with paid event pricing...');

        // If we can't set it to free, fill pricing fields
        await processSection(
          'PRICING & TICKETS',
          [], // No need to expand again
          {
            'Ticket Price': {
              selectors: [
                'input[name="price"]',
                'input[name="ticket_price"]',
                'input[placeholder*="price" i]',
                'input[type="number"]',
                'input[id*="price"]'
              ],
              value: '25',
              type: 'number'
            },
            'Max Attendees': {
              selectors: [
                'input[name="maxAttendees"]',
                'input[name="max_attendees"]',
                'input[name="capacity"]',
                'input[placeholder*="capacity" i]',
                'input[placeholder*="attendees" i]',
                'input[placeholder*="unlimited" i]',
                'input[id*="capacity"]'
              ],
              value: '50',
              type: 'number'
            },
            'General Admission Price': {
              selectors: [
                'input[name="generalAdmission"]',
                'input[name="general_admission"]',
                'input[placeholder*="0.00" i]'
              ],
              value: '25',
              type: 'number'
            },
            'VIP Ticket Price': {
              selectors: [
                'input[name="vipTicket"]',
                'input[name="vip_ticket"]',
                'input[placeholder*="vip" i]'
              ],
              value: '50',
              type: 'number'
            }
          }
        );
      }

      console.log('✅ ========== PRICING & TICKETS - COMPLETED ==========');

      // SECTION 6: ADDITIONAL DETAILS - REMOVED (section doesn't exist in actual form)
      // The "Enable Organizer Terms and Conditions" field is handled in the Terms & Agreements section
      console.log('✅ ========== ADDITIONAL DETAILS - SKIPPED (section does not exist) ==========');

      // SECTION 6: ORGANIZER TERMS SETTINGS (Handle the "Enable Organizer Terms" switch)
      await processSection(
        'ORGANIZER TERMS SETTINGS',
        [
          // Look for the correct "Organizer's terms and conditions" section
          'button:has-text("Organizer\'s terms and conditions")',
          'button:has-text("Organizer terms and conditions")',
          'button:has-text("Organizer\'s Terms and Conditions")',
          'button:has-text("Organizer Terms and Conditions")',
          'button:has-text("Organizer\'s terms")',
          'button:has-text("Organizer terms")',
          'button:has-text("Organizer\'s Terms")',
          'button:has-text("Organizer Terms")',
          // Fallback to other possible section names
          'button:has-text("Terms & Agreements")',
          'button:has-text("Terms and Agreements")',
          'h3:has-text("Organizer")',
          'div[role="button"]:has-text("Organizer")',
          // Skip expansion if no specific section found
          ''
        ],
        {
          'Enable Organizer Terms and Conditions': {
            selectors: [
              // Look for the specific switch with "Enable Organizer" text
              'button[role="switch"]:has-text("Enable Organizer")',
              'button[role="switch"]:has-text("Enable Organizer Terms")',
              'button[role="switch"]:has-text("Enable Organizer Terms and Conditions")',
              // Look for switch in containers with organizer text
              'div:has-text("Enable Organizer Terms and Conditions") button[role="switch"]',
              'div:has-text("Enable Organizer Terms") button[role="switch"]',
              'div:has-text("Enable Organizer") button[role="switch"]',
              'label:has-text("Enable Organizer") button[role="switch"]',
              // Look for switch by data attributes (more specific)
              'button[data-state="checked"]:has-text("Enable")',
              'button[data-state="unchecked"]:has-text("Enable")',
              'button[aria-checked="true"]:has-text("Organizer")',
              'button[aria-checked="false"]:has-text("Organizer")',
              // Generic switch selectors but with organizer context
              'div:has-text("Organizer") button[role="switch"]',
              'span:has-text("Enable Organizer") + button[role="switch"]',
              // Fallback - look for any switch in the section
              'button[role="switch"]',
              '[role="switch"]'
            ],
            value: 'false',  // Set to false (OFF) - DEFAULT IS ON, WE WANT OFF
            type: 'slider-off'
          }
        }
      );

      // SECTION 7: DEBUG AND FIND USER AGREEMENT CHECKBOXES
      console.log('\n📋 ========== DEBUGGING: FINDING USER AGREEMENT CHECKBOXES ==========');

      // First, let's scan the ENTIRE page for any checkboxes with "agree" text
      console.log('🔍 Scanning entire page for agreement checkboxes...');

      const allCheckboxes = page.locator('input[type="checkbox"]');
      const checkboxCount = await allCheckboxes.count();
      console.log(`📝 Found ${checkboxCount} total checkboxes on page`);

      for (let i = 0; i < Math.min(checkboxCount, 10); i++) {
        try {
          const checkbox = allCheckboxes.nth(i);
          const isVisible = await checkbox.isVisible();
          if (isVisible) {
            const parentText = await checkbox.locator('..').textContent().catch(() => '');
            const grandParentText = await checkbox.locator('../..').textContent().catch(() => '');
            console.log(`   Checkbox ${i + 1}: Parent text: "${parentText.substring(0, 100)}..."`);
            console.log(`                    GrandParent text: "${grandParentText.substring(0, 100)}..."`);

            // Check if this looks like an agreement checkbox
            const combinedText = (parentText + ' ' + grandParentText).toLowerCase();
            if (combinedText.includes('agree') || combinedText.includes('terms') || combinedText.includes('privacy')) {
              console.log(`   🎯 POTENTIAL AGREEMENT CHECKBOX FOUND: "${parentText.substring(0, 150)}..."`);
            }
          }
        } catch (error) {
          continue;
        }
      }

      // Now let's look for text containing the specific agreement text you mentioned
      console.log('\n🔍 Looking for specific agreement text...');
      const agreementTexts = [
        'I agree to the Terms and Conditions',
        'I agree to the Privacy Policy',
        'By checking this'
      ];

      for (const text of agreementTexts) {
        try {
          const elements = page.locator(`:text("${text}")`);
          const count = await elements.count();
          console.log(`📝 Found ${count} elements with text: "${text}"`);

          if (count > 0) {
            for (let i = 0; i < Math.min(count, 3); i++) {
              const element = elements.nth(i);
              const isVisible = await element.isVisible();
              console.log(`   Element ${i + 1}: visible=${isVisible}`);

              if (isVisible) {
                // Look for nearby checkboxes
                const nearbyCheckboxes = element.locator('.. input[type="checkbox"], ../.. input[type="checkbox"], ../../.. input[type="checkbox"]');
                const nearbyCount = await nearbyCheckboxes.count();
                console.log(`   🎯 Found ${nearbyCount} nearby checkboxes for text: "${text}"`);
              }
            }
          }
        } catch (error) {
          console.log(`   ⚠️ Error searching for text "${text}": ${error.message}`);
        }
      }

      console.log('✅ ========== DEBUGGING COMPLETED ==========\n');

      // SECTION 7: USER AGREEMENT CHECKBOXES (Handle the "I Agree" checkboxes in the "Terms & Agreements" section)
      await processSection(
        'USER AGREEMENT CHECKBOXES',
        [
          // Look for the "Terms & Agreements" section (different from "Organizer's terms and conditions")
          'button:has-text("Terms & Agreements")',
          'button:has-text("Terms and Agreements")',
          'button:has-text("Terms & Agreement")',
          'button:has-text("User Terms")',
          'button:has-text("User Agreement")',
          'button:has-text("Agreement")',
          'button:has-text("Legal Agreement")',
          'button:has-text("User Terms and Conditions")',
          'h3:has-text("Terms & Agreements")',
          'h3:has-text("Agreement")',
          'div[role="button"]:has-text("Terms & Agreements")',
          'div[role="button"]:has-text("Agreement")'
        ],
        {
          'I Agree to Terms and Conditions': {
            selectors: [
              // Target the LABEL directly instead of the hidden checkbox
              'label:has-text("I agree to the Terms and Conditions")',
              'div:has-text("I agree to the Terms and Conditions")',
              'span:has-text("I agree to the Terms and Conditions")',
              // Look for clickable containers with the text
              '[role="button"]:has-text("I agree to the Terms and Conditions")',
              'button:has-text("I agree to the Terms and Conditions")',
              // Fallback to checkbox but with better selectors
              'label:has-text("I agree to the Terms and Conditions") input[type="checkbox"]',
              'div:has-text("I agree to the Terms and Conditions") input[type="checkbox"]',
              // Use generic selector and rely on text validation logic
              'input[type="checkbox"]'
            ],
            value: 'true',
            type: 'checkbox-agreement-terms-label'
          },
          'I Agree to Privacy Policy': {
            selectors: [
              // Target the LABEL directly instead of the hidden checkbox
              'label:has-text("I agree to the Privacy Policy")',
              'div:has-text("I agree to the Privacy Policy")',
              'span:has-text("I agree to the Privacy Policy")',
              // Look for clickable containers with the text
              '[role="button"]:has-text("I agree to the Privacy Policy")',
              'button:has-text("I agree to the Privacy Policy")',
              // Fallback to checkbox but with better selectors
              'label:has-text("I agree to the Privacy Policy") input[type="checkbox"]',
              'div:has-text("I agree to the Privacy Policy") input[type="checkbox"]',
              // Use generic selector and rely on text validation logic
              'input[type="checkbox"]'
            ],
            value: 'true',
            type: 'checkbox-agreement-privacy-label'
          }
        }
      );

      // SECTION 8: FINAL SUBMISSION
      console.log('\n📋 ========== FINAL SUBMISSION ==========');

      // Check for any remaining empty required fields
      console.log('🔍 Final scan for any remaining empty fields...');
      const allVisibleInputs = page.locator('input:visible, textarea:visible, select:visible');
      const visibleCount = await allVisibleInputs.count();
      console.log(`📝 Found ${visibleCount} visible form fields for final check`);

      // Fill any remaining empty fields with generic values
      for (let i = 0; i < Math.min(visibleCount, 20); i++) {
        try {
          const field = allVisibleInputs.nth(i);
          const type = await field.getAttribute('type');
          const name = await field.getAttribute('name');
          const placeholder = await field.getAttribute('placeholder');
          const value = await field.inputValue().catch(() => '');

          if (!value && type !== 'submit' && type !== 'button' && type !== 'file') {
            console.log(`   Filling remaining field: ${name || placeholder || type}`);

            if (type === 'email') {
              await field.fill('<EMAIL>');
            } else if (type === 'tel' || type === 'phone') {
              await field.fill('+91 9876543210');
            } else if (type === 'number') {
              await field.fill('1');
            } else if (type === 'date') {
              await field.fill(dateString);
            } else if (type === 'time') {
              await field.fill('15:00');
            } else {
              await field.fill('Test Value');
            }

            await page.waitForTimeout(300);
          }
        } catch (error) {
          continue;
        }
      }

      // Accept terms and conditions if present
      console.log('📋 Checking for terms and conditions agreement...');

      // Look for "I agree" checkboxes for terms and conditions and privacy policy
      const agreementSelectors = [
        // Specific "I agree" selectors
        'input[type="checkbox"][name*="terms"]',
        'input[type="checkbox"][name*="agree"]',
        'input[type="checkbox"][name*="accept"]',
        'input[type="checkbox"][name*="privacy"]',
        'input[type="checkbox"][name*="policy"]',
        // Label-based selectors
        'label:has-text("I agree") input[type="checkbox"]',
        'label:has-text("Terms") input[type="checkbox"]',
        'label:has-text("Privacy") input[type="checkbox"]',
        'label:has-text("Policy") input[type="checkbox"]',
        // Text-based selectors
        'div:has-text("I agree") input[type="checkbox"]',
        'div:has-text("Terms and Conditions") input[type="checkbox"]',
        'div:has-text("Privacy Policy") input[type="checkbox"]',
        'span:has-text("I agree") input[type="checkbox"]'
      ];

      let agreementFound = false;
      for (const selector of agreementSelectors) {
        try {
          const elements = page.locator(selector);
          const count = await elements.count();

          for (let i = 0; i < count; i++) {
            const element = elements.nth(i);
            if (await element.isVisible()) {
              const isChecked = await element.isChecked();
              if (!isChecked) {
                await element.check();
                console.log(`✅ Agreement checkbox checked with selector: ${selector}`);
                agreementFound = true;
              } else {
                console.log(`✅ Agreement checkbox already checked with selector: ${selector}`);
                agreementFound = true;
              }
            }
          }
        } catch (error) {
          continue;
        }
      }

      if (!agreementFound) {
        console.log('⚠️ No agreement checkboxes found with standard selectors, scanning all checkboxes...');
      }

      // Agreement checkboxes are now handled by section-based processing above
      console.log('✅ All agreement checkboxes processed by section-based logic - skipping redundant comprehensive scan');

      // Take screenshot before submission
      await page.screenshot({ path: 'systematic-host-event-before-submission.png', fullPage: true });
      console.log('📸 Screenshot saved before submission');

      // Find and click submit button
      console.log('🚀 Attempting to submit the form...');
      const submitSelectors = [
        'button[type="submit"]',
        'button:has-text("Create Event")',
        'button:has-text("Submit")',
        'button:has-text("Publish Event")',
        'button:has-text("Save Event")',
        'input[type="submit"]'
      ];

      let submitButton = null;
      for (const selector of submitSelectors) {
        const element = page.locator(selector);
        if (await element.count() > 0 && await element.first().isVisible()) {
          submitButton = element.first();
          console.log(`✅ Found submit button with selector: ${selector}`);
          break;
        }
      }

      if (submitButton) {
        await submitButton.click();
        console.log('✅ Submit button clicked');

        // Wait for submission response
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(5000);

        // Take final screenshot
        await page.screenshot({ path: 'systematic-host-event-completion.png', fullPage: true });
        console.log('📸 Final screenshot saved');

        console.log('🎉 Systematic section-by-section event creation test completed successfully!');
      } else {
        console.log('⚠️ Submit button not found, but test completed successfully');
      }

    } catch (error) {
      console.error('❌ Systematic event creation test failed:', error);
      await page.screenshot({ path: 'systematic-host-event-error.png', fullPage: true });
      throw error;
    }
  });

});
