import { test, expect } from '@playwright/test';
import {
  performLocationSetupOnly,
  signOut,
  authenticateWithGoogle,
  openSite,
  setLocationWithPinCode,
  selectChitranjanPark
} from './helpers/standard-setup';

/**
 * Comprehensive Automation Tests for TheLocalAdda
 *
 * These tests verify the complete user journey:
 * 1. Open site
 * 2. Enter PIN code (110019) and select Chittranjan Park
 * 3. Verify homepage loaded successfully (hero image, content)
 * 4. Authenticate with Google
 * 5. Verify successful authentication and return to homepage
 */

test.describe('Initial User Journey Setup Location + Authentication Tests', () => {

  test('should complete comprehensive flow: location setup → page verification → authentication', async ({ page }) => {
    // Increase timeout for this test since OAuth can take longer
    test.setTimeout(60000); // 60 seconds instead of default 30
    console.log('🚀 Starting comprehensive user journey test...');

    try {
      // Step 1: Open site and verify basic loading
      console.log('📱 Step 1: Opening site...');
      await openSite(page);

      // Step 2: Set location with PIN code
      console.log('📍 Step 2: Setting location with PIN code...');
      await setLocationWithPinCode(page, '110019');

      // Step 3: Select Chittranjan Park
      console.log('🏘️ Step 3: Selecting Chittranjan Park...');
      await selectChitranjanPark(page);

      // Step 4: Verify homepage loaded successfully
      console.log('✅ Step 4: Verifying homepage elements...');
      await verifyHomepageElements(page);

      // Step 5: Authenticate with Google
      console.log('🔐 Step 5: Starting Google authentication...');
      await authenticateWithGoogle(page);

      // Step 6: Authentication verification is already done in authenticateWithGoogle
      console.log('🎯 Step 6: Authentication verification completed in OAuth flow');

      // Step 7: Verify we're back on homepage with all elements (commented out for now)
      // console.log('🏠 Step 7: Final homepage verification...');
      // await verifyHomepageElements(page);

      console.log('🎉 Comprehensive test completed successfully!');

      // Clean up
      await signOut(page);

    } catch (error) {
      console.error('❌ Comprehensive test failed:', error);
      throw error;
    }
  });

  test('should verify homepage elements after location setup only', async ({ page }) => {
    console.log('🧪 Testing homepage verification without authentication...');

    try {
      // Complete location setup
      await performLocationSetupOnly(page);

      // Verify homepage elements
      await verifyHomepageElements(page);

      console.log('✅ Homepage verification test completed successfully');

    } catch (error) {
      console.error('❌ Homepage verification test failed:', error);
      throw error;
    }
  });



});

/**
 * Verify that homepage elements are loaded correctly
 */
async function verifyHomepageElements(page: any): Promise<void> {
  console.log('🔍 Verifying homepage elements...');

  // Verify we're on the homepage (allow for OAuth redirect hash)
  const currentUrl = page.url();
  const isHomepage = currentUrl === 'https://thelocaladda.com/' || currentUrl === 'https://thelocaladda.com/#';
  expect(isHomepage).toBe(true);

  // Verify location is set to Chittranjan Park
  await expect(page.locator('text="Chittaranjan Park"').first()).toBeVisible();
  console.log('✅ Location verified: Chittranjan Park');

  // Verify hero image is loaded
  const heroImage = page.locator('img[src="/hero-image.jpeg"]');
  await expect(heroImage).toBeVisible();
  console.log('✅ Hero image is visible');

  // Verify main heading is present
  const mainHeading = page.locator('h1:has-text("Find Local")');
  await expect(mainHeading).toBeVisible();
  console.log('✅ Main heading is visible');

  // Verify key text content
  await expect(page.locator('text="Events"')).toBeVisible();
  await expect(page.locator('text="Matter"')).toBeVisible();
  console.log('✅ Key heading text verified');

  // Verify descriptive text (using contains text match)
  const descriptionText = page.locator('p').filter({ hasText: 'Connect with families' });
  await expect(descriptionText).toBeVisible();
  console.log('✅ Description text verified');

  // Verify site branding
  const siteBranding = page.locator('a[href="/"]').first();
  await expect(siteBranding).toContainText('TheलोकलAdda');
  console.log('✅ Site branding verified');

  console.log('🎯 All homepage elements verified successfully');
}


