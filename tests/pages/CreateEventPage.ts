import { Page, Locator, expect } from '@playwright/test';
import { TestEventData, formSections, mandatoryFields, validationMessages } from '../helpers/test-data';

/**
 * Page Object Model for the Create Event Page
 * 
 * This class encapsulates all interactions with the event creation form,
 * making tests more maintainable and readable.
 */
export class CreateEventPage {
  readonly page: Page;
  
  // Form sections
  readonly basicInfoSection: Locator;
  readonly eventDetailsSection: Locator;
  readonly locationSection: Locator;
  readonly organizerSection: Locator;
  readonly ticketingSection: Locator;
  readonly organizerTermsSection: Locator;
  readonly termsSection: Locator;
  
  // Form fields
  readonly titleInput: Locator;
  readonly descriptionInput: Locator;
  readonly categorySelect: Locator;
  readonly tagsInput: Locator;
  readonly startDateInput: Locator;
  readonly startTimeInput: Locator;
  readonly endDateInput: Locator;
  readonly endTimeInput: Locator;
  readonly isMultiDayCheckbox: Locator;
  readonly maxAttendeesInput: Locator;
  readonly venueSelector: Locator;
  readonly organizerNameInput: Locator;
  readonly organizerEmailInput: Locator;
  readonly organizerPhoneInput: Locator;
  readonly websiteUrlInput: Locator;
  readonly isFreeCheckbox: Locator;
  readonly generalAdmissionInput: Locator;
  readonly vipTicketInput: Locator;
  readonly hasEarlyBirdCheckbox: Locator;
  readonly termsAgreedCheckbox: Locator;
  readonly privacyPolicyAgreedCheckbox: Locator;
  
  // Buttons and actions
  readonly submitButton: Locator;
  readonly cancelButton: Locator;
  
  // Error and success messages
  readonly errorSummary: Locator;
  readonly successMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Form sections
    this.basicInfoSection = page.locator(formSections.basicInfo);
    this.eventDetailsSection = page.locator(formSections.eventDetails);
    this.locationSection = page.locator(formSections.location);
    this.organizerSection = page.locator(formSections.organizer);
    this.ticketingSection = page.locator(formSections.ticketing);
    this.organizerTermsSection = page.locator(formSections.organizerTerms);
    this.termsSection = page.locator(formSections.terms);
    
    // Form fields
    this.titleInput = page.locator(mandatoryFields.title);
    this.descriptionInput = page.locator(mandatoryFields.description);
    this.categorySelect = page.locator(mandatoryFields.category);
    this.tagsInput = page.locator('input[name="tags"]');
    this.startDateInput = page.locator('input[name="startDate"]');
    this.startTimeInput = page.locator('input[name="startTime"]');
    this.endDateInput = page.locator('input[name="endDate"]');
    this.endTimeInput = page.locator('input[name="endTime"]');
    this.isMultiDayCheckbox = page.locator('input[name="isMultiDay"]');
    this.maxAttendeesInput = page.locator('input[name="maxAttendees"]');
    this.venueSelector = page.locator('input[name="venueId"]');
    this.organizerNameInput = page.locator(mandatoryFields.organizerName);
    this.organizerEmailInput = page.locator('input[name="organizerEmail"]');
    this.organizerPhoneInput = page.locator('input[name="organizerPhone"]');
    this.websiteUrlInput = page.locator('input[name="websiteUrl"]');
    this.isFreeCheckbox = page.locator('input[name="isFree"]');
    this.generalAdmissionInput = page.locator('input[name="generalAdmission"]');
    this.vipTicketInput = page.locator('input[name="vipTicket"]');
    this.hasEarlyBirdCheckbox = page.locator('input[name="hasEarlyBird"]');
    this.termsAgreedCheckbox = page.locator(mandatoryFields.termsAgreed);
    this.privacyPolicyAgreedCheckbox = page.locator(mandatoryFields.privacyPolicyAgreed);
    
    // Buttons
    this.submitButton = page.locator('button[type="submit"]');
    this.cancelButton = page.locator('button:has-text("Cancel")');
    
    // Messages
    this.errorSummary = page.locator('text=Please fix validation errors');
    this.successMessage = page.locator('text=Event created successfully');
  }

  /**
   * Navigate to the create event page
   */
  async goto() {
    await this.page.goto('/create');
  }

  /**
   * Check if user is authenticated (not redirected to auth page)
   */
  async isAuthenticated(): Promise<boolean> {
    return !this.page.url().includes('/auth');
  }

  /**
   * Open a specific form section
   */
  async openSection(sectionName: keyof typeof formSections) {
    const section = this.page.locator(formSections[sectionName]);
    await section.locator('button').click();
    await expect(section.locator('[data-state="open"]')).toBeVisible();
  }

  /**
   * Fill the Basic Info section
   */
  async fillBasicInfo(data: Partial<TestEventData>) {
    await this.openSection('basicInfo');
    
    if (data.title) {
      await this.titleInput.fill(data.title);
    }
    
    if (data.description) {
      await this.descriptionInput.fill(data.description);
    }
    
    if (data.category) {
      await this.categorySelect.click();
      await this.page.locator(`text=${data.category}`).click();
    }
    
    if (data.tags) {
      await this.tagsInput.fill(data.tags);
    }
  }

  /**
   * Fill the Event Details section
   */
  async fillEventDetails(data: Partial<TestEventData>) {
    await this.openSection('eventDetails');
    
    if (data.startDate) {
      // Date picker interaction might need special handling
      await this.startDateInput.fill(data.startDate);
    }
    
    if (data.startTime) {
      await this.startTimeInput.fill(data.startTime);
    }
    
    if (data.isMultiDay) {
      await this.isMultiDayCheckbox.check();
      
      if (data.endDate) {
        await this.endDateInput.fill(data.endDate);
      }
      
      if (data.endTime) {
        await this.endTimeInput.fill(data.endTime);
      }
    }
    
    if (data.maxAttendees) {
      await this.maxAttendeesInput.fill(data.maxAttendees);
    }
  }

  /**
   * Fill the Location section
   */
  async fillLocation(data: Partial<TestEventData>) {
    await this.openSection('location');
    
    // Venue selection would require interaction with VenueSelector component
    // This might involve searching for existing venues or creating new ones
    if (data.venueName) {
      // Implementation depends on VenueSelector component behavior
      await this.venueSelector.click();
      // Additional steps for venue selection/creation
    }
  }

  /**
   * Fill the Organizer section
   */
  async fillOrganizer(data: Partial<TestEventData>) {
    await this.openSection('organizer');
    
    if (data.organizerName) {
      await this.organizerNameInput.fill(data.organizerName);
    }
    
    if (data.organizerEmail) {
      await this.organizerEmailInput.fill(data.organizerEmail);
    }
    
    if (data.organizerPhone) {
      await this.organizerPhoneInput.fill(data.organizerPhone);
    }
    
    if (data.websiteUrl) {
      await this.websiteUrlInput.fill(data.websiteUrl);
    }
  }

  /**
   * Fill the Ticketing section
   */
  async fillTicketing(data: Partial<TestEventData>) {
    await this.openSection('ticketing');
    
    if (data.isFree !== undefined) {
      if (data.isFree) {
        await this.isFreeCheckbox.check();
      } else {
        await this.isFreeCheckbox.uncheck();
        
        if (data.generalAdmission) {
          await this.generalAdmissionInput.fill(data.generalAdmission);
        }
        
        if (data.vipTicket) {
          await this.vipTicketInput.fill(data.vipTicket);
        }
        
        if (data.hasEarlyBird) {
          await this.hasEarlyBirdCheckbox.check();
        }
      }
    }
  }

  /**
   * Accept terms and conditions
   */
  async acceptTerms() {
    await this.openSection('terms');
    await this.termsAgreedCheckbox.check();
    await this.privacyPolicyAgreedCheckbox.check();
  }

  /**
   * Submit the form
   */
  async submit() {
    await this.submitButton.click();
  }

  /**
   * Fill the entire form with valid data
   */
  async fillCompleteForm(data: TestEventData) {
    await this.fillBasicInfo(data);
    await this.fillEventDetails(data);
    await this.fillLocation(data);
    await this.fillOrganizer(data);
    await this.fillTicketing(data);
    await this.acceptTerms();
  }

  /**
   * Check for validation errors
   */
  async expectValidationError(message: string) {
    await expect(this.page.locator(`text=${message}`)).toBeVisible();
  }

  /**
   * Check for multiple validation errors
   */
  async expectValidationErrors(messages: string[]) {
    for (const message of messages) {
      await this.expectValidationError(message);
    }
  }

  /**
   * Check that error summary is displayed
   */
  async expectErrorSummary() {
    await expect(this.errorSummary).toBeVisible();
  }

  /**
   * Check for successful form submission
   */
  async expectSuccess() {
    await expect(this.successMessage).toBeVisible();
  }

  /**
   * Get all visible validation error messages
   */
  async getValidationErrors(): Promise<string[]> {
    const errorElements = await this.page.locator('[class*="error"], [role="alert"]').all();
    const errors: string[] = [];
    
    for (const element of errorElements) {
      const text = await element.textContent();
      if (text && text.trim()) {
        errors.push(text.trim());
      }
    }
    
    return errors;
  }
}
