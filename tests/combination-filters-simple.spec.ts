import { test, expect, Page } from '@playwright/test';
import { performLocationSetupOnly } from './helpers/standard-setup';
import { testSupabase } from './helpers/test-supabase-client';

test.describe('Combination Filters - Simple Test', () => {
  let page: Page;

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();

    // Navigate directly to discover events page
    console.log('🔍 Navigating directly to discover events page...');
    await page.goto('/events');
    await page.waitForLoadState('domcontentloaded');
    console.log('✅ Successfully navigated to /events page');

    // Wait a bit for the page to fully load
    await page.waitForTimeout(2000);
  });

  test.afterEach(async () => {
    await page.close();
  });

  test('should test boxing + sports + this week combination', async () => {
    console.log('🔄 Testing combination filters: boxing + sports + this week...');

    // 1. Apply search term "boxing"
    console.log('🥊 Applying search term: "boxing"...');
    const searchInput = page.locator('input[placeholder*="Search events"]');
    await searchInput.fill('boxing');
    
    // Trigger search
    const searchButton = page.locator('button:has-text("Search")').or(page.locator('button[type="submit"]'));
    const searchButtonExists = await searchButton.count() > 0;
    
    if (searchButtonExists) {
      await searchButton.click();
    } else {
      await searchInput.press('Enter');
    }
    
    await page.waitForTimeout(1000);
    console.log('✅ Applied search term: "boxing"');

    // 2. Apply Sports category filter
    console.log('🏃 Applying Sports category filter...');
    
    // Expand categories section
    const categoriesHeader = page.locator('h3:text("Categories")');
    await expect(categoriesHeader).toBeVisible();
    const categoriesButton = categoriesHeader.locator('..');
    await categoriesButton.click();
    await page.waitForTimeout(1000);

    // Find and click Sports category
    const sportsButton = page.locator('button:has-text("Sports")');
    await sportsButton.click();
    await page.waitForTimeout(1000);
    console.log('✅ Applied category filter: Sports');

    // 3. Apply This Week date filter
    console.log('📅 Applying This Week date filter...');
    
    // Expand dates section
    const datesHeader = page.locator('h3:text("Date")');
    await expect(datesHeader).toBeVisible();
    const datesButton = datesHeader.locator('..');
    await datesButton.click();
    await page.waitForTimeout(1000);

    // Click This Week with exact matching
    const thisWeekButton = page.locator('button').filter({ hasText: /^This Week$/ });
    await thisWeekButton.click();
    await page.waitForTimeout(2000);
    console.log('✅ Applied date filter: This Week');

    // 4. Validate results
    console.log('🔍 Validating combination filter results...');
    
    // Count events in UI
    const eventCards = page.locator('div.group.w-full.rounded-2xl[class*="shadow-smooth"]');
    const uiCount = await eventCards.count();
    console.log(`🖥️ UI shows ${uiCount} events with all filters applied`);

    // Check if we have the Boxing Match event
    if (uiCount > 0) {
      const eventTitles = await eventCards.locator('h3').allTextContents();
      console.log(`📋 Event titles found: ${eventTitles.join(', ')}`);
      
      // Look for boxing-related events
      const boxingEvents = eventTitles.filter(title => 
        title.toLowerCase().includes('boxing') || title.toLowerCase().includes('match')
      );
      
      if (boxingEvents.length > 0) {
        console.log(`✅ Found boxing-related events: ${boxingEvents.join(', ')}`);
      } else {
        console.log('⚠️ No boxing-related events found in results');
      }
    } else {
      console.log('📊 No events found with current filter combination');
    }

    // 5. Clear all filters
    console.log('🧹 Clearing all filters...');
    const clearAllButton = page.locator('button:has-text("Clear all")').or(page.locator('button:has-text("Clear All")'));
    const clearAllExists = await clearAllButton.count() > 0;
    
    if (clearAllExists) {
      await clearAllButton.click();
      await page.waitForTimeout(1000);
      console.log('✅ Cleared all filters using clear all button');
    } else {
      console.log('⚠️ Clear all button not found');
    }

    // Verify filters are cleared
    const finalCount = await eventCards.count();
    console.log(`📊 Final events count after clearing filters: ${finalCount}`);

    console.log('✅ Combination filters test completed');
  });
});
