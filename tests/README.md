# Event Creation Automation Tests

This directory contains Playwright automation tests for the TheLocalAdda event creation workflow, focusing on mandatory field validation and form functionality.

## Test Structure

### Main Test File
- `event-creation.spec.ts` - Core event creation workflow tests

### Helper Files
- `helpers/test-data.ts` - Test data, validation messages, and utility functions
- `pages/CreateEventPage.ts` - Page Object Model for the create event form

## Test Coverage

### 1. Authentication Requirements
- ✅ Redirects to auth page when not authenticated
- ✅ Preserves redirect parameter in auth URL

### 2. Form Structure and Navigation
- ✅ Displays all form sections when authenticated
- ✅ Allows navigation between form sections

### 3. Mandatory Field Validation
- ✅ Shows validation errors when submitting empty form
- ✅ Validates Basic Info section fields (title, description, category)
- ✅ Validates Terms section fields (terms agreement, privacy policy)

### Mandatory Fields Tested
1. **Basic Info Section**
   - Event title (minimum 5 characters)
   - Event description (minimum 20 characters)
   - Event category (required selection)

2. **Event Details Section**
   - Start date (required)
   - Start time (required)

3. **Location Section**
   - Venue selection (required)

4. **Organizer Section**
   - Organizer name (required)

5. **Terms Section**
   - Terms agreement (required checkbox)
   - Privacy policy agreement (required checkbox)

## Running the Tests

### Prerequisites
1. Make sure the development server is running:
   ```bash
   npm run dev
   ```

2. Install Playwright if not already installed:
   ```bash
   npx playwright install
   ```

### Run All Tests
```bash
npm run test:tests
```

### Run Specific Test File
```bash
npx playwright test tests/event-creation.spec.ts
```

### Run Tests in Headed Mode (with browser UI)
```bash
npx playwright test tests/event-creation.spec.ts --headed
```

### Run Tests in Debug Mode
```bash
npx playwright test tests/event-creation.spec.ts --debug
```

### View Test Report
```bash
npx playwright show-report
```

## Test Configuration

The tests are configured in `playwright.config.ts` with:
- **Base URL**: `http://localhost:5173` (Vite dev server)
- **Auto-start dev server**: Tests will automatically start the dev server
- **Browsers**: Chromium, Firefox, WebKit
- **Screenshots**: Captured on failure
- **Videos**: Recorded on failure
- **Traces**: Captured on retry

## Authentication Handling

Since the event creation page requires authentication, the tests handle this by:

1. **Checking for redirect**: Tests detect if the user is redirected to `/auth`
2. **Conditional skipping**: Tests skip execution if not authenticated
3. **Preserving redirect**: Verifies that the redirect parameter is maintained

### For Authenticated Testing
To test with authentication, you would need to:
1. Set up test user credentials
2. Implement login flow in test setup
3. Use Playwright's state management to persist authentication

## Form Validation Messages

The tests verify these specific validation messages:

- `"Title should be at least 5 characters long"`
- `"Description should be at least 20 characters long"`
- `"Please select a category for your event"`
- `"Please select the start date"`
- `"Please select a start time"`
- `"Please select or create a venue"`
- `"You must agree to the terms"`
- `"You must agree to the privacy policy"`

## Page Object Model

The `CreateEventPage` class provides methods for:
- Form section navigation
- Field interaction
- Validation error checking
- Form submission

## Future Enhancements

### Planned Test Additions
1. **Complete form submission workflow**
2. **Date and time picker interactions**
3. **Venue selection and creation**
4. **Category dropdown selection**
5. **File upload testing (event images)**
6. **Multi-day event configuration**
7. **Paid event ticketing validation**
8. **Form progress indicators**
9. **Error summary navigation**

### Authentication Integration
1. **Google OAuth flow testing**
2. **Session persistence**
3. **User role testing (admin vs regular user)**

### Data-Driven Testing
1. **Multiple event types**
2. **Boundary value testing**
3. **Cross-browser validation**

## Troubleshooting

### Common Issues

1. **Tests skip due to authentication**
   - Expected behavior for unauthenticated users
   - Implement authentication flow for complete testing

2. **Dev server not starting**
   - Check if port 5173 is available
   - Ensure `npm run dev` works manually

3. **Element not found errors**
   - Form structure may have changed
   - Update selectors in page object model

4. **Timeout errors**
   - Increase timeout in playwright.config.ts
   - Check if form loads slowly

### Debug Tips

1. **Use headed mode** to see browser interactions
2. **Add screenshots** with `await page.screenshot()`
3. **Use page.pause()** to inspect page state
4. **Check console logs** with `page.on('console', console.log)`

## Contributing

When adding new tests:
1. Follow the existing page object model pattern
2. Add test data to `helpers/test-data.ts`
3. Update this README with new test coverage
4. Ensure tests handle authentication gracefully
