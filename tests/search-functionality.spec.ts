import { test, expect } from '@playwright/test';
import { performLocationSetupOnly } from './helpers/standard-setup';
import { testSupabase } from './helpers/test-supabase-client';
import { SEARCH_TERMS } from './config/search-terms';

/**
 * Advanced Search Functionality Tests
 * 
 * These tests cover comprehensive search scenarios including:
 * - Category-specific searches
 * - Location-based searches  
 * - Date range searches
 * - Performance testing
 * - Search result interactions
 */

test.describe('Advanced Search Functionality', () => {

  // Test 1: Category-specific search
  test('should search within specific event categories', async ({ page }) => {
    console.log('🧪 Starting category-specific search test...');
    test.setTimeout(40000);
    
    try {
      await performLocationSetupOnly(page);
      
      // First, get available categories from database
      const { data: categories } = await testSupabase
        .from('event_categories')
        .select('id, name')
        .limit(3);
      
      if (!categories || categories.length === 0) {
        console.log('⚠️ No categories found in database, skipping category search test');
        return;
      }
      
      for (const category of categories) {
        console.log(`🔍 Testing category search: "${category.name}"`);
        
        // Navigate to events page
        await page.goto('/events');
        await page.waitForLoadState('networkidle');
        
        // Look for category filter (this might be a dropdown or button)
        const categoryFilters = page.locator(`text="${category.name}"`);
        const categoryFilterCount = await categoryFilters.count();
        
        if (categoryFilterCount > 0) {
          // Click on category filter
          await categoryFilters.first().click();
          await page.waitForTimeout(2000);
          await page.waitForLoadState('networkidle');
          
          // Query database for events in this category
          const { count: dbCount } = await testSupabase
            .from('events')
            .select('id', { count: 'exact' })
            .eq('category_id', category.id)
            .eq('approval_status', 'approved');
          
          // Count UI results
          const eventCards = page.locator('div.group.w-full.rounded-2xl[class*="shadow-smooth"]');
          const uiCount = await eventCards.count();
          
          console.log(`📊 Category "${category.name}": DB=${dbCount || 0}, UI=${uiCount}`);
          
          if ((dbCount || 0) > 0) {
            expect(uiCount).toBeGreaterThan(0);
            console.log(`✅ Category "${category.name}" shows results`);
          } else {
            console.log(`ℹ️ Category "${category.name}" has no events`);
          }
        } else {
          console.log(`⚠️ Category filter for "${category.name}" not found in UI`);
        }
      }
      
      console.log('🎉 Category-specific search test completed successfully');
    } catch (error) {
      console.error('❌ Category-specific search test failed:', error);
      throw error;
    }
  });

  // Test 2: Search result interaction
  test('should allow interaction with search results', async ({ page }) => {
    console.log('🧪 Starting search result interaction test...');
    test.setTimeout(30000);
    
    try {
      await performLocationSetupOnly(page);
      
      // Perform a search that should return results
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const searchInput = page.locator('input[type="search"], input[placeholder*="search" i], input[placeholder*="find" i]').first();
      await expect(searchInput).toBeVisible();
      
      // Search for a common term
      await searchInput.fill(SEARCH_TERMS.INTERACTION);
      await searchInput.press('Enter');
      
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Find event cards
      const eventCards = page.locator('div.group.w-full.rounded-2xl[class*="shadow-smooth"]');
      const cardCount = await eventCards.count();
      
      if (cardCount > 0) {
        console.log(`🔍 Found ${cardCount} event cards to test interaction`);
        
        // Test clicking on the first event card
        const firstCard = eventCards.first();
        const eventLink = firstCard.locator('a[href*="/events/"]');
        
        // Get the event ID from the href
        const href = await eventLink.getAttribute('href');
        console.log(`🔗 Testing click on event: ${href}`);
        
        // Click on the event card
        await eventLink.click();
        
        // Wait for navigation to event details page
        await page.waitForURL(/.*\/events\/[^\/]+$/);
        await page.waitForLoadState('networkidle');
        
        // Verify we're on an event details page
        const currentUrl = page.url();
        expect(currentUrl).toMatch(/\/events\/[^\/]+$/);
        console.log(`✅ Successfully navigated to event details: ${currentUrl}`);
        
        // Verify event details page has expected elements
        const eventTitle = page.locator('h1, h2').first();
        await expect(eventTitle).toBeVisible();
        console.log('✅ Event details page loaded with title');
        
        // Test back navigation
        await page.goBack();
        await page.waitForLoadState('networkidle');
        
        // Verify we're back to search results
        const backUrl = page.url();
        expect(backUrl).toContain(`search=${SEARCH_TERMS.INTERACTION}`);
        console.log('✅ Successfully navigated back to search results');
        
      } else {
        console.log('⚠️ No event cards found for interaction test');
      }
      
      console.log('🎉 Search result interaction test completed successfully');
    } catch (error) {
      console.error('❌ Search result interaction test failed:', error);
      throw error;
    }
  });

  // Test 3: Search performance and responsiveness
  test('should handle search operations efficiently', async ({ page }) => {
    console.log('🧪 Starting search performance test...');
    test.setTimeout(35000);
    
    try {
      await performLocationSetupOnly(page);
      
      const searchTerms = SEARCH_TERMS.PERFORMANCE;
      const performanceTimes: number[] = [];
      
      for (const searchTerm of searchTerms) {
        console.log(`⏱️ Testing search performance for: "${searchTerm}"`);
        
        await page.goto('/');
        await page.waitForLoadState('networkidle');
        
        const searchInput = page.locator('input[type="search"], input[placeholder*="search" i], input[placeholder*="find" i]').first();
        await expect(searchInput).toBeVisible();
        
        // Measure search time
        const startTime = Date.now();
        
        await searchInput.fill(searchTerm);
        await searchInput.press('Enter');
        
        // Wait for results to load
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(1000); // Allow for any additional loading
        
        const endTime = Date.now();
        const searchTime = endTime - startTime;
        performanceTimes.push(searchTime);
        
        console.log(`⏱️ Search for "${searchTerm}" took ${searchTime}ms`);
        
        // Verify search completed successfully (page loaded)
        const pageTitle = await page.title();
        expect(pageTitle).toContain('TheLocalAdda');
      }
      
      // Calculate average search time
      const averageTime = performanceTimes.reduce((a, b) => a + b, 0) / performanceTimes.length;
      console.log(`📊 Average search time: ${averageTime.toFixed(2)}ms`);
      
      // Performance assertion - searches should complete within reasonable time
      expect(averageTime).toBeLessThan(5000); // 5 seconds max average
      console.log('✅ Search performance is within acceptable limits');
      
      console.log('🎉 Search performance test completed successfully');
    } catch (error) {
      console.error('❌ Search performance test failed:', error);
      throw error;
    }
  });

  // Test 4: Search URL parameters and deep linking
  test('should handle search URL parameters correctly', async ({ page }) => {
    console.log('🧪 Starting search URL parameters test...');
    test.setTimeout(25000);
    
    try {
      await performLocationSetupOnly(page);
      
      const testCases = SEARCH_TERMS.URL_PARAMETERS;
      
      for (const testCase of testCases) {
        console.log(`🔗 Testing direct URL navigation: ${testCase.url}`);
        
        // Navigate directly to search URL
        await page.goto(testCase.url);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        // Verify the search input shows the expected term
        const searchInput = page.locator('input[type="search"], input[placeholder*="search" i], input[placeholder*="find" i]').first();
        
        if (await searchInput.count() > 0) {
          const inputValue = await searchInput.inputValue();
          console.log(`🔍 Search input value: "${inputValue}"`);
          console.log(`🎯 Expected value: "${testCase.expectedTerm}"`);
          
          // Note: URL decoding might affect exact matching, so we check if the core term is present
          const coreExpectedTerm = testCase.expectedTerm.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
          const coreInputValue = inputValue.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
          
          if (coreInputValue.includes(coreExpectedTerm) || coreExpectedTerm.includes(coreInputValue)) {
            console.log(`✅ Search input correctly populated for URL: ${testCase.url}`);
          } else {
            console.log(`⚠️ Search input value "${inputValue}" doesn't match expected "${testCase.expectedTerm}"`);
          }
        }
        
        // Verify URL contains search parameter
        const currentUrl = page.url();
        expect(currentUrl).toContain('search=');
        console.log(`✅ URL correctly contains search parameter: ${currentUrl}`);
      }
      
      console.log('🎉 Search URL parameters test completed successfully');
    } catch (error) {
      console.error('❌ Search URL parameters test failed:', error);
      throw error;
    }
  });

});
