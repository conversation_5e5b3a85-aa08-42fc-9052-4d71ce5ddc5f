# Event Creation Form Testing Guide

## Overview

This guide explains how to use the data-driven testing approach for the event creation form. The tests are designed to validate both successful form submissions and proper error handling for invalid inputs.

## Test Structure

### 1. **Data-Driven Test Scenarios**
- Located in: `tests/config/event-test-data.ts`
- Contains multiple test scenarios with different input combinations
- Each scenario specifies whether it should succeed or fail validation

### 2. **Main Test File**
- Located in: `tests/host-event.spec.ts`
- Contains parameterized tests that run all scenarios
- Includes helper functions for form filling and validation checking

## Test Scenarios

### Valid Scenarios (Should Pass)
- **Valid complete event data**: All required fields with valid data
- **Special characters in fields**: Tests handling of special characters
- **Minimum valid data**: Tests with minimal but valid input

### Invalid Scenarios (Should Show Validation Errors)
- **Missing required title**: Empty title field
- **Invalid email format**: Malformed email address
- **Missing required description**: Empty description field
- **Invalid phone number format**: Malformed phone number
- **Past date validation**: Event date in the past
- **Multiple validation errors**: Multiple fields with invalid data
- **Extremely long title**: Title exceeding character limits

## How to Run Tests

### Run All Scenarios
```bash
# Run all event creation test scenarios
npx playwright test host-event.spec.ts

# Run with specific browser
npx playwright test host-event.spec.ts --project=chromium

# Run with UI mode for debugging
npx playwright test host-event.spec.ts --ui
```

### Run Specific Scenarios
```bash
# Run only validation error scenarios
npx playwright test host-event.spec.ts -g "should handle event creation.*validation"

# Run only successful scenarios
npx playwright test host-event.spec.ts -g "should handle event creation.*Valid"

# Run single scenario for debugging
npx playwright test host-event.spec.ts -g "should test single validation scenario"
```

## Adding New Test Scenarios

### 1. Add to Configuration File
Edit `tests/config/event-test-data.ts` and add a new scenario:

```typescript
{
  scenario: "Your test scenario name",
  expectError: true, // or false for valid scenarios
  errorFields: ["fieldName1", "fieldName2"], // optional, for error scenarios
  data: {
    title: "Test Title",
    description: "Test Description",
    // ... other fields
  }
}
```

### 2. Field Options
Available fields in test data:
- `title`: Event title (string)
- `description`: Event description (string)
- `category`: Event category (string)
- `startDate`: Start date in YYYY-MM-DD format (string)
- `startTime`: Start time in HH:MM format (string)
- `venue`: Venue/location (string)
- `organizerName`: Organizer name (string)
- `organizerEmail`: Organizer email (string)
- `organizerPhone`: Organizer phone (string)
- `tags`: Event tags (string)
- `isFree`: Whether event is free (boolean)

### 3. Dynamic Dates
Use helper functions for dynamic dates:
```typescript
import { getFutureDate, getPastDate } from './config/event-test-data';

// 4 days from now
startDate: getFutureDate(4)

// 30 days ago (for testing past date validation)
startDate: getPastDate(30)
```

## Test Results

### Success Indicators
- Form submits successfully
- Redirected to success page or event details
- Success message appears
- No validation errors shown

### Error Indicators
- Validation error messages appear
- Form stays on create page (no redirect)
- Error styling applied to invalid fields
- Submit button doesn't process the form

## Debugging

### Screenshots
Tests automatically capture screenshots on failures:
- `scenario-error-{scenario-name}.png`: General test failures
- `validation-error-missing-{scenario-name}.png`: When expected errors don't appear
- `success-unclear-{scenario-name}.png`: When success is uncertain

### Console Logs
Tests provide detailed console output:
- Form filling progress
- Field detection and filling status
- Validation error detection
- Success/failure indicators

### Single Scenario Testing
Use the "should test single validation scenario" test for focused debugging:
1. Modify the test to use your specific scenario
2. Run with `--headed` flag to see browser actions
3. Add breakpoints or `page.pause()` for step-by-step debugging

## Best Practices

### 1. **Test Data Management**
- Keep test data in the configuration file
- Use descriptive scenario names
- Include both positive and negative test cases

### 2. **Error Validation**
- Always specify `expectError: true` for invalid scenarios
- List expected error fields in `errorFields` array
- Test edge cases and boundary conditions

### 3. **Maintenance**
- Update date values regularly or use dynamic date functions
- Keep email and phone formats realistic
- Test with actual venue names that exist in your system

### 4. **Performance**
- Run validation tests frequently during development
- Use single scenario tests for quick debugging
- Group related scenarios for organized test execution

## Troubleshooting

### Common Issues
1. **Authentication failures**: Ensure `performStandardSetup()` works correctly
2. **Form field not found**: Check if field selectors need updating
3. **Validation not triggered**: Verify form validation is properly implemented
4. **Date format issues**: Use YYYY-MM-DD format for dates

### Debug Steps
1. Run single scenario test with `--headed` flag
2. Check console logs for field detection issues
3. Verify form selectors match actual HTML elements
4. Test manually to confirm expected behavior
