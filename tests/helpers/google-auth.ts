import { Page } from '@playwright/test';
import { getGoogleTestCredentials, type TestCredentials } from '../config/test-credentials';

/**
 * Google OAuth Authentication Helper for Playwright Tests
 *
 * This helper handles the actual Google OAuth flow for testing.
 * Credentials are loaded securely from .env.test file.
 */

export interface GoogleTestCredentials {
  email: string;
  password: string;
}

/**
 * Performs Google OAuth sign-in flow
 * 
 * IMPORTANT: You need to set up test credentials for this to work:
 * 1. Create a test Google account
 * 2. Set environment variables or pass credentials
 * 3. Make sure the test account is authorized in your Google Cloud Console
 */
export async function signInWithGoogle(
  page: Page,
  credentials?: GoogleTestCredentials
): Promise<void> {
  // Use provided credentials or load from secure config
  let testEmail: string;
  let testPassword: string;

  if (credentials) {
    testEmail = credentials.email;
    testPassword = credentials.password;
  } else {
    const creds = getGoogleTestCredentials();
    testEmail = creds.email;
    testPassword = creds.password;
  }

  // Navigate to the auth page
  await page.goto('/auth');

  // Click the "Sign in with Google" button
  await page.locator('text=Sign in with Google').click();

  // Wait for Google OAuth popup or redirect
  // This might open in a new tab/window or redirect in the same page
  
  try {
    // Handle popup window (if Google opens in popup)
    const [popup] = await Promise.all([
      page.waitForEvent('popup', { timeout: 10000 }),
      // The click above should trigger the popup
    ]);

    if (popup) {
      await handleGoogleOAuthInPopup(popup, testEmail, testPassword);
      await popup.close();
    }
  } catch (error) {
    // If no popup, handle redirect in same page
    await handleGoogleOAuthInSamePage(page, testEmail, testPassword);
  }

  // Wait for redirect back to the app
  await page.waitForURL(/^(?!.*accounts\.google\.com).*/, { timeout: 30000 });
  
  // Wait for authentication to be processed
  await page.waitForLoadState('networkidle');
}

/**
 * Handle Google OAuth in a popup window
 */
async function handleGoogleOAuthInPopup(
  popup: Page, 
  email: string, 
  password: string
): Promise<void> {
  // Wait for Google sign-in page to load
  await popup.waitForLoadState('networkidle');

  // Enter email
  const emailInput = popup.locator('input[type="email"]');
  await emailInput.fill(email);
  await popup.locator('button:has-text("Next")').click();

  // Wait for password page
  await popup.waitForLoadState('networkidle');

  // Enter password
  const passwordInput = popup.locator('input[type="password"]');
  await passwordInput.fill(password);
  await popup.locator('button:has-text("Next")').click();

  // Handle any additional consent screens
  await handleGoogleConsentScreens(popup);
}

/**
 * Handle Google OAuth in the same page (redirect flow)
 */
async function handleGoogleOAuthInSamePage(
  page: Page, 
  email: string, 
  password: string
): Promise<void> {
  // Wait for Google sign-in page to load
  await page.waitForURL(/accounts\.google\.com/, { timeout: 10000 });
  await page.waitForLoadState('networkidle');

  // Enter email
  const emailInput = page.locator('input[type="email"]');
  await emailInput.fill(email);
  await page.locator('button:has-text("Next")').click();

  // Wait for password page
  await page.waitForLoadState('networkidle');

  // Enter password
  const passwordInput = page.locator('input[type="password"]');
  await passwordInput.fill(password);
  await page.locator('button:has-text("Next")').click();

  // Handle any additional consent screens
  await handleGoogleConsentScreens(page);
}

/**
 * Handle Google consent screens (permissions, etc.)
 */
async function handleGoogleConsentScreens(page: Page): Promise<void> {
  // Wait a bit for any consent screens to appear
  await page.waitForTimeout(2000);

  try {
    // Look for common consent buttons
    const continueButton = page.locator('button:has-text("Continue")');
    const allowButton = page.locator('button:has-text("Allow")');
    const acceptButton = page.locator('button:has-text("Accept")');

    if (await continueButton.isVisible({ timeout: 5000 })) {
      await continueButton.click();
    } else if (await allowButton.isVisible({ timeout: 5000 })) {
      await allowButton.click();
    } else if (await acceptButton.isVisible({ timeout: 5000 })) {
      await acceptButton.click();
    }
  } catch (error) {
    // Consent screens might not appear for returning users
    console.log('No consent screens found, proceeding...');
  }
}

/**
 * Sign out from the application
 */
export async function signOut(page: Page): Promise<void> {
  // Look for sign out button/link
  const signOutSelectors = [
    'text=Sign out',
    'text=Logout',
    'button:has-text("Sign out")',
    'a:has-text("Sign out")',
    '[data-testid="sign-out"]'
  ];

  for (const selector of signOutSelectors) {
    try {
      const element = page.locator(selector);
      if (await element.isVisible({ timeout: 2000 })) {
        await element.click();
        break;
      }
    } catch (error) {
      // Continue to next selector
    }
  }

  // Wait for sign out to complete
  await page.waitForLoadState('networkidle');
}

/**
 * Check if user is currently signed in
 */
export async function isSignedIn(page: Page): Promise<boolean> {
  // Look for indicators that user is signed in
  const signedInIndicators = [
    'text=Sign out',
    'text=Profile',
    'text=My Events',
    'a[href="/profile"]',
    'a[href="/my-events"]',
    '[data-testid="user-menu"]'
  ];

  for (const selector of signedInIndicators) {
    try {
      if (await page.locator(selector).isVisible({ timeout: 2000 })) {
        return true;
      }
    } catch (error) {
      // Continue checking
    }
  }

  return false;
}

/**
 * Wait for authentication state to be determined
 */
export async function waitForAuthState(page: Page, timeout: number = 10000): Promise<void> {
  await page.waitForFunction(
    () => {
      // Check for either signed in or signed out state
      const hasSignIn = document.querySelector('button') && 
                       Array.from(document.querySelectorAll('button')).some(btn => 
                         btn.textContent?.includes('Sign in with Google'));
      
      const hasSignOut = document.querySelector('button') && 
                        Array.from(document.querySelectorAll('button')).some(btn => 
                          btn.textContent?.includes('Sign out'));
      
      const hasProfile = document.querySelector('a[href="/profile"]') !== null;
      
      return hasSignIn || hasSignOut || hasProfile;
    },
    { timeout }
  );
}

/**
 * Set up authenticated test environment
 */
export async function setupAuthenticatedTest(
  page: Page, 
  credentials?: GoogleTestCredentials
): Promise<void> {
  // Check if already signed in
  if (await isSignedIn(page)) {
    return;
  }

  // Sign in with Google
  await signInWithGoogle(page, credentials);
  
  // Wait for auth state to be established
  await waitForAuthState(page);
  
  // Verify sign in was successful
  const signedIn = await isSignedIn(page);
  if (!signedIn) {
    throw new Error('Google sign-in failed - user does not appear to be authenticated');
  }
}
