import { Page } from '@playwright/test';

/**
 * Simple Authentication Helper for Playwright Tests
 * 
 * This bypasses the complex Google OAuth flow and directly mocks
 * the authentication state that Supabase expects.
 */

/**
 * Mock authentication by setting the required localStorage values
 * This simulates what happens after successful Google OAuth
 */
export async function mockAuthentication(page: Page): Promise<void> {
  // Set up the authentication state before navigating to any page
  await page.addInitScript(() => {
    // Mock Supabase auth session
    const mockSession = {
      access_token: 'mock-access-token-for-testing',
      refresh_token: 'mock-refresh-token-for-testing',
      expires_in: 3600,
      expires_at: Date.now() / 1000 + 3600,
      token_type: 'bearer',
      user: {
        id: 'test-user-123',
        aud: 'authenticated',
        role: 'authenticated',
        email: '<EMAIL>',
        email_confirmed_at: new Date().toISOString(),
        phone: '',
        confirmed_at: new Date().toISOString(),
        last_sign_in_at: new Date().toISOString(),
        app_metadata: {
          provider: 'google',
          providers: ['google']
        },
        user_metadata: {
          avatar_url: 'https://lh3.googleusercontent.com/a/default-user',
          email: '<EMAIL>',
          email_verified: true,
          full_name: 'Test User',
          iss: 'https://accounts.google.com',
          name: 'Test User',
          picture: 'https://lh3.googleusercontent.com/a/default-user',
          provider_id: '*********',
          sub: '*********'
        },
        identities: [
          {
            id: '*********',
            user_id: 'test-user-123',
            identity_data: {
              email: '<EMAIL>',
              email_verified: true,
              name: 'Test User',
              picture: 'https://lh3.googleusercontent.com/a/default-user',
              provider_id: '*********',
              sub: '*********'
            },
            provider: 'google',
            last_sign_in_at: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    };

    // Set the auth token in localStorage (Supabase format)
    const authKey = 'sb-teetxyvxjvbuztjmrvyt-auth-token';
    localStorage.setItem(authKey, JSON.stringify(mockSession));
    
    // Also set in the generic supabase format
    localStorage.setItem('supabase.auth.token', JSON.stringify(mockSession));
  });

  // Mock the profile API response
  await page.route('**/rest/v1/profiles*', async (route) => {
    const mockProfile = {
      id: 'test-user-123',
      username: 'testuser',
      full_name: 'Test User',
      avatar_url: 'https://lh3.googleusercontent.com/a/default-user',
      website: null,
      is_admin: false,
      location: null,
      location_updated_at: null,
      locality_short_name: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify([mockProfile]),
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, apikey'
      }
    });
  });

  // Mock other Supabase API calls that might be needed
  await page.route('**/auth/v1/user', async (route) => {
    const mockUser = {
      id: 'test-user-123',
      aud: 'authenticated',
      role: 'authenticated',
      email: '<EMAIL>',
      email_confirmed_at: new Date().toISOString(),
      app_metadata: {
        provider: 'google',
        providers: ['google']
      },
      user_metadata: {
        avatar_url: 'https://lh3.googleusercontent.com/a/default-user',
        email: '<EMAIL>',
        email_verified: true,
        full_name: 'Test User',
        name: 'Test User'
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(mockUser)
    });
  });
}

/**
 * Clear authentication state
 */
export async function clearAuthentication(page: Page): Promise<void> {
  await page.addInitScript(() => {
    // Clear all auth-related localStorage items
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('auth') || key.includes('supabase'))) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => localStorage.removeItem(key));
    
    // Also clear sessionStorage
    sessionStorage.clear();
  });
}

/**
 * Set up authenticated test environment
 */
export async function setupAuthenticatedTest(page: Page): Promise<void> {
  // Mock authentication before any navigation
  await mockAuthentication(page);
  
  // Navigate to home page first to establish auth state
  await page.goto('/');
  
  // Wait for the page to load and auth state to be processed
  await page.waitForLoadState('networkidle');
  
  // Give a moment for React to process the auth state
  await page.waitForTimeout(1000);
}

/**
 * Check if the page shows authenticated state
 */
export async function isAuthenticated(page: Page): Promise<boolean> {
  try {
    // Look for elements that indicate authentication
    const authIndicators = [
      'text=Sign out',
      'text=Profile',
      'text=My Events',
      'a[href="/profile"]',
      'a[href="/my-events"]'
    ];

    for (const indicator of authIndicators) {
      const element = page.locator(indicator);
      if (await element.isVisible({ timeout: 2000 })) {
        return true;
      }
    }

    // Check if we're NOT on the auth page
    const currentUrl = page.url();
    return !currentUrl.includes('/auth');
  } catch {
    return false;
  }
}

/**
 * Mock admin authentication
 */
export async function setupAdminTest(page: Page): Promise<void> {
  // Set up regular authentication first
  await mockAuthentication(page);
  
  // Override the profile response to include admin privileges
  await page.route('**/rest/v1/profiles*', async (route) => {
    const mockAdminProfile = {
      id: 'test-user-123',
      username: 'admin_user',
      full_name: 'Admin User',
      avatar_url: 'https://lh3.googleusercontent.com/a/default-user',
      website: null,
      is_admin: true, // This makes the user an admin
      location: null,
      location_updated_at: null,
      locality_short_name: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify([mockAdminProfile]),
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, apikey'
      }
    });
  });
  
  // Navigate to home page to establish auth state
  await page.goto('/');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(1000);
}
