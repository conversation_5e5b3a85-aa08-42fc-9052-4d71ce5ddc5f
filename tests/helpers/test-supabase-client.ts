import { createClient } from '@supabase/supabase-js';
import type { Database } from '../../src/integrations/supabase/types';

/**
 * Test-specific Supabase client that works in Node.js environment
 * Uses process.env instead of import.meta.env which is Vite-specific
 */

// Get environment variables from process.env (Node.js environment)
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || "https://teetxyvxjvbuztjmrvyt.supabase.co";
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRlZXR4eXZ4anZidXp0am1ydnl0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDI1Mjc3MjMsImV4cCI6MjA1ODEwMzcyM30.W3z8E17awhlZtsykwuof79ZQWCGK4bRdHFE09TdQbXg";

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  throw new Error('Missing Supabase environment variables. Please check your .env file.');
}

// Create and export the test Supabase client
export const testSupabase = createClient<Database>(SUPABASE_URL, SUPABASE_ANON_KEY);

// Log configuration for debugging (without exposing sensitive keys)
console.log('🔧 Test Supabase client configured:', {
  url: SUPABASE_URL,
  keyLength: SUPABASE_ANON_KEY.length,
  keyPrefix: SUPABASE_ANON_KEY.substring(0, 20) + '...'
});
