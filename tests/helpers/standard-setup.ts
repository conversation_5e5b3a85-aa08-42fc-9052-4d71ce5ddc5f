import { Page, expect } from '@playwright/test';

/**
 * Standard Test Setup Helper
 * 
 * This helper provides the standard steps that every test should follow:
 * 1. Open the site
 * 2. Enter PIN code (110019)
 * 3. Select Chittranjan Park
 * 4. Authenticate with Google using provided credentials
 */

export interface TestCredentials {
  email: string;
  password: string;
}

// Secure test credentials (loaded from environment or hardcoded for testing)
const TEST_CREDENTIALS: TestCredentials = {
  email: '<EMAIL>',
  password: 'Localadda12#'
};

/**
 * Step 1: Navigate to homepage and wait for load
 */
export async function openSite(page: Page): Promise<void> {
  console.log('🌐 Opening site...');
  await page.goto('/');
  await page.waitForLoadState('networkidle');
  
  // Verify site loaded correctly
  const siteLink = page.locator('a[href="/"]');
  await expect(siteLink.first()).toContainText('TheलोकलAdda');
  console.log('✅ Site opened successfully');
}

/**
 * Step 2: Set location using PIN code
 */
export async function setLocationWithPinCode(page: Page, pinCode: string = '110019'): Promise<void> {
  console.log(`📍 Setting location with PIN code: ${pinCode}...`);
  
  // Locate the PIN code input field
  const pinCodeInput = page.locator('input[placeholder*="Pincode"], input[name*="pincode"]');
  await pinCodeInput.fill(pinCode);
  
  // Click "Set Location" button
  const setLocationButton = page.locator('button[type="submit"]:has-text("Set Location")');
  await setLocationButton.click();
  
  // Verify the modal disappeared
  await expect(pinCodeInput).not.toBeVisible();
  console.log('✅ PIN code entered and location modal closed');
}

/**
 * Step 3: Select Chittranjan Park from location options
 */
export async function selectChitranjanPark(page: Page): Promise<void> {
  console.log('🏘️ Selecting Chittranjan Park...');

  // Locate and click the "Chittaranjan Park" option
  const chittaranjanParkOption = page.locator('text="Chittaranjan Park"');
  await chittaranjanParkOption.click();
  console.log('✅ Clicked on Chittranjan Park option');

  // Wait for location selection to process and homepage to load
  console.log('⏳ Waiting for location selection to complete and homepage to load...');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(5000); // Longer wait for homepage to fully load

  // Check what's actually on the page after location selection
  console.log('🔍 Checking page state after location selection...');
  const currentUrl = page.url();
  console.log(`📍 Current URL: ${currentUrl}`);

  // Check for various location indicators
  const locationIndicators = [
    'text="Chittaranjan Park"',
    'text="C.R.Park"',
    'text=/chittranjan park/i',
    'text=/c.r.park/i',
    'text="110019"'
  ];

  let locationDisplayed = false;
  for (const selector of locationIndicators) {
    const element = page.locator(selector);
    const count = await element.count();
    if (count > 0) {
      const text = await element.first().textContent();
      console.log(`✅ Found location indicator "${selector}": "${text}"`);
      locationDisplayed = true;
      break;
    }
  }

  if (!locationDisplayed) {
    console.log('⚠️ No location indicators found, checking page content...');

    // Debug: Check what's actually on the page
    const bodyText = await page.textContent('body');
    const locationKeywords = ['chittranjan', 'c.r.park', '110019', 'park'];

    for (const keyword of locationKeywords) {
      if (bodyText?.toLowerCase().includes(keyword.toLowerCase())) {
        console.log(`🔍 Found keyword "${keyword}" in page body`);
      }
    }
  }

  console.log('✅ Chittranjan Park selection completed');
}

/**
 * Step 4: Authenticate with Google using real credentials
 */
export async function authenticateWithGoogle(page: Page, credentials: TestCredentials = TEST_CREDENTIALS): Promise<void> {
  console.log('🔐 Starting Google authentication...');
  console.log(`📧 Using email: ${credentials.email}`);
  console.log('🌐 Current URL before auth:', page.url());

  try {
    // Navigate to auth page directly for more reliable flow
    console.log('🔄 Navigating to /auth page...');
    await page.goto('/auth');
    await page.waitForLoadState('networkidle');
    console.log('🔑 Successfully navigated to auth page');
    console.log('🌐 Current URL after navigation:', page.url());

    // Find and click the Google sign-in button
    const signInSelectors = [
      'text=Sign in with Google',
      'button:has-text("Sign in with Google")',
      'a:has-text("Sign in with Google")',
      '[data-provider="google"]',
      'button[type="button"]:has-text("Google")'
    ];

    let signInButton = null;
    for (const selector of signInSelectors) {
      signInButton = page.locator(selector);
      if (await signInButton.isVisible({ timeout: 3000 })) {
        console.log(`🔑 Found sign-in button with selector: ${selector}`);
        break;
      }
    }

    if (!signInButton || !await signInButton.isVisible()) {
      throw new Error('Could not find "Sign in with Google" button on auth page');
    }

    await signInButton.click();
    console.log('🔑 Clicked "Sign in with Google" button');

    // Wait for Google OAuth to start
    await page.waitForTimeout(3000);

    // Try to handle the authentication flow
    let authCompleted = false;

    try {
      // First, try popup approach
      console.log('🪟 Attempting popup authentication...');
      const popup = await page.waitForEvent('popup', { timeout: 8000 });

      if (popup) {
        console.log('🪟 Popup window opened');
        await completeGoogleAuth(popup, credentials);

        // Wait for popup to close
        await popup.waitForEvent('close', { timeout: 15000 });
        console.log('🪟 Popup closed');
        authCompleted = true;
      }
    } catch (popupError) {
      console.log('🔄 Popup approach failed, trying same-page approach...');

      // Check if we're now on Google's page
      if (page.url().includes('accounts.google.com')) {
        console.log('🔄 Redirected to Google, handling same-page auth...');
        await completeGoogleAuth(page, credentials);
        authCompleted = true;
      }
    }

    if (!authCompleted) {
      throw new Error('Could not complete Google authentication - no popup or redirect detected');
    }

    // Only proceed to verification if consent was completed successfully
    console.log('⏳ Waiting for redirect back to app after OAuth consent...');
    try {
      // Wait for redirect back to our app (not Google domain)
      await page.waitForURL(/^(?!.*accounts\.google\.com).*/, { timeout: 15000 });
      await page.waitForLoadState('networkidle');
      console.log('✅ Successfully redirected back to app after OAuth consent');
    } catch (redirectError) {
      console.log('⚠️ Redirect timeout after OAuth consent');

      // Check current state
      try {
        const currentUrl = page.url();
        console.log('🔍 Current URL after consent:', currentUrl);

        if (currentUrl.includes('google.com') || currentUrl.includes('google.co.in')) {
          throw new Error('Still on Google domain after OAuth consent - authentication may have failed');
        } else {
          console.log('✅ Not on Google domain, proceeding...');
        }
      } catch (urlError) {
        console.log('❌ Cannot access page URL:', urlError.message);
        throw new Error('Page became inaccessible after OAuth consent');
      }
    }

    // Give time for the app to process the authentication and show success popup
    console.log('⏳ Processing authentication state and waiting for success popup...');
    await page.waitForTimeout(3000);

    // Now verify authentication was successful - look for success popup first
    console.log('🔍 OAuth consent completed successfully, now verifying "Successfully signed in" popup...');
    await verifyAuthentication(page);
    console.log('✅ Google authentication completed successfully');

  } catch (error) {
    console.error('❌ Google authentication failed:', error);

    // Authentication failed

    throw error;
  }
}

/**
 * Complete Google authentication (works for both popup and same-page)
 */
async function completeGoogleAuth(page: Page, credentials: TestCredentials): Promise<void> {
  console.log('🔐 Completing Google authentication...');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);

  try {
    // Step 1: Handle email input
    console.log('📧 Step 1: Entering email...');
    await handleEmailInput(page, credentials.email);

    // Step 2: Handle password input
    console.log('🔒 Step 2: Entering password...');
    await handlePasswordInput(page, credentials.password);

    // Step 3: Handle consent/continue screens
    console.log('✅ Step 3: Handling consent screens...');
    await handleConsentScreens(page);

    console.log('🎉 Google authentication steps completed');

  } catch (error) {
    console.error('❌ Error in Google authentication:', error);
    throw error;
  }
}

/**
 * Handle email input step
 */
async function handleEmailInput(page: Page, email: string): Promise<void> {
  const emailSelectors = [
    'input[type="email"]',
    'input[id="identifierId"]',
    'input[name="identifier"]',
    'input[autocomplete="username"]',
    '#identifierId'
  ];

  let emailInput = null;
  for (const selector of emailSelectors) {
    emailInput = page.locator(selector);
    if (await emailInput.isVisible({ timeout: 5000 })) {
      console.log(`📧 Found email input: ${selector}`);
      break;
    }
  }

  if (!emailInput || !await emailInput.isVisible()) {
    throw new Error('Could not find email input field');
  }

  await emailInput.clear();
  await emailInput.fill(email);
  console.log(`📧 Filled email: ${email}`);

  // Click Next button for email
  await clickNextButton(page, 'email');
}

/**
 * Handle password input step
 */
async function handlePasswordInput(page: Page, password: string): Promise<void> {
  // Wait for password page to load
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);

  const passwordSelectors = [
    'input[type="password"]',
    'input[name="password"]',
    'input[name="Passwd"]',
    'input[autocomplete="current-password"]',
    '#password'
  ];

  let passwordInput = null;
  for (const selector of passwordSelectors) {
    passwordInput = page.locator(selector);
    if (await passwordInput.isVisible({ timeout: 5000 })) {
      console.log(`🔒 Found password input: ${selector}`);
      break;
    }
  }

  if (!passwordInput || !await passwordInput.isVisible()) {
    throw new Error('Could not find password input field');
  }

  await passwordInput.clear();
  await passwordInput.fill(password);
  console.log('🔒 Filled password');

  // Click Next button for password
  await clickNextButton(page, 'password');
}

/**
 * Click Next button (works for both email and password steps)
 */
async function clickNextButton(page: Page, step: string): Promise<void> {
  const nextSelectors = [
    'button:has-text("Next")',
    'button[id="identifierNext"]',
    'button[id="passwordNext"]',
    'input[type="submit"]',
    'button[type="submit"]',
    'button:has-text("Continue")'
  ];

  let clicked = false;
  for (const selector of nextSelectors) {
    const button = page.locator(selector);
    if (await button.isVisible({ timeout: 3000 }) && await button.isEnabled()) {
      await button.click();
      console.log(`🔘 Clicked Next button for ${step}: ${selector}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      clicked = true;
      break;
    }
  }

  if (!clicked) {
    console.log(`⚠️ Could not find Next button for ${step}, trying Enter key...`);
    // Try pressing Enter on the current focused element
    await page.keyboard.press('Enter');
    await page.waitForTimeout(2000);
  }
}

/**
 * Handle Google consent screens - Targeted approach for OAuth consent page
 */
async function handleConsentScreens(page: Page): Promise<void> {
  console.log('🔐 Checking for consent screens...');

  try {
    // Wait for page to stabilize
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);

    console.log('🔍 Current URL:', page.url());

    // Check if we're on any OAuth consent page (including /signin/oauth/id and /signin/oauth/consent)
    const currentUrl = page.url();
    const isOAuthConsentPage = currentUrl.includes('accounts.google.com/signin/oauth') ||
                              currentUrl.includes('accounts.google.co.in/signin/oauth');

    if (isOAuthConsentPage) {
      console.log('🔍 On Google OAuth consent page, looking for consent buttons...');

      // Specific selectors for OAuth consent page
      const oauthConsentSelectors = [
        // Common OAuth consent buttons
        'button[type="button"]:has-text("Continue")',
        'button[type="button"]:has-text("Allow")',
        'button[type="button"]:has-text("Accept")',
        'button[type="button"]:has-text("Confirm")',
        'button[type="button"]:has-text("Grant")',
        'button[type="button"]:has-text("Authorize")',

        // Generic submit buttons
        'button[type="submit"]',
        'input[type="submit"]',

        // ID-based selectors for Google OAuth
        'button[id*="submit"]',
        'button[id*="approve"]',
        'button[id*="continue"]',
        'button[id*="allow"]',

        // Data attribute selectors
        'button[data-action="allow"]',
        'button[data-action="continue"]',

        // Generic button with specific text patterns
        'button:has-text("Continue")',
        'button:has-text("Allow")',
        'button:has-text("Accept")',

        // Fallback - any visible button
        'button:visible'
      ];

      let clickedConsent = false;

      // Try each selector with more patience
      for (const selector of oauthConsentSelectors) {
        console.log(`🔍 Trying OAuth selector: ${selector}`);

        try {
          const buttons = page.locator(selector);
          const count = await buttons.count();

          if (count > 0) {
            console.log(`✅ Found ${count} button(s) with selector: ${selector}`);

            // Try clicking the first visible and enabled button
            for (let i = 0; i < count; i++) {
              const button = buttons.nth(i);

              if (await button.isVisible({ timeout: 500 }) && await button.isEnabled({ timeout: 500 })) {
                console.log(`🎯 Clicking button ${i + 1} with selector: ${selector}`);

                // Click the button
                await button.click();
                console.log(`✅ Successfully clicked OAuth consent button`);

                // Wait for response
                await page.waitForTimeout(2000);

                clickedConsent = true;
                break;
              }
            }

            if (clickedConsent) break;
          }
        } catch (error) {
          console.log(`❌ Error with selector ${selector}: ${error.message}`);
        }
      }

      if (!clickedConsent) {
        console.log('⚠️ Could not find clickable consent button on OAuth page');

        // Try to find any clickable elements for debugging
        const allButtons = await page.locator('button').count();
        const allInputs = await page.locator('input').count();
        console.log(`🔍 Found ${allButtons} buttons and ${allInputs} inputs on page`);

        // Don't throw error - sometimes the page auto-proceeds
        console.log('⚠️ Continuing without clicking consent button...');
      } else {
        console.log('✅ OAuth consent button clicked successfully');
      }

    } else if (page.url().includes('google.com') || page.url().includes('google.co.in')) {
      console.log('🔍 On other Google page, waiting for OAuth consent page...');

      // Wait longer for potential navigation to OAuth consent page
      await page.waitForTimeout(5000);

      // Check if we've navigated to OAuth consent page
      const updatedUrl = page.url();
      const isNowOAuthPage = updatedUrl.includes('accounts.google.com/signin/oauth') ||
                            updatedUrl.includes('accounts.google.co.in/signin/oauth');

      if (isNowOAuthPage) {
        console.log('🔍 Navigated to OAuth consent page, handling consent...');

        // Use the same OAuth consent selectors as above
        const oauthConsentSelectors = [
          'button[type="button"]:has-text("Continue")',
          'button[type="button"]:has-text("Allow")',
          'button[type="button"]:has-text("Accept")',
          'button[type="button"]:has-text("Confirm")',
          'button[type="submit"]',
          'input[type="submit"]',
          'button:has-text("Continue")',
          'button:has-text("Allow")',
          'button:has-text("Accept")',
          'button:visible'
        ];

        let clickedOAuthConsent = false;

        for (const selector of oauthConsentSelectors) {
          console.log(`🔍 Trying OAuth selector: ${selector}`);

          try {
            const buttons = page.locator(selector);
            const count = await buttons.count();

            if (count > 0) {
              console.log(`✅ Found ${count} button(s) with selector: ${selector}`);

              for (let i = 0; i < count; i++) {
                const button = buttons.nth(i);

                if (await button.isVisible({ timeout: 500 }) && await button.isEnabled({ timeout: 500 })) {
                  console.log(`🎯 Clicking button ${i + 1} with selector: ${selector}`);
                  await button.click();
                  console.log(`✅ Successfully clicked OAuth consent button`);
                  await page.waitForTimeout(2000);
                  clickedOAuthConsent = true;
                  break;
                }
              }

              if (clickedOAuthConsent) break;
            }
          } catch (error) {
            console.log(`❌ Error with selector ${selector}: ${error.message}`);
          }
        }

        if (!clickedOAuthConsent) {
          console.log('⚠️ Could not find clickable consent button on OAuth page');
        } else {
          console.log('✅ OAuth consent button clicked successfully');
        }

      } else {
        console.log('🔍 Still on other Google page, checking for general consent buttons...');

        // General consent handling for other Google pages
        const generalSelectors = [
          'button:has-text("Continue")',
          'button:has-text("Allow")',
          'button:has-text("Accept")'
        ];

        for (const selector of generalSelectors) {
          try {
            const button = page.locator(selector);
            if (await button.isVisible({ timeout: 1000 })) {
              await button.click();
              console.log(`✅ Clicked general consent button: ${selector}`);
              await page.waitForTimeout(1000);
              break;
            }
          } catch {
            // Continue to next selector
          }
        }
      }
    } else {
      console.log('✅ Not on Google domain, consent may have been handled automatically');
    }

  } catch (error) {
    console.log('ℹ️ Consent screen handling completed with issues:', error.message);
    // Don't throw - consent screens are often optional or auto-handled
  }
}

/**
 * Verify that authentication was successful by looking for success popup
 */
async function verifyAuthentication(page: Page): Promise<void> {
  console.log('🔍 Verifying authentication...');

  // First, look for the "Successfully signed in" popup at bottom right
  console.log('🔍 Looking for "Successfully signed in" popup...');

  const successPopupSelectors = [
    'text="Successfully signed in"',
    'text="Successfully logged in"',
    'text="Sign in successful"',
    'text="Login successful"',
    '[role="alert"]:has-text("Success")',
    '[class*="toast"]:has-text("Success")',
    '[class*="notification"]:has-text("Success")',
    '[class*="alert"]:has-text("Success")',
    '[data-testid*="success"]',
    '[data-testid*="toast"]'
  ];

  let foundSuccessPopup = false;
  let foundPopupSelector = '';

  // Wait for the success popup to appear (should appear after OAuth consent is complete)
  console.log('⏳ Waiting for "Successfully signed in" popup to appear...');
  await page.waitForTimeout(5000); // Longer wait since this is after OAuth consent

  for (const selector of successPopupSelectors) {
    try {
      console.log(`🔍 Checking for popup: ${selector}`);
      if (await page.locator(selector).isVisible({ timeout: 2000 })) {
        foundSuccessPopup = true;
        foundPopupSelector = selector;
        console.log(`✅ Found success popup: ${selector}`);
        break;
      }
    } catch {
      // Continue checking
    }
  }

  if (foundSuccessPopup) {
    console.log(`🎉 Authentication verified with success popup: ${foundPopupSelector}`);

    // Wait a bit more for the popup to disappear and UI to settle
    await page.waitForTimeout(2000);
    return;
  }

  // Fallback: Look for traditional authentication indicators
  console.log('⚠️ Success popup not found, checking traditional auth indicators...');

  const authIndicators = [
    'text=Sign out',
    'text=Profile',
    'text=My Events',
    'a[href="/profile"]',
    'a[href="/my-events"]',
    'button:has-text("Sign out")',
    '[data-testid="user-menu"]'
  ];

  let isAuthenticated = false;
  let foundIndicator = '';

  for (const indicator of authIndicators) {
    try {
      if (await page.locator(indicator).isVisible({ timeout: 3000 })) {
        isAuthenticated = true;
        foundIndicator = indicator;
        console.log(`✅ Found authentication indicator: ${indicator}`);
        break;
      }
    } catch {
      // Continue checking
    }
  }

  if (!isAuthenticated) {
    console.log('❌ Authentication verification failed - no success popup or auth indicators found');
    console.log('🔍 Current URL:', page.url());

    // Authentication verification failed

    // Check if we're still on Google or back on our site
    const currentUrl = page.url();
    if (currentUrl.includes('google.com') || currentUrl.includes('google.co.in')) {
      // If we're still on Google, try to handle any remaining consent screens
      console.log('🔍 Still on Google page, attempting to handle remaining consent screens...');

      try {
        await handleConsentScreens(page);

        // Wait a bit more for redirect
        await page.waitForTimeout(5000);

        // Check again if we're back on our site
        const newUrl = page.url();
        if (!newUrl.includes('google.com') && !newUrl.includes('google.co.in')) {
          console.log('✅ Successfully redirected back to app after additional consent handling');
          // Try authentication verification again
          await verifyAuthentication(page);
          return;
        }
      } catch (consentError) {
        console.log('⚠️ Additional consent handling failed:', consentError.message);
      }

      throw new Error('Still on Google authentication page - authentication may not have completed');
    } else {
      throw new Error('Authentication verification failed - no success popup or authentication indicators found');
    }
  }

  console.log(`✅ Authentication verified with fallback indicator: ${foundIndicator}`);
}

/**
 * Complete standard setup: All steps combined
 * This is the main function that every test should call
 */
export async function performStandardSetup(page: Page): Promise<void> {
  console.log('🚀 Starting standard test setup...');

  // Step 1: Open site
  await openSite(page);

  // Step 2: Set location with PIN code
  await setLocationWithPinCode(page);

  // Step 3: Select Chittranjan Park
  await selectChitranjanPark(page);

  console.log('✅ Location setup completed, now starting authentication...');

  // Step 4: Authenticate with Google
  try {
    await authenticateWithGoogle(page);
    console.log('✅ Authentication completed successfully');
  } catch (error) {
    console.error('❌ Authentication failed:', error);
    throw error;
  }

  console.log('🎉 Standard setup completed successfully!');
}

/**
 * Check if location is already set AND we're on the actual homepage (to avoid double location popup)
 */
export async function isLocationAlreadySet(page: Page): Promise<boolean> {
  try {
    const currentUrl = page.url();
    console.log(`🔍 Checking location status on: ${currentUrl}`);

    // First check: Are we on the homepage (using same indicators as working test)
    const homepageIndicators = [
      'text="Discover Events"',
      'text="Find Local"',
      'img[src="/hero-image.jpeg"]',
      'text="Events"',
      'text="Matter"',
      'input[placeholder*="Search for events" i]',
      'input[placeholder*="Find events" i]',
      'input[placeholder*="Discover events" i]',
      'input[placeholder*="Search" i]:not([placeholder*="pin" i]):not([placeholder*="code" i])'
    ];

    let onHomepage = false;
    for (const selector of homepageIndicators) {
      const element = page.locator(selector);
      if (await element.count() > 0) {
        console.log(`✅ Found homepage indicator: ${selector}`);
        onHomepage = true;
        break;
      }
    }

    // Second check: Is location actually set?
    const locationIndicators = [
      'text=/chittranjan park/i',
      'text=/c.r.park/i',
      'text=/110019/i'
    ];

    let locationSet = false;
    for (const selector of locationIndicators) {
      const element = page.locator(selector);
      if (await element.count() > 0 && await element.first().isVisible()) {
        console.log(`✅ Found location indicator: ${selector}`);
        locationSet = true;
        break;
      }
    }

    // Third check: No PIN code input (means we're past location setup)
    const pinCodeInput = page.locator('input[placeholder*="pin" i], input[placeholder*="code" i]');
    const hasPinInput = await pinCodeInput.count() > 0;

    if (onHomepage && locationSet && !hasPinInput) {
      console.log('✅ Location fully set and on homepage');
      return true;
    } else {
      console.log(`ℹ️ Location setup needed - Homepage: ${onHomepage}, Location: ${locationSet}, HasPinInput: ${hasPinInput}`);
      return false;
    }

  } catch (error) {
    console.log('⚠️ Could not determine location status, assuming setup needed');
    return false;
  }
}

/**
 * Quick setup for tests that don't need authentication
 * Now checks if location is already set to avoid double popup
 */
export async function performLocationSetupOnly(page: Page): Promise<void> {
  console.log('🚀 Starting location-only setup...');

  await openSite(page);

  // Check if location is already set
  if (await isLocationAlreadySet(page)) {
    console.log('✅ Location already configured, skipping setup');
    return;
  }

  await setLocationWithPinCode(page);
  await selectChitranjanPark(page);

  console.log('📍 Location setup completed!');
}

/**
 * Smart navigation that avoids triggering LocationGate if location is already set
 */
export async function navigateWithoutLocationPopup(page: Page, url: string): Promise<void> {
  console.log(`🧭 Smart navigation to: ${url}`);

  // Navigate to the URL
  await page.goto(url);
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(1000);

  // Check if LocationGate appeared and handle it
  const locationGate = page.locator('[class*="backdrop-blur"], [class*="modal"], [class*="dialog"]');
  const hasModal = await locationGate.count() > 0;

  if (hasModal) {
    console.log('⚠️ LocationGate appeared after navigation, handling it...');

    // Check if location is already set in the modal
    if (await isLocationAlreadySet(page)) {
      console.log('✅ Location already set, closing modal');

      // Try to close the modal by clicking outside or finding close button
      const closeButton = page.locator('button:has-text("×"), button[aria-label*="close" i], [data-dismiss="modal"]');
      if (await closeButton.count() > 0) {
        await closeButton.first().click();
        console.log('✅ Closed LocationGate modal');
      } else {
        // Click outside the modal
        await page.click('body', { position: { x: 10, y: 10 } });
        console.log('✅ Clicked outside to close modal');
      }
    } else {
      console.log('ℹ️ Location not set, need to perform setup');
      await setLocationWithPinCode(page);
      await selectChitranjanPark(page);
    }
  } else {
    console.log('✅ No LocationGate appeared, navigation successful');
  }

  await page.waitForTimeout(1000);
}

/**
 * Find search input with multiple fallback strategies
 * Handles both homepage Hero search and Events page SearchBar
 */
export async function findSearchInput(page: Page): Promise<any> {
  console.log('🔍 Looking for search input with multiple strategies...');

  // Check if page is still valid
  try {
    const pageTitle = await page.title();
    console.log(`📄 Current page: ${pageTitle} at ${page.url()}`);
  } catch (error) {
    throw new Error(`Page context is invalid: ${error.message}`);
  }

  // Wait for page to be fully loaded
  try {
    await page.waitForLoadState('networkidle');
  } catch (error) {
    console.log('⚠️ NetworkIdle wait failed:', error.message);
    // Continue anyway, page might still be usable
  }

  // If we're on homepage, wait for LocationGate to close and Hero to render
  const currentUrl = page.url();
  if (currentUrl === 'https://thelocaladda.com/' || currentUrl.endsWith('/')) {
    console.log('🏠 On homepage, waiting for LocationGate to close and Hero to render...');

    // Wait for location gate to disappear (it has a backdrop-blur class)
    try {
      await page.waitForSelector('.backdrop-blur-md', { state: 'detached', timeout: 10000 });
      console.log('✅ LocationGate closed');
    } catch (error) {
      console.log('⚠️ LocationGate timeout, continuing anyway...');
    }

    // Wait for Hero component to render (it has specific classes)
    try {
      await page.waitForSelector('section', { timeout: 5000 });
      console.log('✅ Hero section found');
    } catch (error) {
      console.log('⚠️ Hero section timeout, continuing anyway...');
    }

    // Extra wait for animations to complete (with error handling)
    try {
      await page.waitForTimeout(3000);
    } catch (error) {
      console.log('⚠️ Page timeout failed, page may be closed:', error.message);
      throw new Error('Page context was closed during search input detection');
    }
  } else {
    try {
      await page.waitForTimeout(2000);
    } catch (error) {
      console.log('⚠️ Page timeout failed, page may be closed:', error.message);
      throw new Error('Page context was closed during search input detection');
    }
  }

  // First, try specific selectors for known search components
  const specificSelectors = [
    // Hero component search (homepage)
    'input[placeholder="Search for events..."]',
    'form input[type="text"]', // Hero form input
    // SearchBar component (events page)
    'input[placeholder="Search events..."]',
    'input[placeholder*="search" i]',
    // Generic search selectors
    'input[type="search"]',
    'input[name*="search" i]',
    '[data-testid*="search"] input',
    '.search input'
  ];

  for (const selector of specificSelectors) {
    const element = page.locator(selector).first();
    if (await element.count() > 0) {
      // Check if element is visible
      const isVisible = await element.isVisible();
      console.log(`🔍 Selector "${selector}": found=${await element.count()}, visible=${isVisible}`);

      if (isVisible) {
        console.log(`✅ Found visible search input with selector: ${selector}`);
        return element;
      }
    }
  }

  // If no search input found on current page, try navigating to events page
  const pageUrl = page.url();
  if (!pageUrl.includes('/events')) {
    console.log('🔄 No search input on current page, trying events page...');
    await page.goto('/events');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Try again on events page
    for (const selector of specificSelectors) {
      const element = page.locator(selector).first();
      if (await element.count() > 0 && await element.isVisible()) {
        console.log(`✅ Found search input on events page with selector: ${selector}`);
        return element;
      }
    }
  }

  // If still no search input found, log debugging info
  console.log('⚠️ No search input found, logging page info...');
  console.log('Page title:', await page.title());
  console.log('Page URL:', page.url());
  console.log('Total input elements:', await page.locator('input').count());

  // List all input elements for debugging
  const allInputs = page.locator('input');
  const inputCount = await allInputs.count();
  for (let i = 0; i < Math.min(inputCount, 5); i++) {
    const input = allInputs.nth(i);
    const type = await input.getAttribute('type') || 'text';
    const placeholder = await input.getAttribute('placeholder') || '';
    const name = await input.getAttribute('name') || '';
    const className = await input.getAttribute('class') || '';
    console.log(`Input ${i + 1}: type="${type}", placeholder="${placeholder}", name="${name}", class="${className}"`);
  }

  // Try any input as last resort
  if (inputCount > 0) {
    const anyInput = allInputs.first();
    console.log('🔄 Using first available input as fallback');
    return anyInput;
  }

  throw new Error(`No search input found. Available inputs: ${inputCount}`);
}

/**
 * Sign out helper for test cleanup
 */
export async function signOut(page: Page): Promise<void> {
  console.log('👋 Signing out...');

  const signOutSelectors = [
    'text=Sign out',
    'button:has-text("Sign out")',
    'a:has-text("Sign out")'
  ];

  for (const selector of signOutSelectors) {
    try {
      const element = page.locator(selector);
      if (await element.isVisible({ timeout: 2000 })) {
        await element.click();
        await page.waitForLoadState('networkidle');
        console.log('✅ Signed out successfully');
        return;
      }
    } catch {
      // Continue to next selector
    }
  }

  console.log('ℹ️ Sign out button not found - user may already be signed out');
}
