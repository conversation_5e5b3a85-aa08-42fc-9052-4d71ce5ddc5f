import { Page } from '@playwright/test';

/**
 * Authentication helper for Playwright tests
 * 
 * This helper provides methods to mock authentication state
 * without going through the actual Google OAuth flow.
 */

export interface MockUser {
  id: string;
  email: string;
  user_metadata: {
    full_name: string;
    avatar_url?: string;
  };
  app_metadata: {
    provider: string;
  };
}

export interface MockSession {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  expires_at: number;
  token_type: string;
  user: Mock<PERSON><PERSON>;
}

/**
 * Creates a mock user object for testing
 */
export function createMockUser(overrides: Partial<MockUser> = {}): MockUser {
  return {
    id: 'test-user-id-123',
    email: '<EMAIL>',
    user_metadata: {
      full_name: 'Test User',
      avatar_url: 'https://example.com/avatar.jpg',
    },
    app_metadata: {
      provider: 'google',
    },
    ...overrides,
  };
}

/**
 * Creates a mock session object for testing
 */
export function createMockSession(user?: MockUser): MockSession {
  const mockUser = user || createMockUser();
  const now = Date.now();
  
  return {
    access_token: 'mock-access-token-for-testing',
    refresh_token: 'mock-refresh-token-for-testing',
    expires_in: 3600,
    expires_at: now + 3600000, // 1 hour from now
    token_type: 'bearer',
    user: mockUser,
  };
}

/**
 * Mock authentication by setting localStorage and sessionStorage
 * This simulates what Supabase Auth does when a user is authenticated
 */
export async function mockAuthentication(page: Page, user?: MockUser): Promise<void> {
  const mockUser = user || createMockUser();
  const mockSession = createMockSession(mockUser);
  
  // Set the Supabase auth session in localStorage
  // This is what Supabase uses to persist authentication state
  await page.addInitScript((session) => {
    // Set the auth session
    localStorage.setItem(
      'sb-teetxyvxjvbuztjmrvyt-auth-token',
      JSON.stringify(session)
    );
    
    // Also set it in the format Supabase expects
    localStorage.setItem(
      'supabase.auth.token',
      JSON.stringify(session)
    );
  }, mockSession);

  // Also mock the user profile in the database response
  await page.route('**/rest/v1/profiles*', async (route) => {
    const mockProfile = {
      id: mockUser.id,
      username: mockUser.user_metadata.full_name?.toLowerCase().replace(' ', '_'),
      full_name: mockUser.user_metadata.full_name,
      avatar_url: mockUser.user_metadata.avatar_url,
      website: null,
      is_admin: false,
      location: null,
      location_updated_at: null,
      locality_short_name: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify([mockProfile]),
    });
  });
}

/**
 * Mock admin authentication
 */
export async function mockAdminAuthentication(page: Page): Promise<void> {
  const adminUser = createMockUser({
    id: 'admin-user-id-123',
    email: '<EMAIL>',
    user_metadata: {
      full_name: 'Admin User',
    },
  });

  await mockAuthentication(page, adminUser);

  // Override the profile response to include admin privileges
  await page.route('**/rest/v1/profiles*', async (route) => {
    const mockProfile = {
      id: adminUser.id,
      username: 'admin_user',
      full_name: 'Admin User',
      avatar_url: adminUser.user_metadata.avatar_url,
      website: null,
      is_admin: true, // This is the key difference
      location: null,
      location_updated_at: null,
      locality_short_name: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify([mockProfile]),
    });
  });
}

/**
 * Clear authentication state
 */
export async function clearAuthentication(page: Page): Promise<void> {
  await page.addInitScript(() => {
    localStorage.removeItem('sb-teetxyvxjvbuztjmrvyt-auth-token');
    localStorage.removeItem('supabase.auth.token');
    sessionStorage.clear();
  });
}

/**
 * Check if the page shows authenticated state
 */
export async function isAuthenticated(page: Page): Promise<boolean> {
  try {
    // Look for elements that indicate authentication
    // This could be a user menu, profile link, or sign out button
    const authIndicators = [
      'text=Sign out',
      'text=Profile',
      'text=My Events',
      '[data-testid="user-menu"]',
      '[data-testid="authenticated-user"]'
    ];

    for (const indicator of authIndicators) {
      const element = page.locator(indicator);
      if (await element.isVisible({ timeout: 1000 })) {
        return true;
      }
    }

    return false;
  } catch {
    return false;
  }
}

/**
 * Wait for authentication state to be loaded
 */
export async function waitForAuthState(page: Page, timeout: number = 5000): Promise<void> {
  // Wait for the auth context to be initialized
  // This looks for either authenticated or unauthenticated state
  await page.waitForFunction(
    () => {
      // Check if we have auth indicators or sign-in button using proper CSS selectors
      const hasSignInButton = document.querySelector('button:has-text("Sign in with Google")') !== null ||
                             document.querySelector('button[type="button"]:contains("Sign in with Google")') !== null ||
                             document.querySelector('button') && Array.from(document.querySelectorAll('button')).some(btn => btn.textContent?.includes('Sign in with Google'));

      const hasSignOutButton = document.querySelector('button') && Array.from(document.querySelectorAll('button')).some(btn => btn.textContent?.includes('Sign out'));

      const hasUserMenu = document.querySelector('[data-testid="user-menu"]') !== null ||
                         document.querySelector('[aria-label*="user"]') !== null ||
                         document.querySelector('[aria-label*="profile"]') !== null;

      // Also check for common auth indicators
      const hasProfileLink = document.querySelector('a[href="/profile"]') !== null;
      const hasMyEventsLink = document.querySelector('a[href="/my-events"]') !== null;

      return hasSignInButton || hasSignOutButton || hasUserMenu || hasProfileLink || hasMyEventsLink;
    },
    { timeout }
  );
}

/**
 * Mock successful Google OAuth callback
 * This simulates what happens when Google redirects back to your app
 */
export async function mockGoogleOAuthCallback(page: Page, user?: MockUser): Promise<void> {
  const mockUser = user || createMockUser();
  const mockSession = createMockSession(mockUser);

  // Navigate to a URL that simulates the OAuth callback
  const callbackUrl = new URL(page.url());
  callbackUrl.hash = `#access_token=${mockSession.access_token}&expires_in=${mockSession.expires_in}&refresh_token=${mockSession.refresh_token}&token_type=bearer&type=signup`;
  
  await page.goto(callbackUrl.toString());
  
  // Wait for the auth state to be processed
  await waitForAuthState(page);
}

/**
 * Test helper to set up authenticated test environment
 */
export async function setupAuthenticatedTest(page: Page, user?: MockUser): Promise<void> {
  // Mock authentication before navigating to any page
  await mockAuthentication(page, user);
  
  // Navigate to the page and wait for auth state
  await page.goto('/');
  await waitForAuthState(page);
}

/**
 * Test helper to set up admin test environment
 */
export async function setupAdminTest(page: Page): Promise<void> {
  await mockAdminAuthentication(page);
  await page.goto('/');
  await waitForAuthState(page);
}
