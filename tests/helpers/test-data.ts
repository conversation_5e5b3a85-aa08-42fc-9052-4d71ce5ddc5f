/**
 * Test data and utilities for event creation tests
 */

export interface TestEventData {
  title: string;
  description: string;
  category: string;
  organizerName: string;
  organizerEmail?: string;
  organizerPhone?: string;
  websiteUrl?: string;
  startDate: string;
  startTime: string;
  endDate?: string;
  endTime?: string;
  isMultiDay: boolean;
  maxAttendees?: string;
  venueName?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  isFree: boolean;
  generalAdmission?: string;
  vipTicket?: string;
  hasEarlyBird: boolean;
  earlyBirdDeadline?: string;
  earlyBirdDiscount?: string;
  groupDiscount: boolean;
  minGroupSize?: string;
  groupDiscountPercentage?: string;
  refundPolicy?: string;
  organizerTerms?: string;
  tags?: string;
}

export const validEventData: TestEventData = {
  title: 'Automated Test Event - Community Workshop',
  description: 'This is a comprehensive test event description that meets all the minimum character requirements. It provides detailed information about what attendees can expect from this community workshop event.',
  category: 'Workshop',
  organizerName: 'Test Organizer',
  organizerEmail: '<EMAIL>',
  organizerPhone: '+91 9876543210',
  websiteUrl: 'https://example.com',
  startDate: '2024-12-31', // Future date
  startTime: '10:00',
  endDate: '2024-12-31',
  endTime: '12:00',
  isMultiDay: false,
  maxAttendees: '50',
  venueName: 'Test Community Center',
  address: '123 Test Street',
  city: 'Delhi',
  state: 'Delhi',
  zipCode: '110001',
  isFree: true,
  hasEarlyBird: false,
  groupDiscount: false,
  refundPolicy: 'Full refund available up to 24 hours before the event.',
  organizerTerms: 'Please arrive 15 minutes early. Bring your own water bottle.',
  tags: 'community, workshop, learning'
};

export const invalidEventData = {
  shortTitle: 'Test', // Less than 5 characters
  shortDescription: 'Short desc', // Less than 20 characters
  invalidEmail: 'invalid-email-format',
  invalidUrl: 'not-a-valid-url',
  pastDate: '2020-01-01',
  invalidTime: '25:00'
};

export const mandatoryFields = {
  title: 'input[name="title"]',
  description: 'textarea[name="description"]',
  category: '[role="combobox"]', // Category selector
  startDate: 'input[name="startDate"]',
  startTime: 'input[name="startTime"]',
  venueId: 'input[name="venueId"]',
  organizerName: 'input[name="organizerName"]',
  termsAgreed: 'input[name="termsAgreed"]',
  privacyPolicyAgreed: 'input[name="privacyPolicyAgreed"]'
};

export const formSections = {
  basicInfo: '[data-value="item-1"]',
  eventDetails: '[data-value="item-2"]',
  location: '[data-value="item-3"]',
  organizer: '[data-value="item-4"]',
  ticketing: '[data-value="item-5"]',
  organizerTerms: '[data-value="item-6"]',
  terms: '[data-value="item-7"]'
};

export const validationMessages = {
  titleTooShort: 'Title should be at least 5 characters long',
  descriptionTooShort: 'Description should be at least 20 characters long',
  categoryRequired: 'Please select a category for your event',
  startDateRequired: 'Please select the start date',
  startTimeRequired: 'Please select a start time',
  venueRequired: 'Please select or create a venue',
  organizerNameRequired: 'Organizer name is required',
  invalidEmail: 'Please enter a valid email',
  invalidUrl: 'Please enter a valid URL',
  termsRequired: 'You must agree to the terms',
  privacyRequired: 'You must agree to the privacy policy',
  organizerTermsRequired: 'Please provide terms and conditions for your event'
};

export const successMessages = {
  eventCreated: 'Event created successfully',
  eventSubmitted: 'Your event has been submitted for approval'
};

/**
 * Helper function to get a future date in YYYY-MM-DD format
 */
export function getFutureDate(daysFromNow: number = 30): string {
  const date = new Date();
  date.setDate(date.getDate() + daysFromNow);
  return date.toISOString().split('T')[0];
}

/**
 * Helper function to get current time in HH:MM format
 */
export function getCurrentTime(): string {
  const now = new Date();
  return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
}

/**
 * Helper function to get a time 2 hours from current time
 */
export function getFutureTime(hoursFromNow: number = 2): string {
  const now = new Date();
  now.setHours(now.getHours() + hoursFromNow);
  return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
}

/**
 * Common categories that should be available in the system
 */
export const commonCategories = [
  'Workshop',
  'Meetup',
  'Conference',
  'Social',
  'Sports',
  'Arts & Culture',
  'Food & Drink',
  'Health & Wellness',
  'Technology',
  'Business'
];

/**
 * Common venues for testing
 */
export const commonVenues = [
  'Community Center',
  'Library Hall',
  'Park Pavilion',
  'Conference Room',
  'Outdoor Space'
];

/**
 * Test data for different event types
 */
export const eventTypes = {
  freeWorkshop: {
    ...validEventData,
    title: 'Free Community Workshop',
    category: 'Workshop',
    isFree: true
  },
  
  paidConference: {
    ...validEventData,
    title: 'Tech Conference 2024',
    category: 'Conference',
    isFree: false,
    generalAdmission: '500',
    vipTicket: '1000',
    hasEarlyBird: true,
    earlyBirdDiscount: '20'
  },
  
  multiDayEvent: {
    ...validEventData,
    title: 'Multi-Day Festival',
    category: 'Arts & Culture',
    isMultiDay: true,
    startDate: getFutureDate(30),
    endDate: getFutureDate(32)
  },
  
  socialMeetup: {
    ...validEventData,
    title: 'Community Social Meetup',
    category: 'Social',
    isFree: true,
    maxAttendees: '25'
  }
};
