// Event Title Debug Script
// Run this in your browser console to see what event data is being sent

console.log('🔍 Event Title Debug Script');
console.log('============================');

// Monitor all gtag calls to see event data
if (typeof window.gtag === 'function') {
    const originalGtag = window.gtag;
    
    window.gtag = function(...args) {
        if (args[0] === 'event' && args[1].includes('localadda_event')) {
            console.log('📊 Event Data Sent:');
            console.log('- Action:', args[1]);
            console.log('- Parameters:', args[2]);
            console.log('- Event Label:', args[2]?.event_label);
            console.log('- Event ID:', args[2]?.event_id);
            console.log('- Event Title:', args[2]?.event_title);
            console.log('- Full Object:', args[2]);
            console.log('---');
        }
        return originalGtag.apply(this, args);
    };
    
    console.log('✅ Event monitoring enabled');
    console.log('Click on events and check the data above');
} else {
    console.log('❌ gtag not found');
}

// Check if event data is available in the DOM
console.log('🔍 Checking DOM for event data...');
const eventCards = document.querySelectorAll('[data-event-id], .event-card, [href*="/events/"]');
console.log(`Found ${eventCards.length} potential event elements`);

eventCards.forEach((card, index) => {
    const title = card.querySelector('h1, h2, h3, h4, .title, [class*="title"]')?.textContent?.trim();
    const link = card.querySelector('a[href*="/events/"]') || card;
    const href = link.getAttribute('href');
    const eventId = href ? href.split('/').pop() : 'unknown';
    
    console.log(`Event ${index + 1}:`, {
        title: title || 'No title found',
        eventId: eventId,
        element: card
    });
});

console.log('============================');
console.log('Instructions:');
console.log('1. Click on event cards');
console.log('2. Check if event_label contains the title');
console.log('3. Look for any missing title data');
