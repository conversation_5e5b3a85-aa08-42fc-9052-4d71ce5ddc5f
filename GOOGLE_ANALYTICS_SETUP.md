# Google Analytics Setup Guide

This guide will help you set up Google Analytics 4 (GA4) for your TheLocalAdda website.

## 🚀 Quick Setup

### 1. Create Google Analytics Account

1. Go to [Google Analytics](https://analytics.google.com/)
2. Click "Start measuring"
3. Create an account name (e.g., "TheLocalAdda")
4. Choose what you want to measure: "Web"
5. Set up your property:
   - Property name: "TheLocalAdda Website"
   - Reporting time zone: Your local timezone
   - Currency: Your local currency
6. Choose your business information
7. Accept the terms of service

### 2. Get Your Tracking ID ✅ COMPLETED

Your Google Analytics tracking ID is: **G-K5ERWJ7N4Y**

This has already been configured in your application!

### 3. Configure Environment Variables ✅ COMPLETED

Your tracking ID is already configured! The Google Analytics script is embedded directly in your HTML.

**Optional:** For development testing, you can add to your `.env` file:

```bash
# Optional: Enable analytics in development (default: false)
VITE_ENABLE_ANALYTICS=true

# Optional: Enable debug mode for analytics (default: false)
VITE_GA_DEBUG=true
```

### 4. Build and Deploy

The analytics will automatically be included when you build your application:

```bash
npm run build
```

## 📊 What's Being Tracked

### Automatic Tracking
- **Page Views**: Every route change is automatically tracked
- **User Sessions**: Session duration and user engagement
- **Device Information**: Browser, OS, device type
- **Geographic Data**: Country, region, city (anonymized)

### Custom Events
- **Authentication**: Login/logout events with provider info
- **Event Interactions**: View, register, share, create events
- **Search Queries**: Search terms and result counts
- **Form Interactions**: Form starts, submissions, and errors
- **Error Tracking**: JavaScript errors and their locations
- **Performance Metrics**: Page load times and component render times

## 🔧 Using Analytics in Your Components

### Basic Event Tracking

```tsx
import { useAnalytics } from '@/hooks/useAnalytics';

const MyComponent = () => {
  const { trackEvent } = useAnalytics();

  const handleButtonClick = () => {
    trackEvent('click', 'User Engagement', 'Header CTA Button');
  };

  return <button onClick={handleButtonClick}>Click me</button>;
};
```

### Authentication Tracking

```tsx
import { useAnalytics } from '@/hooks/useAnalytics';

const AuthComponent = () => {
  const { trackAuth } = useAnalytics();

  const handleLogin = async () => {
    // Your login logic here
    trackAuth('login', 'google');
  };

  return <button onClick={handleLogin}>Sign in with Google</button>;
};
```

### Event Interaction Tracking

```tsx
import { useEventAnalytics } from '@/hooks/useAnalytics';

const EventCard = ({ event }) => {
  const { trackEventView, trackEventRegister } = useEventAnalytics();

  useEffect(() => {
    // Track when event is viewed
    trackEventView(event.id, event.title);
  }, [event.id, event.title]);

  const handleRegister = () => {
    trackEventRegister(event.id, event.title);
    // Your registration logic here
  };

  return (
    <div>
      <h3>{event.title}</h3>
      <button onClick={handleRegister}>Register</button>
    </div>
  );
};
```

### Search Tracking

```tsx
import { useSearchAnalytics } from '@/hooks/useAnalytics';

const SearchComponent = () => {
  const { trackSearchQuery } = useSearchAnalytics();

  const handleSearch = (query: string, results: any[]) => {
    trackSearchQuery(query, results.length);
  };

  return <SearchInput onSearch={handleSearch} />;
};
```

### Form Tracking

```tsx
import { useFormAnalytics } from '@/hooks/useAnalytics';

const CreateEventForm = () => {
  const { trackFormStart, trackFormSubmit, trackFormError } = useFormAnalytics('create-event');

  useEffect(() => {
    trackFormStart();
  }, []);

  const handleSubmit = async (data) => {
    try {
      await submitEvent(data);
      trackFormSubmit();
    } catch (error) {
      trackFormError(error.message);
    }
  };

  return <form onSubmit={handleSubmit}>...</form>;
};
```

## 🐛 Development and Debugging

### Enable Debug Mode

Set `VITE_GA_DEBUG=true` in your `.env` file to see analytics events in the console:

```bash
VITE_GA_DEBUG=true
```

### Test in Development

To test analytics in development mode:

```bash
VITE_ENABLE_ANALYTICS=true
VITE_GA_DEBUG=true
```

### Verify Installation

1. Open your website
2. Open browser DevTools → Console
3. Look for analytics initialization messages
4. Check the Network tab for requests to `google-analytics.com`

## 📈 Viewing Your Data

### Real-Time Reports
1. Go to Google Analytics
2. Click **Reports** → **Realtime**
3. Test your website to see live data

### Key Reports to Monitor
- **Acquisition** → **Traffic acquisition**: How users find your site
- **Engagement** → **Events**: Custom events you're tracking
- **Engagement** → **Pages and screens**: Most popular pages
- **Demographics** → **User attributes**: User demographics
- **Tech** → **Tech details**: Browser and device information

## 🔒 Privacy and Compliance

### Built-in Privacy Features
- **IP Anonymization**: Enabled by default
- **Consent Mode**: Ready for cookie consent integration
- **Data Retention**: Configurable in GA4 settings

### GDPR Compliance
Consider implementing a cookie consent banner:

```tsx
// Example cookie consent integration
const { setUserProperties } = useAnalytics();

const handleConsentAccepted = () => {
  setUserProperties({
    consent_analytics: 'granted',
    consent_marketing: 'granted'
  });
};
```

## 🚨 Troubleshooting

### Analytics Not Working?

1. **Check Environment Variables**:
   ```bash
   echo $VITE_GA_TRACKING_ID
   ```

2. **Verify Tracking ID Format**:
   - Should start with `G-` (GA4)
   - Example: `G-ABC123DEF4`

3. **Check Console for Errors**:
   - Enable debug mode
   - Look for initialization messages

4. **Verify Script Loading**:
   - Check Network tab for `gtag/js` requests
   - Ensure no ad blockers are interfering

### Common Issues

- **Ad Blockers**: Some users may have analytics blocked
- **Development Mode**: Analytics disabled by default in dev
- **Missing Tracking ID**: Check environment variable setup
- **CORS Issues**: Ensure your domain is configured in GA4

## 📚 Additional Resources

- [Google Analytics 4 Documentation](https://developers.google.com/analytics/devguides/collection/ga4)
- [GA4 Event Reference](https://developers.google.com/analytics/devguides/collection/ga4/reference/events)
- [Google Tag Manager](https://tagmanager.google.com/) (for advanced tracking)

## 🎯 Next Steps

1. Set up **Goals** in GA4 for key actions (event registrations, account signups)
2. Configure **Audiences** for remarketing
3. Set up **Custom Dimensions** for additional event properties
4. Implement **Enhanced Ecommerce** if you add paid events
5. Connect to **Google Ads** for advertising campaigns
