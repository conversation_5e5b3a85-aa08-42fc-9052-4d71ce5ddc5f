// Event Tracking Debug Script
// Run this in your browser console to see what events are being tracked

console.log('🔍 Event Tracking Debug');
console.log('======================');

// Monitor all gtag calls
if (typeof window.gtag === 'function') {
    const originalGtag = window.gtag;
    let eventCount = 0;
    
    window.gtag = function(...args) {
        if (args[0] === 'event') {
            eventCount++;
            console.log(`📊 Event #${eventCount}:`, {
                action: args[1],
                parameters: args[2] || {},
                timestamp: new Date().toLocaleTimeString()
            });
            
            // Highlight potential duplicates
            if (args[1] === 'click' || args[1] === 'event_view' || args[1] === 'select_content') {
                console.warn('⚠️ Potential duplicate event:', args[1]);
            }
        }
        return originalGtag.apply(this, args);
    };
    
    console.log('✅ Event monitoring enabled');
    console.log('Click on events and watch for duplicate tracking');
} else {
    console.log('❌ gtag not found');
}

// Check Enhanced Measurement settings
console.log('💡 To check Enhanced Measurement:');
console.log('1. Go to Google Analytics');
console.log('2. Admin → Data Streams → Your Stream');
console.log('3. Click "Enhanced measurement"');
console.log('4. Check what auto-tracking is enabled');
