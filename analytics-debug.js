// Google Analytics Debug Script
// Copy and paste this into your browser console on your live website

console.log('🔍 Google Analytics Debug Script');
console.log('================================');

// Check if gtag is loaded
if (typeof gtag === 'function') {
    console.log('✅ Google Analytics (gtag) is loaded');
    
    // Send a test event
    gtag('event', 'debug_test', {
        event_category: 'debug',
        event_label: 'console_test',
        debug_mode: true
    });
    console.log('📤 Test event sent');
    
} else {
    console.log('❌ Google Analytics (gtag) is NOT loaded');
    console.log('Check if:');
    console.log('- You are on the live website (not localhost)');
    console.log('- Ad blockers are disabled');
    console.log('- Network requests to googletagmanager.com are not blocked');
}

// Check for dataLayer
if (window.dataLayer) {
    console.log('✅ dataLayer exists:', window.dataLayer.length, 'items');
    console.log('Recent dataLayer items:', window.dataLayer.slice(-5));
} else {
    console.log('❌ dataLayer not found');
}

// Check current page info
console.log('📄 Current page info:');
console.log('- URL:', window.location.href);
console.log('- Title:', document.title);
console.log('- Referrer:', document.referrer);

// Network check
console.log('🌐 To check network requests:');
console.log('1. Open DevTools → Network tab');
console.log('2. Filter by "google" or "analytics"');
console.log('3. Reload the page');
console.log('4. Look for requests to googletagmanager.com');

console.log('================================');
console.log('Next steps:');
console.log('1. Check Google Analytics Real-time reports');
console.log('2. Wait 24-48 hours for standard reports');
console.log('3. Generate some real traffic to your site');
