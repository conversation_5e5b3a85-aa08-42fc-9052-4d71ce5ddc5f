<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics Test - TheLocalAdda</title>
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-K5ERWJ7N4Y"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-K5ERWJ7N4Y');
      
      // Enable debug mode
      gtag('config', 'G-K5ERWJ7N4Y', {
        debug_mode: true
      });
    </script>
    
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 50px auto; 
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover {
            background: #3367d6;
        }
        .status {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .debug {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Google Analytics Test Page</h1>
        <p>This page tests if Google Analytics is working correctly for TheLocalAdda.</p>
        
        <div class="status">
            <strong>✅ Analytics Status:</strong> 
            <span id="status">Checking...</span>
        </div>
        
        <h3>Test Analytics Events:</h3>
        <button onclick="testPageView()">📄 Test Page View</button>
        <button onclick="testCustomEvent()">🎯 Test Custom Event</button>
        <button onclick="testLogin()">🔐 Test Login Event</button>
        <button onclick="testSearch()">🔍 Test Search Event</button>
        
        <div class="debug">
            <strong>Debug Console:</strong>
            <div id="debug-log"></div>
        </div>
        
        <h3>Instructions:</h3>
        <ol>
            <li><strong>Open Browser DevTools</strong> (F12)</li>
            <li><strong>Go to Network tab</strong> and look for requests to <code>google-analytics.com</code></li>
            <li><strong>Click the test buttons above</strong> to send events</li>
            <li><strong>Check Google Analytics Real-time reports</strong> (may take 1-2 minutes)</li>
            <li><strong>Look for this page in Real-time → Overview</strong></li>
        </ol>
        
        <h3>Troubleshooting:</h3>
        <ul>
            <li>Make sure you're on the <strong>live website</strong> (not localhost)</li>
            <li>Disable ad blockers temporarily</li>
            <li>Check if requests to <code>googletagmanager.com</code> are being blocked</li>
            <li>Wait 24-48 hours for data to appear in standard reports</li>
        </ul>
    </div>

    <script>
        // Debug logging
        function log(message) {
            const debugLog = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }
        
        // Check if gtag is loaded
        function checkAnalytics() {
            const statusEl = document.getElementById('status');
            if (typeof gtag === 'function') {
                statusEl.textContent = 'Google Analytics Loaded ✅';
                statusEl.style.color = 'green';
                log('✅ Google Analytics (gtag) is loaded and ready');
                return true;
            } else {
                statusEl.textContent = 'Google Analytics NOT Loaded ❌';
                statusEl.style.color = 'red';
                log('❌ Google Analytics (gtag) is not loaded');
                return false;
            }
        }
        
        // Test functions
        function testPageView() {
            if (!checkAnalytics()) return;
            
            gtag('event', 'page_view', {
                page_title: 'Analytics Test Page',
                page_location: window.location.href
            });
            log('📄 Page view event sent');
        }
        
        function testCustomEvent() {
            if (!checkAnalytics()) return;
            
            gtag('event', 'test_button_click', {
                event_category: 'engagement',
                event_label: 'analytics_test',
                value: 1
            });
            log('🎯 Custom event sent: test_button_click');
        }
        
        function testLogin() {
            if (!checkAnalytics()) return;
            
            gtag('event', 'login', {
                method: 'google'
            });
            log('🔐 Login event sent');
        }
        
        function testSearch() {
            if (!checkAnalytics()) return;
            
            gtag('event', 'search', {
                search_term: 'test search query'
            });
            log('🔍 Search event sent');
        }
        
        // Initialize
        window.addEventListener('load', function() {
            setTimeout(checkAnalytics, 1000); // Wait for gtag to load
            log('🚀 Analytics test page loaded');
            
            // Send initial page view
            setTimeout(testPageView, 2000);
        });
        
        // Track page visibility
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                log('👁️ Page became visible');
            } else {
                log('🙈 Page became hidden');
            }
        });
    </script>
</body>
</html>
