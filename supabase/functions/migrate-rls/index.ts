
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.47.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }
  
  try {
    // Create a Supabase client with the Admin key
    const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("Missing Supabase environment variables");
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });

    // Drop existing RLS policies for events table
    console.log("Dropping existing RLS policies for events table...");
    const { error: dropError } = await supabase.rpc('drop_all_policies_for_table', {
      table_name: 'events'
    });
    
    if (dropError) {
      console.error("Error dropping policies:", dropError);
      // Continue anyway, as the function might not exist
    }
    
    // Create RLS policies for the events table
    console.log("Creating RLS policies for events table...");
    
    // Policy 1: Authenticated users can see approved events
    const { error: policy1Error } = await supabase.rpc('create_policy', {
      table_name: 'events',
      policy_name: 'Users can view approved events',
      definition: "approval_status = 'approved'",
      policy_action: 'SELECT',
      policy_role: 'authenticated'
    });
    
    if (policy1Error) {
      console.error("Error creating policy 1:", policy1Error);
      throw policy1Error;
    }
    
    // Policy 2: Users can see their own events regardless of status
    const { error: policy2Error } = await supabase.rpc('create_policy', {
      table_name: 'events',
      policy_name: 'Users can view their own events',
      definition: "organizer_id = auth.uid()",
      policy_action: 'SELECT',
      policy_role: 'authenticated'
    });
    
    if (policy2Error) {
      console.error("Error creating policy 2:", policy2Error);
      throw policy2Error;
    }
    
    // Policy 3: Admins can see all events
    const { error: policy3Error } = await supabase.rpc('create_policy', {
      table_name: 'events',
      policy_name: 'Admins can see all events',
      definition: "EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.is_admin = true)",
      policy_action: 'SELECT',
      policy_role: 'authenticated'
    });
    
    if (policy3Error) {
      console.error("Error creating policy 3:", policy3Error);
      throw policy3Error;
    }
    
    // Policy 4: Users can only create their own events
    const { error: policy4Error } = await supabase.rpc('create_policy', {
      table_name: 'events',
      policy_name: 'Users can create their own events',
      definition: "organizer_id = auth.uid()",
      policy_action: 'INSERT',
      policy_role: 'authenticated'
    });
    
    if (policy4Error) {
      console.error("Error creating policy 4:", policy4Error);
      throw policy4Error;
    }
    
    // Policy 5: Users can update only their own events
    const { error: policy5Error } = await supabase.rpc('create_policy', {
      table_name: 'events',
      policy_name: 'Users can update their own events',
      definition: "organizer_id = auth.uid()",
      policy_action: 'UPDATE',
      policy_role: 'authenticated'
    });
    
    if (policy5Error) {
      console.error("Error creating policy 5:", policy5Error);
      throw policy5Error;
    }
    
    // Policy 6: Admins can update any event
    const { error: policy6Error } = await supabase.rpc('create_policy', {
      table_name: 'events',
      policy_name: 'Admins can update any event',
      definition: "EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.is_admin = true)",
      policy_action: 'UPDATE',
      policy_role: 'authenticated'
    });
    
    if (policy6Error) {
      console.error("Error creating policy 6:", policy6Error);
      throw policy6Error;
    }
    
    // Policy 7: Users can delete only their own events
    const { error: policy7Error } = await supabase.rpc('create_policy', {
      table_name: 'events',
      policy_name: 'Users can delete their own events',
      definition: "organizer_id = auth.uid()",
      policy_action: 'DELETE',
      policy_role: 'authenticated'
    });
    
    if (policy7Error) {
      console.error("Error creating policy 7:", policy7Error);
      throw policy7Error;
    }
    
    // Policy 8: Admins can delete any event
    const { error: policy8Error } = await supabase.rpc('create_policy', {
      table_name: 'events',
      policy_name: 'Admins can delete any event',
      definition: "EXISTS (SELECT 1 FROM profiles WHERE profiles.id = auth.uid() AND profiles.is_admin = true)",
      policy_action: 'DELETE',
      policy_role: 'authenticated'
    });
    
    if (policy8Error) {
      console.error("Error creating policy 8:", policy8Error);
      throw policy8Error;
    }
    
    // Create additional RPC functions for policy management if needed
    console.log("Creating RPC functions...");
    
    // Function to drop all policies for a table
    const createDropPoliciesFunc = `
      CREATE OR REPLACE FUNCTION drop_all_policies_for_table(table_name text)
      RETURNS void
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      DECLARE
        pol RECORD;
      BEGIN
        FOR pol IN 
          SELECT policyname 
          FROM pg_policies 
          WHERE tablename = table_name
        LOOP
          EXECUTE format('DROP POLICY IF EXISTS %I ON %I', pol.policyname, table_name);
        END LOOP;
      END;
      $$;
    `;
    
    const { error: dropFuncError } = await supabase.sql(createDropPoliciesFunc);
    
    if (dropFuncError) {
      console.error("Error creating drop_all_policies_for_table function:", dropFuncError);
      // Continue anyway
    }
    
    // Function to create a policy
    const createPolicyFunc = `
      CREATE OR REPLACE FUNCTION create_policy(
        table_name text,
        policy_name text,
        definition text,
        policy_action text DEFAULT 'ALL',
        policy_role text DEFAULT 'authenticated'
      )
      RETURNS void
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      DECLARE
        sql text;
      BEGIN
        sql := format('CREATE POLICY %I ON %I FOR %s TO %s USING (%s)',
                      policy_name, table_name, policy_action, policy_role, definition);
        EXECUTE sql;
      END;
      $$;
    `;
    
    const { error: policyFuncError } = await supabase.sql(createPolicyFunc);
    
    if (policyFuncError) {
      console.error("Error creating create_policy function:", policyFuncError);
      // Continue anyway
    }
    
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: "RLS policies created successfully"
      }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200 
      }
    );
    
  } catch (error) {
    console.error("Error in migrate-rls function:", error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        message: error.message || "An error occurred while migrating RLS policies" 
      }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500 
      }
    );
  }
});
