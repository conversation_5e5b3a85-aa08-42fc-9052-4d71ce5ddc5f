// Supabase Edge Function: Generate Event Poster and Upload to Storage
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { Image } from "https://deno.land/x/imagescript/mod.ts";
const supabaseUrl = Deno.env.get("SUPABASE_URL");
const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
const supabase = createClient(supabaseUrl, supabaseServiceKey);
// Constants
const SIZE = 1080;
const MARGIN = 50;
const logoSize = 125;
const labelFontSize = 40;
const titleFontSize = 80;
const categoryFontSize = 35;
// Helper: Draw icon + label combo
async function placeIconLabel(image, icon, text, font, y) {
  const labelImg = await Image.renderText(font, labelFontSize, text, 0xffffffff);
  const iconSize = 36;
  icon.resize(iconSize, iconSize);
  const verticalOffset = y + Math.floor((labelImg.height - iconSize) / 2);
  image.composite(icon, MARGIN, verticalOffset);
  image.composite(labelImg, MARGIN + iconSize + 12, y);
}
// CORS headers for all responses
const corsHeaders = {
  "Access-Control-Allow-Origin": "*", // Or specify your domain
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle OPTIONS request (CORS preflight)
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204, // No content
      headers: corsHeaders,
    });
  }

  try {
    const body = await req.json();
    const { eventImagePath, outputPosterPath, title, date, startTime, endTime, location, category } = body;
    const staticAssets = [
      "localadda_logo.png",
      "calendar.png",
      "clock.png",
      "location.png",
      "gradient.png",
      "fonts/Raleway-Bold.ttf",
      "fonts/Roboto-Regular.ttf",
      "fonts/Roboto-Bold.ttf"
    ];
    const [eventImageRaw, logoRaw, calendarIconRaw, clockIconRaw, locationIconRaw, gradientRaw, ralewayBoldRaw, robotoRegularRaw, robotoBoldRaw] = await Promise.all([
      supabase.storage.from("event-images").download(eventImagePath).then((res)=>res.data?.arrayBuffer()),
      ...staticAssets.map((name)=>supabase.storage.from("static-images").download(name).then((res)=>res.data?.arrayBuffer()))
    ]);
    const bgImage = await Image.decode(eventImageRaw);
    const logo = await Image.decode(logoRaw);
    const calendarIcon = await Image.decode(calendarIconRaw);
    const clockIcon = await Image.decode(clockIconRaw);
    const locationIcon = await Image.decode(locationIconRaw);
    const gradientImg = await Image.decode(gradientRaw);
    const ralewayBold = new Uint8Array(ralewayBoldRaw);
    const robotoRegular = new Uint8Array(robotoRegularRaw);
    const robotoBold = new Uint8Array(robotoBoldRaw);
    bgImage.cover(SIZE, SIZE);
    gradientImg.cover(SIZE, SIZE);
    bgImage.composite(gradientImg, 0, 0);
    // Category pill
    const categoryFontImg = await Image.renderText(robotoBold, categoryFontSize, category.name, category.textColor);
    const paddingX = 25, paddingY = 10;
    const pillWidth = categoryFontImg.width + paddingX * 2;
    const pillHeight = categoryFontImg.height + paddingY * 2;
    const categoryPill = new Image(pillWidth, pillHeight);
    categoryPill.fill(category.color);
    categoryPill.roundCorners(pillHeight / 2);
    categoryPill.composite(categoryFontImg, paddingX, (pillHeight - categoryFontImg.height) / 2);
    bgImage.composite(categoryPill, MARGIN, MARGIN);
    // Title wrapping
    const maxTitleWidth = SIZE - MARGIN * 2;
    const words = title.split(" ");
    let line1 = "", line2 = "", temp = "", lineCount = 1;
    for (const word of words){
      const testLine = temp.length ? `${temp} ${word}` : word;
      const testImg = await Image.renderText(ralewayBold, titleFontSize, testLine, 0xffffffff);
      if (testImg.width < maxTitleWidth) {
        temp = testLine;
      } else {
        if (!line1) {
          line1 = temp;
          temp = word;
          lineCount = 2;
        } else {
          line2 = temp + "…";
          temp = "";
          break;
        }
      }
    }
    if (!line1) line1 = temp;
    else if (!line2) line2 = temp;
    const line1Img = await Image.renderText(ralewayBold, titleFontSize, line1, 0xffffffff);
    const line2Img = line2 ? await Image.renderText(ralewayBold, titleFontSize, line2, 0xffffffff) : null;
    const titleY = SIZE - 400;
    bgImage.composite(line1Img, MARGIN, titleY);
    if (line2Img) bgImage.composite(line2Img, MARGIN, titleY + titleFontSize + 10);
    const spacingAfterTitle = lineCount === 2 ? titleFontSize * 2 + 40 : titleFontSize + 30;
    const labelYBase = titleY + spacingAfterTitle;
    await placeIconLabel(bgImage, calendarIcon, date, robotoRegular, labelYBase);
    const timeText = endTime ? `${startTime} - ${endTime}` : startTime;
    await placeIconLabel(bgImage, clockIcon, timeText, robotoRegular, labelYBase + labelFontSize + 20);
    let locationText = location;
    let locationImg = await Image.renderText(robotoRegular, labelFontSize, locationText, 0xffffffff);
    const maxWidth = SIZE - MARGIN * 2 - 50;
    if (locationImg.width > maxWidth) {
      while(locationText.length > 3){
        locationText = locationText.slice(0, -1);
        locationImg = await Image.renderText(robotoRegular, labelFontSize, locationText + "...", 0xffffffff);
        if (locationImg.width <= maxWidth) {
          locationText += "...";
          break;
        }
      }
    }
    await placeIconLabel(bgImage, locationIcon, locationText, robotoRegular, labelYBase + (labelFontSize + 20) * 2);
    logo.resize(logoSize, logoSize);
    bgImage.composite(logo, SIZE - logoSize - MARGIN, MARGIN);
    const tagline = "@thelocaladda.com";
    const taglineImg = await Image.renderText(robotoRegular, 30, tagline, 0x888888FF);
    bgImage.composite(taglineImg, (SIZE - taglineImg.width) / 2, SIZE - taglineImg.height - 7);
    const outputBuffer = await bgImage.encodeJPEG(80);
    const { error: uploadError } = await supabase.storage.from("event-posters").upload(outputPosterPath, outputBuffer, {
      contentType: "image/jpeg",
      upsert: true
    });
    if (uploadError) throw uploadError;
    const posterUrl = `${supabaseUrl}/storage/v1/object/public/event-posters/${outputPosterPath}`;
    return new Response(JSON.stringify({
      posterUrl
    }), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({
      error: error.message
    }), {
      status: 500,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders
      }
    });
  }
});
