
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.47.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Create a Supabase client with the Admin key
    const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("Missing Supabase environment variables");
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });

    // Process any parameters from request
    const params = req.url.includes("?") ? 
      Object.fromEntries(new URL(req.url).searchParams.entries()) : 
      {};
    
    // Check for existing events
    const { data: existingEvents, error: eventError } = await supabase
      .from("events")
      .select("id")
      .limit(1);
      
    if (eventError) {
      throw new Error(`Error checking existing events: ${eventError.message}`);
    }
    
    // If events already exist and we're not forcing a reseed, skip
    if (existingEvents && existingEvents.length > 0 && params.force !== "true") {
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: "Events already exist. Use ?force=true to reseed anyway." 
        }),
        { 
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 200 
        }
      );
    }
    
    // If we need to reseed, first check for user profiles
    const { data: profiles, error: profileError } = await supabase
      .from("profiles")
      .select("id, email")
      .limit(10);
    
    if (profileError) {
      throw new Error(`Error fetching profiles: ${profileError.message}`);
    }
    
    // If no profiles exist, we can't seed events
    if (!profiles || profiles.length === 0) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: "No user profiles found. At least one user must sign up before seeding events." 
        }),
        { 
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400 
        }
      );
    }
    
    // Use the first profile as the organizer
    const organizerId = profiles[0].id;
    
    // Get the existing events (if any) to delete them
    if (params.force === "true") {
      const { error: deleteError } = await supabase
        .from("events")
        .delete()
        .neq("id", "00000000-0000-0000-0000-000000000000"); // Delete all events
      
      if (deleteError) {
        console.error("Error deleting existing events:", deleteError);
      }
    }
    
    // Create sample events
    const sampleEvents = [
      {
        title: "South Delhi Art Festival",
        description: "Join us for a weekend of art exhibitions, workshops, and performances showcasing the talent of South Delhi artists.",
        category: "Art & Culture",
        start_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
        end_date: new Date(Date.now() + 9 * 24 * 60 * 60 * 1000).toISOString(), // 9 days from now
        is_multi_day: true,
        venue_name: "South Delhi Art Gallery",
        address: "123 Art Street",
        city: "New Delhi",
        state: "Delhi",
        zip_code: "110017",
        organizer_id: organizerId,
        organizer_name: "South Delhi Artist Collective",
        organizer_email: profiles[0].email,
        organizer_phone: "+91 98765 43210",
        is_free: false,
        general_admission_price: 250,
        vip_ticket_price: 500,
        registration_deadline: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000).toISOString(),
        max_attendees: 200,
        has_early_bird: true,
        early_bird_deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
        tags: ["art", "culture", "exhibition", "local"],
        image_url: "https://images.unsplash.com/photo-1594766281595-dd49658f4a21?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80",
        additional_images: [],
      },
      {
        title: "Delhi Street Food Festival",
        description: "Experience the vibrant street food culture of Delhi with over 50 food stalls representing different regions and cuisines.",
        category: "Food & Drink",
        start_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days from now
        is_multi_day: false,
        venue_name: "Nehru Park",
        address: "Vinay Marg, Diplomatic Enclave",
        city: "New Delhi",
        state: "Delhi",
        zip_code: "110021",
        organizer_id: organizerId,
        organizer_name: "Delhi Food Enthusiasts",
        organizer_email: profiles[0].email,
        is_free: true,
        max_attendees: 500,
        tags: ["food", "festival", "street food", "local"],
        image_url: "https://images.unsplash.com/photo-1601050690597-df0568f70950?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80",
        additional_images: [],
      },
      {
        title: "Yoga in the Park",
        description: "Start your weekend with a rejuvenating yoga session in the serene environment of Lodhi Gardens.",
        category: "Health & Wellness",
        start_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days from now
        is_multi_day: false,
        venue_name: "Lodhi Gardens",
        address: "Lodhi Road",
        city: "New Delhi",
        state: "Delhi",
        zip_code: "110003",
        organizer_id: organizerId,
        organizer_name: "Wellness Collective Delhi",
        organizer_email: profiles[0].email,
        is_free: false,
        general_admission_price: 100,
        registration_deadline: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
        max_attendees: 30,
        tags: ["yoga", "wellness", "outdoors", "morning"],
        image_url: "https://images.unsplash.com/photo-1599901860904-17e6ed7083a0?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80",
        additional_images: [],
      },
      {
        title: "Tech Startup Networking Mixer",
        description: "Connect with fellow entrepreneurs, investors, and tech enthusiasts in this casual networking event.",
        category: "Business & Professional",
        start_date: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days from now
        is_multi_day: false,
        venue_name: "91springboard",
        address: "Sector 44, Gurugram",
        city: "Gurugram",
        state: "Haryana",
        zip_code: "122003",
        organizer_id: organizerId,
        organizer_name: "Delhi Startup Hub",
        organizer_email: profiles[0].email,
        is_free: false,
        general_admission_price: 300,
        registration_deadline: new Date(Date.now() + 9 * 24 * 60 * 60 * 1000).toISOString(),
        max_attendees: 100,
        tags: ["tech", "startup", "networking", "business"],
        image_url: "https://images.unsplash.com/photo-1556761175-5973dc0f32e7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1632&q=80",
        additional_images: [],
      },
      {
        title: "Live Music Night: Indie Artists",
        description: "Enjoy an evening of live performances by talented independent artists from across Delhi NCR.",
        category: "Music",
        start_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days from now
        is_multi_day: false,
        venue_name: "The Piano Man Jazz Club",
        address: "B 6-7/22, Safdarjung Enclave Market",
        city: "New Delhi",
        state: "Delhi",
        zip_code: "110029",
        organizer_id: organizerId,
        organizer_name: "Delhi Music Collective",
        organizer_email: profiles[0].email,
        is_free: false,
        general_admission_price: 400,
        vip_ticket_price: 800,
        registration_deadline: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000).toISOString(),
        max_attendees: 120,
        tags: ["music", "live", "indie", "nightlife"],
        image_url: "https://images.unsplash.com/photo-1501612780327-45045538702b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80",
        additional_images: [],
      },
    ];
    
    // Insert sample events
    const { data: insertedEvents, error: insertError } = await supabase
      .from("events")
      .insert(sampleEvents)
      .select();
    
    if (insertError) {
      throw new Error(`Error inserting sample events: ${insertError.message}`);
    }
    
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: "Database seeded successfully", 
        data: {
          eventsInserted: insertedEvents?.length || 0,
          organizerId
        }
      }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200 
      }
    );
    
  } catch (error) {
    console.error("Error in seed function:", error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        message: error.message || "An error occurred while seeding the database" 
      }),
      { 
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500 
      }
    );
  }
});
