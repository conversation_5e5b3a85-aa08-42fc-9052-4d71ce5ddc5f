-- Migration: Add Payment System for Events
-- This migration adds comprehensive payment system support including:
-- 1. Event payment methods (UPI and Net Banking)
-- 2. Registration payment proofs and approval workflow
-- 3. Enhanced registration status management

-- Add payment method details to events table
ALTER TABLE public.events 
ADD COLUMN IF NOT EXISTS upi_id text,
ADD COLUMN IF NOT EXISTS upi_account_holder_name text,
ADD COLUMN IF NOT EXISTS bank_account_holder_name text,
ADD COLUMN IF NOT EXISTS bank_name text,
ADD COLUMN IF NOT EXISTS bank_account_number text,
ADD COLUMN IF NOT EXISTS bank_ifsc_code text,
ADD COLUMN IF NOT EXISTS payment_reference_message text;

-- Add comments for new payment columns
COMMENT ON COLUMN public.events.upi_id IS 'UPI ID (VPA) for UPI payments';
COMMENT ON COLUMN public.events.upi_account_holder_name IS 'Account holder name for UPI payments';
COMMENT ON COLUMN public.events.bank_account_holder_name IS 'Account holder name for bank transfers';
COMMENT ON COLUMN public.events.bank_name IS 'Bank name for direct transfers';
COMMENT ON COLUMN public.events.bank_account_number IS 'Bank account number for transfers';
COMMENT ON COLUMN public.events.bank_ifsc_code IS 'IFSC code for bank transfers';
COMMENT ON COLUMN public.events.payment_reference_message IS 'Reference message for payment tracking';

-- Enhance registrations table with payment proof and approval workflow
ALTER TABLE public.registrations 
ADD COLUMN IF NOT EXISTS registration_status text DEFAULT 'pending' NOT NULL,
ADD COLUMN IF NOT EXISTS payment_proof_url text,
ADD COLUMN IF NOT EXISTS payment_transaction_ref text,
ADD COLUMN IF NOT EXISTS payment_proof_uploaded_at timestamp with time zone,
ADD COLUMN IF NOT EXISTS reviewed_by uuid,
ADD COLUMN IF NOT EXISTS reviewed_at timestamp with time zone,
ADD COLUMN IF NOT EXISTS review_notes text,
ADD COLUMN IF NOT EXISTS approved_at timestamp with time zone;

-- Add constraint for registration status
ALTER TABLE public.registrations 
ADD CONSTRAINT registrations_status_check 
CHECK (registration_status IN ('pending', 'approved', 'declined', 'cancelled'));

-- Add comments for new registration columns
COMMENT ON COLUMN public.registrations.registration_status IS 'Registration approval status: pending, approved, declined, cancelled';
COMMENT ON COLUMN public.registrations.payment_proof_url IS 'URL to uploaded payment proof screenshot/document';
COMMENT ON COLUMN public.registrations.payment_transaction_ref IS 'Transaction reference number provided by participant';
COMMENT ON COLUMN public.registrations.payment_proof_uploaded_at IS 'Timestamp when payment proof was uploaded';
COMMENT ON COLUMN public.registrations.reviewed_by IS 'User ID of host who reviewed the registration';
COMMENT ON COLUMN public.registrations.reviewed_at IS 'Timestamp when registration was reviewed';
COMMENT ON COLUMN public.registrations.review_notes IS 'Notes added by host during review';
COMMENT ON COLUMN public.registrations.approved_at IS 'Timestamp when registration was approved';

-- Add foreign key constraint for reviewed_by
ALTER TABLE public.registrations 
ADD CONSTRAINT registrations_reviewed_by_fkey 
FOREIGN KEY (reviewed_by) REFERENCES public.profiles(id) ON DELETE SET NULL;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_registrations_status ON public.registrations(registration_status);
CREATE INDEX IF NOT EXISTS idx_registrations_event_status ON public.registrations(event_id, registration_status);
CREATE INDEX IF NOT EXISTS idx_registrations_reviewed_by ON public.registrations(reviewed_by);

-- Create function to automatically approve registrations for free events
CREATE OR REPLACE FUNCTION auto_approve_free_event_registrations()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if the event is free
  IF (SELECT is_free FROM public.events WHERE id = NEW.event_id) = true THEN
    -- Auto-approve registration for free events
    NEW.registration_status := 'approved';
    NEW.approved_at := NOW();
    NEW.payment_status := 'completed';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to auto-approve free event registrations
DROP TRIGGER IF EXISTS trigger_auto_approve_free_events ON public.registrations;
CREATE TRIGGER trigger_auto_approve_free_events
  BEFORE INSERT ON public.registrations
  FOR EACH ROW
  EXECUTE FUNCTION auto_approve_free_event_registrations();

-- Create function to update approved_at timestamp when status changes to approved
CREATE OR REPLACE FUNCTION update_registration_approved_at()
RETURNS TRIGGER AS $$
BEGIN
  -- Set approved_at when status changes to approved
  IF NEW.registration_status = 'approved' AND OLD.registration_status != 'approved' THEN
    NEW.approved_at := NOW();
  END IF;
  
  -- Clear approved_at if status changes from approved to something else
  IF NEW.registration_status != 'approved' AND OLD.registration_status = 'approved' THEN
    NEW.approved_at := NULL;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to manage approved_at timestamp
DROP TRIGGER IF EXISTS trigger_update_approved_at ON public.registrations;
CREATE TRIGGER trigger_update_approved_at
  BEFORE UPDATE ON public.registrations
  FOR EACH ROW
  EXECUTE FUNCTION update_registration_approved_at();

-- Update RLS policies for registrations to include payment proof management

-- Drop existing conflicting policies first
DROP POLICY IF EXISTS "Allow users to update payment proof" ON public.registrations;
DROP POLICY IF EXISTS "Allow organizers to review registrations" ON public.registrations;
DROP POLICY IF EXISTS "Allow org members to review org event registrations" ON public.registrations;

-- Allow users to update their own registration payment proofs
CREATE POLICY "Allow users to update payment proof" ON public.registrations
FOR UPDATE TO authenticated
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

-- Allow event organizers to update registration status and review
CREATE POLICY "Allow organizers to review registrations" ON public.registrations
FOR UPDATE TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.events
    WHERE events.id = registrations.event_id
    AND events.organizer_id = auth.uid()
  )
);

-- Allow organization members to review registrations for organization events
CREATE POLICY "Allow org members to review org event registrations" ON public.registrations
FOR UPDATE TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.events e
    JOIN public.organization_members om ON e.organization_id = om.organization_id
    WHERE e.id = registrations.event_id
    AND om.user_id = auth.uid()
    AND om.role IN ('owner', 'admin', 'event_manager')
    AND om.status = 'active'
  )
);

-- Create view for registration management (for hosts)
CREATE OR REPLACE VIEW registration_management AS
SELECT 
  r.id,
  r.event_id,
  r.user_id,
  r.registration_date,
  r.ticket_type,
  r.quantity,
  r.unit_price,
  r.total_amount,
  r.payment_status,
  r.payment_method,
  r.registration_status,
  r.payment_proof_url,
  r.payment_transaction_ref,
  r.payment_proof_uploaded_at,
  r.reviewed_by,
  r.reviewed_at,
  r.review_notes,
  r.approved_at,
  -- Event details
  e.title as event_title,
  e.is_free as event_is_free,
  e.organizer_id as event_organizer_id,
  e.organization_id as event_organization_id,
  -- User details
  p.full_name as user_name,
  p.email as user_email,
  p.phone_number as user_phone,
  -- Reviewer details
  reviewer.full_name as reviewed_by_name
FROM public.registrations r
JOIN public.events e ON r.event_id = e.id
JOIN public.profiles p ON r.user_id = p.id
LEFT JOIN public.profiles reviewer ON r.reviewed_by = reviewer.id;

-- Grant access to the view
GRANT SELECT ON registration_management TO authenticated;

-- Note: RLS policies cannot be applied to views, only to underlying tables
-- The view will inherit security from the underlying tables' RLS policies

-- Ensure registrations table has proper RLS policy for organizers
DROP POLICY IF EXISTS "Allow organizers to view registrations for their events" ON public.registrations;
CREATE POLICY "Allow organizers to view registrations for their events" ON public.registrations
FOR SELECT TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.events e
    WHERE e.id = event_id
    AND (
      e.organizer_id = auth.uid() OR
      (e.organization_id IS NOT NULL AND EXISTS (
        SELECT 1 FROM public.organization_members om
        WHERE om.organization_id = e.organization_id
        AND om.user_id = auth.uid()
        AND om.role IN ('owner', 'admin', 'event_manager')
        AND om.status = 'active'
      ))
    )
  )
);

-- Note: Views cannot have RLS enabled directly
-- Security is inherited from the underlying tables' RLS policies
