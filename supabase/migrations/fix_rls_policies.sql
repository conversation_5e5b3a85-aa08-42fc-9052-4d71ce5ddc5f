-- Fix RLS Policies for Payment System
-- This script fixes the RLS policy issues that prevent views from having policies

-- Note: Views cannot have RLS policies or RLS enabled
-- Security is inherited from the underlying tables

-- Ensure the registration_management view exists without RLS
DROP VIEW IF EXISTS registration_management;
CREATE VIEW registration_management AS
SELECT 
  r.id,
  r.event_id,
  r.user_id,
  r.registration_date,
  r.ticket_type,
  r.quantity,
  r.unit_price,
  r.total_amount,
  r.payment_status,
  r.payment_method,
  r.registration_status,
  r.payment_proof_url,
  r.payment_transaction_ref,
  r.payment_proof_uploaded_at,
  r.reviewed_by,
  r.reviewed_at,
  r.review_notes,
  r.approved_at,
  -- Event details
  e.title as event_title,
  e.is_free as event_is_free,
  e.organizer_id as event_organizer_id,
  e.organization_id as event_organization_id,
  -- User details
  p.full_name as user_name,
  p.email as user_email,
  p.phone_number as user_phone,
  -- Reviewer details
  reviewer.full_name as reviewed_by_name
FROM public.registrations r
LEFT JOIN public.events e ON r.event_id = e.id
LEFT JOIN public.profiles p ON r.user_id = p.id
LEFT JOIN public.profiles reviewer ON r.reviewed_by = reviewer.id;

-- Grant access to the view
GRANT SELECT ON registration_management TO authenticated;

-- Ensure proper RLS policies exist on the underlying registrations table
-- These policies will control access to the view data

-- Drop and recreate the organizer view policy
DROP POLICY IF EXISTS "Allow organizers to view registrations for their events" ON public.registrations;
CREATE POLICY "Allow organizers to view registrations for their events" ON public.registrations
FOR SELECT TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.events e
    WHERE e.id = event_id 
    AND (
      e.organizer_id = auth.uid() OR
      (e.organization_id IS NOT NULL AND EXISTS (
        SELECT 1 FROM public.organization_members om
        WHERE om.organization_id = e.organization_id 
        AND om.user_id = auth.uid() 
        AND om.role IN ('owner', 'admin', 'event_manager')
        AND om.status = 'active'
      ))
    )
  )
);

-- Ensure users can still view their own registrations
DROP POLICY IF EXISTS "Allow users to read their own registrations" ON public.registrations;
CREATE POLICY "Allow users to read their own registrations" ON public.registrations
FOR SELECT TO authenticated
USING (user_id = auth.uid());

-- Ensure users can create their own registrations
DROP POLICY IF EXISTS "Allow users to create their own registrations" ON public.registrations;
CREATE POLICY "Allow users to create their own registrations" ON public.registrations
FOR INSERT TO authenticated
WITH CHECK (user_id = auth.uid());

-- Allow users to update their own registration payment proofs
DROP POLICY IF EXISTS "Allow users to update payment proof" ON public.registrations;
CREATE POLICY "Allow users to update payment proof" ON public.registrations 
FOR UPDATE TO authenticated 
USING (user_id = auth.uid()) 
WITH CHECK (user_id = auth.uid());

-- Allow event organizers to update registration status and review
DROP POLICY IF EXISTS "Allow organizers to review registrations" ON public.registrations;
CREATE POLICY "Allow organizers to review registrations" ON public.registrations 
FOR UPDATE TO authenticated 
USING (
  EXISTS (
    SELECT 1 FROM public.events 
    WHERE events.id = registrations.event_id 
    AND events.organizer_id = auth.uid()
  )
);

-- Allow organization members to review registrations for organization events
DROP POLICY IF EXISTS "Allow org members to review org event registrations" ON public.registrations;
CREATE POLICY "Allow org members to review org event registrations" ON public.registrations 
FOR UPDATE TO authenticated 
USING (
  EXISTS (
    SELECT 1 FROM public.events e
    JOIN public.organization_members om ON e.organization_id = om.organization_id
    WHERE e.id = registrations.event_id 
    AND om.user_id = auth.uid()
    AND om.role IN ('owner', 'admin', 'event_manager')
    AND om.status = 'active'
  )
);

-- Verify RLS is enabled on the registrations table
ALTER TABLE public.registrations ENABLE ROW LEVEL SECURITY;
