-- Migration: Consolidate registration status and remove old payment_status
-- Timestamp: 20240718120000

BEGIN;

-- 1. Create the new ENUM type if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'registration_status_enum') THEN
        CREATE TYPE public.registration_status_enum AS ENUM (
            'payment-pending',
            'awaiting-confirmation',
            'accepted',
            'declined'
        );
    END IF;
END
$$;

-- 2. Add payment system columns if they don't exist (as a safeguard, user confirmed they exist)
ALTER TABLE public.registrations
    ADD COLUMN IF NOT EXISTS payment_proof_url TEXT,
    ADD COLUMN IF NOT EXISTS reviewed_by UUID REFERENCES public.profiles(id),
    ADD COLUMN IF NOT EXISTS reviewed_at TIMESTAMPTZ,
    ADD COLUMN IF NOT EXISTS review_notes TEXT;

-- 3. Ensure registration_status column exists with the correct ENUM type
-- This section handles adding the column if it doesn't exist, or altering it if it exists as TEXT.

-- Add a temporary column for data migration if registration_status is TEXT
ALTER TABLE public.registrations
    ADD COLUMN IF NOT EXISTS temp_migration_status public.registration_status_enum;

-- Variable to track if registration_status was originally TEXT
DO $$
DECLARE
    is_text_column BOOLEAN := FALSE;
BEGIN
    -- Check if registration_status exists and is TEXT
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'registrations' AND column_name = 'registration_status'
    ) THEN
        IF (SELECT data_type FROM information_schema.columns
            WHERE table_schema = 'public' AND table_name = 'registrations' AND column_name = 'registration_status'
           ) = 'text' THEN
            is_text_column := TRUE;
        END IF;
    END IF;

    -- Populate temp_migration_status based on old payment_status and old text registration_status
    -- This logic needs to run before altering/dropping the original registration_status or payment_status
    UPDATE public.registrations r
    SET temp_migration_status = CASE
        WHEN e.is_free THEN 'accepted'::public.registration_status_enum
        ELSE
            CASE
                WHEN r.payment_proof_url IS NOT NULL THEN 'awaiting-confirmation'::public.registration_status_enum
                WHEN r.payment_proof_url IS NULL THEN 'payment-pending'::public.registration_status_enum
                -- Fallbacks using payment_status if it exists
                WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='registrations' AND column_name='payment_status') AND r.payment_status = 'pending' AND r.payment_proof_url IS NULL THEN 'payment-pending'::public.registration_status_enum
                WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='registrations' AND column_name='payment_status') AND r.payment_status = 'pending' AND r.payment_proof_url IS NOT NULL THEN 'awaiting-confirmation'::public.registration_status_enum
                WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='registrations' AND column_name='payment_status') AND r.payment_status IN ('completed', 'paid') THEN 'accepted'::public.registration_status_enum
                WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='registrations' AND column_name='payment_status') AND r.payment_status = 'failed' THEN 'declined'::public.registration_status_enum
                -- Fallbacks using existing text registration_status if it was text
                WHEN is_text_column AND r.registration_status = 'approved' THEN 'accepted'::public.registration_status_enum
                WHEN is_text_column AND r.registration_status IN ('declined', 'cancelled') THEN 'declined'::public.registration_status_enum
                WHEN is_text_column AND r.registration_status = 'pending' AND r.payment_proof_url IS NOT NULL THEN 'awaiting-confirmation'::public.registration_status_enum
                WHEN is_text_column AND r.registration_status = 'pending' AND r.payment_proof_url IS NULL THEN 'payment-pending'::public.registration_status_enum
                ELSE 'payment-pending'::public.registration_status_enum -- Default for paid events
            END
    END
    FROM public.events e
    WHERE r.event_id = e.id AND r.temp_migration_status IS NULL; -- Only update if not already set

    -- Now, handle the registration_status column itself
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'registrations' AND column_name = 'registration_status'
    ) THEN
        -- If it exists, check its type
        IF (SELECT udt_name FROM information_schema.columns
            WHERE table_schema = 'public' AND table_name = 'registrations' AND column_name = 'registration_status'
           ) != 'registration_status_enum' THEN
            -- Type is different (e.g., text), so drop and recreate carefully
            ALTER TABLE public.registrations DROP COLUMN registration_status;
            ALTER TABLE public.registrations ADD COLUMN registration_status public.registration_status_enum;
        END IF;
        -- If it was already the correct enum, no structural change needed here for the column itself.
    ELSE
        -- Column doesn't exist, so add it
        ALTER TABLE public.registrations ADD COLUMN registration_status public.registration_status_enum;
    END IF;

    -- Copy values from temp_migration_status to the final registration_status column
    UPDATE public.registrations SET registration_status = temp_migration_status WHERE temp_migration_status IS NOT NULL;

END
$$;

-- Drop the temporary migration column
ALTER TABLE public.registrations DROP COLUMN IF EXISTS temp_migration_status;

-- 4. Recreate the registration_management view to remove payment_status
-- and ensure it uses the new registration_status column from the registrations table.
DROP VIEW IF EXISTS public.registration_management;

CREATE VIEW public.registration_management AS
SELECT
    r.id,
    r.event_id,
    r.user_id,
    r.registration_date,
    r.ticket_type,
    r.quantity,
    r.unit_price,
    r.total_amount,
    r.payment_method,
    r.registration_status, -- This is the new ENUM based column
    r.payment_proof_url,
    r.payment_transaction_ref,
    r.payment_proof_uploaded_at,
    r.reviewed_by,
    r.reviewed_at,
    r.review_notes,
    r.approved_at, -- Assuming this column exists on registrations table
    e.title AS event_title,
    e.is_free AS event_is_free,
    e.organizer_id AS event_organizer_id,
    e.organization_id AS event_organization_id,
    u.full_name AS user_name,
    u.email AS user_email,
    u.phone_number AS user_phone,
    rev.full_name AS reviewed_by_name
FROM
    public.registrations r
JOIN
    public.events e ON r.event_id = e.id
LEFT JOIN
    public.profiles u ON r.user_id = u.id
LEFT JOIN
    public.profiles rev ON r.reviewed_by = rev.id;

-- 5. Drop the old payment_status column if it exists (now safe after view update)
ALTER TABLE public.registrations
    DROP COLUMN IF EXISTS payment_status;

-- 6. Set a default for new rows (optional, application can also handle this)
-- ALTER TABLE public.registrations ALTER COLUMN registration_status SET DEFAULT 'payment-pending'::public.registration_status_enum;

-- 7. Update RLS Policies for public.registrations

-- Policy: Allow users to update their own 'payment-pending' registrations (e.g. for payment proof)
-- The application logic in useRegistrations.ts handles the status transition to 'awaiting-confirmation'.
-- This policy ensures users can only make updates (like adding a proof URL) if the registration is in 'payment-pending'.
DROP POLICY IF EXISTS "Allow users to update their own registrations" ON public.registrations;
CREATE POLICY "Allow users to update their own 'payment-pending' registrations"
    ON public.registrations
    FOR UPDATE TO authenticated
    USING (
        user_id = auth.uid() AND
        (SELECT registration_status FROM public.registrations WHERE id = registrations.id) = 'payment-pending'::public.registration_status_enum
    )
    WITH CHECK (
        user_id = auth.uid()
        -- Check that if proof is provided, status becomes 'awaiting-confirmation'
        -- or if no proof provided yet, it can remain 'payment-pending'
        AND (
            (payment_proof_url IS NOT NULL AND registration_status = 'awaiting-confirmation'::public.registration_status_enum) OR
            (payment_proof_url IS NULL AND registration_status = 'payment-pending'::public.registration_status_enum)
        )
    );

-- Policy: Allow event organizers to update registration status for review
-- Allows organizers to change status from 'awaiting-confirmation' to 'accepted' or 'declined'.
DROP POLICY IF EXISTS "Allow event organizers to update registration status" ON public.registrations;
CREATE POLICY "Allow event organizers to update registration status"
    ON public.registrations
    FOR UPDATE TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.events e
            WHERE e.id = registrations.event_id AND e.organizer_id = auth.uid()
        ) AND
        (SELECT registration_status FROM public.registrations WHERE id = registrations.id) = 'awaiting-confirmation'::public.registration_status_enum
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.events e
            WHERE e.id = registrations.event_id AND e.organizer_id = auth.uid()
        ) AND
        registration_status IN ('accepted'::public.registration_status_enum, 'declined'::public.registration_status_enum)
    );

-- Note: Policies for SELECT, INSERT, DELETE from db_schema.sql are assumed to be generally fine,
-- unless specific status-based restrictions are needed for them (e.g., restricting delete based on status).
-- The user has not requested changes to delete logic beyond removing 'cancelled' status.

COMMIT;
