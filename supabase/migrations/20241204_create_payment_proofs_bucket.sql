-- Create storage bucket for payment proofs
INSERT INTO storage.buckets (id, name, public)
VALUES ('payment-proofs', 'payment-proofs', true)
ON CONFLICT (id) DO NOTHING;

-- Create RLS policies for payment-proofs bucket
CREATE POLICY "Allow authenticated users to upload payment proofs" ON storage.objects
FOR INSERT TO authenticated
WITH CHECK (bucket_id = 'payment-proofs');

CREATE POLICY "Allow users to view their own payment proofs" ON storage.objects
FOR SELECT TO authenticated
USING (bucket_id = 'payment-proofs' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Allow event organizers to view payment proofs" ON storage.objects
FOR SELECT TO authenticated
USING (
  bucket_id = 'payment-proofs' AND
  EXISTS (
    SELECT 1 FROM public.registrations r
    JOIN public.events e ON r.event_id = e.id
    WHERE r.payment_proof_url LIKE '%' || name || '%'
    AND (e.organizer_id = auth.uid() OR 
         EXISTS (
           SELECT 1 FROM public.organization_members om
           WHERE om.organization_id = e.organization_id
           AND om.user_id = auth.uid()
           AND om.role IN ('owner', 'admin', 'event_manager')
           AND om.status = 'active'
         ))
  )
);

CREATE POLICY "Allow users to update their own payment proofs" ON storage.objects
FOR UPDATE TO authenticated
USING (bucket_id = 'payment-proofs' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Allow users to delete their own payment proofs" ON storage.objects
FOR DELETE TO authenticated
USING (bucket_id = 'payment-proofs' AND auth.uid()::text = (storage.foldername(name))[1]);
