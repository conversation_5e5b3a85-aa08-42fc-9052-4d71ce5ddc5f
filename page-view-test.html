<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page View Test - TheLocalAdda</title>
    
    <!-- Google tag (gtag.js) with send_page_view disabled -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-K5ERWJ7N4Y"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      // Disable automatic page view tracking
      gtag('config', 'G-K5ERWJ7N4Y', {
        send_page_view: false
      });
    </script>
    
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 600px; 
            margin: 50px auto; 
            padding: 20px;
        }
        button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .log {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔍 Page View Double-Count Test</h1>
    <p>This page tests if page views are being counted correctly (only once).</p>
    
    <button onclick="sendManualPageView()">📄 Send Manual Page View</button>
    <button onclick="clearLog()">🧹 Clear Log</button>
    
    <div class="log" id="log"></div>
    
    <h3>Instructions:</h3>
    <ol>
        <li>Open Google Analytics Real-time reports</li>
        <li>Watch the page view count when you load this page</li>
        <li>It should only count as 1 page view (not 2)</li>
        <li>Click "Send Manual Page View" to test manual tracking</li>
    </ol>

    <script>
        let pageViewCount = 0;
        
        function log(message) {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logEl.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(message);
        }
        
        function sendManualPageView() {
            pageViewCount++;
            
            if (typeof gtag === 'function') {
                gtag('event', 'page_view', {
                    page_title: document.title,
                    page_location: window.location.href,
                    custom_parameter: `manual_${pageViewCount}`
                });
                log(`✅ Manual page view #${pageViewCount} sent`);
            } else {
                log('❌ gtag not available');
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Send initial page view manually (simulating React app behavior)
        window.addEventListener('load', function() {
            setTimeout(() => {
                if (typeof gtag === 'function') {
                    sendManualPageView();
                    log('🚀 Initial page view sent (should be the only automatic one)');
                } else {
                    log('❌ gtag not loaded yet');
                }
            }, 1000);
        });
        
        log('📄 Page loaded - waiting for gtag...');
    </script>
</body>
</html>
