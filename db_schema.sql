SET 
  statement_timeout = 0;
SET 
  lock_timeout = 0;
SET 
  idle_in_transaction_session_timeout = 0;
SET 
  client_encoding = 'UTF8';
SET 
  standard_conforming_strings = on;
SELECT 
  pg_catalog.set_config('search_path', '', false);
SET 
  check_function_bodies = false;
SET 
  xmloption = content;
SET 
  client_min_messages = warning;
SET 
  row_security = off;
CREATE EXTENSION IF NOT EXISTS "pgsodium";
COMMENT ON SCHEMA "public" IS 'standard public schema';
CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";
CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";
CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";
CREATE EXTENSION IF NOT EXISTS "postgis" WITH SCHEMA "extensions";
CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";
CREATE OR REPLACE FUNCTION "public"."handle_new_organization"()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Create owner membership for the organization creator
  INSERT INTO public.organization_members (organization_id, user_id, role, joined_at)
  VALUES (NEW.id, NEW.created_by, 'owner', NOW());

  RETURN NEW;
END;
$$;

CREATE
OR REPLACE FUNCTION "public"."find_events"(
  "featured" boolean DEFAULT false, "include_past_events" boolean DEFAULT false, 
  "limit_count" integer DEFAULT 12, 
  "user_id" "uuid" DEFAULT NULL :: "uuid"
) RETURNS SETOF "json" LANGUAGE "plpgsql" SECURITY DEFINER AS $$ DECLARE current_date_time timestamp;
BEGIN current_date_time := NOW();
RETURN QUERY 
SELECT 
  json_build_object(
    'id', 
    e.id, 
    'title', 
    e.title, 
    'description', 
    e.description, 
    'tags', 
    e.tags, 
    'image_url', 
    e.image_url, 
    'start_date', 
    e.start_date, 
    'end_date', 
    e.end_date, 
    'is_multi_day', 
    e.is_multi_day, 
    'max_attendees', 
    e.max_attendees, 
    'registration_deadline', 
    e.registration_deadline, 
    'parking_instructions', 
    e.parking_instructions, 
    'organizer_id', 
    e.organizer_id, 
    'organizer_name', 
    e.organizer_name, 
    'organizer_email', 
    e.organizer_email, 
    'organizer_phone', 
    e.organizer_phone, 
    'website_url', 
    e.website_url, 
    'is_free', 
    e.is_free, 
    'general_admission_price', 
    e.general_admission_price, 
    'vip_ticket_price', 
    e.vip_ticket_price, 
    'has_early_bird', 
    e.has_early_bird, 
    'early_bird_deadline', 
    e.early_bird_deadline, 
    'group_discount', 
    e.group_discount, 
    'payment_methods', 
    e.payment_methods, 
    'refund_policy', 
    e.refund_policy, 
    'created_at', 
    e.created_at, 
    'updated_at', 
    e.updated_at, 
    'approval_status', 
    e.approval_status, 
    'approval_notes', 
    e.approval_notes, 
    'additional_images', 
    e.additional_images, 
    'category_id', 
    e.category_id, 
    'venue_id', 
    e.venue_id, 
    'category', 
    (
      SELECT 
        json_build_object(
          'id', c.id, 'name', c.name, 'description', 
          c.description, 'color', c.color, 
          'text_color', c.text_color, 'icon', 
          c.icon, 'priority', c.priority, 'active', 
          c.active, 'created_at', c.created_at, 
          'updated_at', c.updated_at
        ) 
      FROM 
        event_categories c 
      WHERE 
        c.id = e.category_id
    ), 
    'venue', 
    (
      SELECT 
        json_build_object(
          'id', v.id, 'name', v.name, 'address', 
          v.address, 'city', v.city, 'state', 
          v.state, 'zip_code', v.zip_code, 
          'created_at', v.created_at, 'updated_at', 
          v.updated_at, 'priority', v.priority
        ) 
      FROM 
        event_venues v 
      WHERE 
        v.id = e.venue_id
    ), 
    'registrations', 
    (
      SELECT 
        json_agg(
          json_build_object('quantity', r.quantity)
        ) 
      FROM 
        registrations r 
      WHERE 
        r.event_id = e.id
    )
  ) 
FROM 
  events e 
WHERE 
  e.approval_status = 'approved' 
  AND (
    include_past_events = true 
    OR (
      (
        e.end_date IS NULL 
        AND e.start_date >= current_date_time
      ) 
      OR (
        e.end_date IS NOT NULL 
        AND e.end_date >= current_date_time
      )
    )
  ) -- Remove the featured filter since is_featured doesn't exist
  -- If featured is requested, we'll just return all approved events for now
ORDER BY 
  e.start_date ASC 
LIMIT 
  limit_count;
END;
$$;
ALTER FUNCTION "public"."find_events"(
  "featured" boolean, "include_past_events" boolean, 
  "limit_count" integer, "user_id" "uuid"
) OWNER TO "postgres";
CREATE 
OR REPLACE FUNCTION "public"."find_events_within_radius"(
  "search_lat" double precision, "search_lng" double precision, 
  "radius_km" double precision DEFAULT 2, 
  "date_start" timestamp with time zone DEFAULT NULL :: timestamp with time zone, 
  "date_end" timestamp with time zone DEFAULT NULL :: timestamp with time zone, 
  "filter_category_id" "uuid" DEFAULT NULL :: "uuid", 
  "search_term" "text" DEFAULT NULL :: "text", 
  "include_past_events" boolean DEFAULT false, 
  "user_id" "uuid" DEFAULT NULL :: "uuid", 
  "result_limit" integer DEFAULT NULL :: integer, 
  "result_offset" integer DEFAULT 0
) RETURNS TABLE(
  "id" "uuid", "title" "text", "description" "text", 
  "tags" "text" [], "image_url" "text", 
  "start_date" timestamp with time zone, 
  "end_date" timestamp with time zone, 
  "is_multi_day" boolean, "max_attendees" integer, 
  "is_free" boolean, "general_admission_price" numeric, 
  "approval_status" "text", "category_id" "uuid", 
  "venue_id" "uuid", "category_name" "text", 
  "category_color" "text", "category_text_color" "text", 
  "category_icon" "text", "venue_name" "text", 
  "venue_address" "text", "venue_city" "text", 
  "venue_state" "text", "venue_zip_code" "text", 
  "venue_locality" "text", "venue_place_id" "text", 
  "venue_priority" integer, "distance_km" double precision, 
  "registrations" "jsonb"
) LANGUAGE "plpgsql" SECURITY DEFINER AS $$ BEGIN RETURN QUERY 
SELECT 
  e.id, 
  e.title, 
  e.description, 
  e.tags, 
  e.image_url, 
  e.start_date, 
  e.end_date, 
  e.is_multi_day, 
  e.max_attendees, 
  e.is_free, 
  e.general_admission_price, 
  e.approval_status, 
  e.category_id, 
  e.venue_id, 
  c.name as category_name, 
  c.color as category_color, 
  c.text_color as category_text_color, 
  c.icon as category_icon, 
  v.name as venue_name, 
  v.address as venue_address, 
  v.city as venue_city, 
  v.state as venue_state, 
  v.zip_code as venue_zip_code, 
  v.locality as venue_locality, 
  v.place_id as venue_place_id, 
  v.priority as venue_priority, 
  ST_Distance(
    v.location :: geography, 
    ST_SetSRID(
      ST_MakePoint(search_lng, search_lat), 
      4326
    ):: geography
  ) / 1000 AS distance_km, 
  -- Get registrations as a JSON array
  COALESCE(
    (
      SELECT 
        jsonb_agg(
          jsonb_build_object('quantity', r.quantity)
        ) 
      FROM 
        registrations r 
      WHERE 
        r.event_id = e.id
    ), 
    '[]' :: jsonb
  ) as registrations 
FROM 
  events e 
  JOIN event_venues v ON e.venue_id = v.id 
  LEFT JOIN event_categories c ON e.category_id = c.id 
WHERE 
  -- Filter by distance
  ST_DWithin(
    v.location :: geography, 
    ST_SetSRID(
      ST_MakePoint(search_lng, search_lat), 
      4326
    ):: geography, 
    radius_km * 1000 -- Convert km to meters
    ) -- Filter by date range if provided
  AND (
    date_start IS NULL 
    OR e.start_date >= date_start
  ) 
  AND (
    date_end IS NULL 
    OR e.start_date <= date_end
  ) -- Filter by category if provided
  AND (
    filter_category_id IS NULL 
    OR e.category_id = filter_category_id
  ) -- Filter by search term if provided
  AND (
    search_term IS NULL 
    OR e.title ILIKE '%' || search_term || '%' 
    OR e.description ILIKE '%' || search_term || '%' 
    OR search_term = ANY(e.tags)
  ) -- Handle past events
  AND (
    (
      include_past_events = true 
      AND e.start_date < NOW()
    ) 
    OR (
      include_past_events = false 
      AND e.start_date >= NOW()
    )
  ) -- Handle approval status
  AND (
    e.approval_status = 'approved' 
    OR (
      user_id IS NOT NULL 
      AND e.organizer_id = user_id 
      AND e.approval_status = 'pending'
    )
  ) 
ORDER BY 
  -- Sort by venue priority first (higher priority first)
  v.priority DESC, 
  -- Then by start date (ascending for upcoming, descending for past)
  CASE WHEN include_past_events THEN e.start_date END DESC, 
  CASE WHEN NOT include_past_events THEN e.start_date END ASC, 
  -- Then by distance (closest first)
  distance_km ASC 
LIMIT 
  CASE WHEN result_limit IS NULL THEN NULL ELSE result_limit END OFFSET result_offset;
END;
$$;
ALTER FUNCTION "public"."find_events_within_radius"(
  "search_lat" double precision, "search_lng" double precision, 
  "radius_km" double precision, "date_start" timestamp with time zone, 
  "date_end" timestamp with time zone, 
  "filter_category_id" "uuid", "search_term" "text", 
  "include_past_events" boolean, "user_id" "uuid", 
  "result_limit" integer, "result_offset" integer
) OWNER TO "postgres";
CREATE 
OR REPLACE FUNCTION "public"."find_venues_within_radius"(
  "search_lat" double precision, "search_lng" double precision, 
  "radius_km" double precision DEFAULT 2, 
  "result_limit" integer DEFAULT NULL :: integer
) RETURNS TABLE(
  "id" "uuid", "name" "text", "address" "text", 
  "city" "text", "state" "text", "zip_code" "text", 
  "locality" "text", "place_id" "text", 
  "created_at" timestamp with time zone, 
  "updated_at" timestamp with time zone, 
  "priority" integer, "distance_km" double precision
) LANGUAGE "plpgsql" SECURITY DEFINER AS $$ BEGIN RETURN QUERY 
SELECT 
  v.id, 
  v.name, 
  v.address, 
  v.city, 
  v.state, 
  v.zip_code, 
  v.locality, 
  v.place_id, 
  v.created_at, 
  v.updated_at, 
  v.priority, 
  ST_Distance(
    v.location :: geography, 
    ST_SetSRID(
      ST_MakePoint(search_lng, search_lat), 
      4326
    ):: geography
  ) / 1000 AS distance_km 
FROM 
  event_venues v 
WHERE 
  ST_DWithin(
    v.location :: geography, 
    ST_SetSRID(
      ST_MakePoint(search_lng, search_lat), 
      4326
    ):: geography, 
    radius_km * 1000
  ) 
ORDER BY 
  v.priority DESC, 
  -- Sort by priority descending (higher priority first)
  distance_km ASC -- Then sort by distance (closest first)
LIMIT 
  CASE WHEN result_limit IS NULL THEN NULL ELSE result_limit END;
END;
$$;
ALTER FUNCTION "public"."find_venues_within_radius"(
  "search_lat" double precision, "search_lng" double precision, 
  "radius_km" double precision, "result_limit" integer
) OWNER TO "postgres";
CREATE 
OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER AS $$ BEGIN INSERT INTO public.profiles (
  id, username, full_name, avatar_url, 
  email
) 
VALUES 
  (
    NEW.id, 
    COALESCE(
      NEW.raw_user_meta_data ->> 'name', 
      NEW.email
    ), 
    NEW.raw_user_meta_data ->> 'full_name', 
    NEW.raw_user_meta_data ->> 'avatar_url', 
    NEW.email
  );
RETURN NEW;
END;
$$;
ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";
SET 
  default_tablespace = '';
SET 
  default_table_access_method = "heap";
CREATE TABLE IF NOT EXISTS "public"."event_categories" (
  "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL, 
  "name" "text" NOT NULL, 
  "description" "text", 
  "color" "text" NOT NULL, 
  "text_color" "text" NOT NULL, 
  "priority" integer DEFAULT 0 NOT NULL, 
  "icon" "text", 
  "active" boolean DEFAULT true NOT NULL, 
  "created_at" timestamp with time zone DEFAULT "now"() NOT NULL, 
  "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);
ALTER TABLE 
  "public"."event_categories" OWNER TO "postgres";
CREATE TABLE IF NOT EXISTS "public"."event_venues" (
  "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL, 
  "name" "text" NOT NULL, 
  "address" "text" NOT NULL, 
  "city" "text" NOT NULL, 
  "state" "text" NOT NULL, 
  "zip_code" "text", 
  "created_at" timestamp with time zone DEFAULT "now"() NOT NULL, 
  "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL, 
  "priority" integer DEFAULT 0 NOT NULL, 
  "location" "extensions"."geography"(Point, 4326), 
  "locality" "text", 
  "place_id" "text"
);
ALTER TABLE 
  "public"."event_venues" OWNER TO "postgres";
COMMENT ON COLUMN "public"."event_venues"."locality" IS 'Stores the sublocality_level_1 value from Google Places API (e.g., neighborhood or district)';
COMMENT ON COLUMN "public"."event_venues"."place_id" IS 'Stores the Google Places ID for precise location linking and additional data retrieval';
CREATE TABLE IF NOT EXISTS "public"."events" (
  "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL, 
  "title" "text" NOT NULL, 
  "description" "text" NOT NULL, 
  "tags" "text" [] DEFAULT '{}' :: "text" [], 
  "image_url" "text", 
  "start_date" timestamp with time zone NOT NULL, 
  "end_date" timestamp with time zone, 
  "is_multi_day" boolean DEFAULT false, 
  "max_attendees" integer, 
  "registration_deadline" timestamp with time zone, 
  "parking_instructions" "text", 
  "organizer_id" "uuid" NOT NULL,
  "organizer_name" "text" NOT NULL,
  "organizer_email" "text",
  "organizer_phone" "text",
  "website_url" "text",
  "organization_id" "uuid",
  "hosted_by_type" "text" DEFAULT 'individual' :: "text" NOT NULL,
  "is_free" boolean DEFAULT true, 
  "general_admission_price" numeric(10, 2), 
  "vip_ticket_price" numeric(10, 2), 
  "has_early_bird" boolean DEFAULT false, 
  "early_bird_deadline" timestamp with time zone, 
  "group_discount" boolean DEFAULT false, 
  "payment_methods" "text" [], 
  "refund_policy" "text", 
  "created_at" timestamp with time zone DEFAULT "now"() NOT NULL, 
  "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL, 
  "approval_status" "text" DEFAULT 'pending' :: "text" NOT NULL, 
  "approval_notes" "text", 
  "additional_images" "text" [], 
  "category_id" "uuid", 
  "venue_id" "uuid" NOT NULL, 
  "poster_image_filename" "text", 
  CONSTRAINT "events_approval_status_check" CHECK (
    (
      "approval_status" = ANY (
        ARRAY[ 'pending' :: "text", 'approved' :: "text", 
        'rejected' :: "text" ]
      )
    )
  )
);
ALTER TABLE 
  "public"."events" OWNER TO "postgres";
COMMENT ON TABLE "public"."events" IS 'Events table with venue relationship. Venue details are now stored in the event_venues table.';
COMMENT ON COLUMN "public"."events"."poster_image_filename" IS 'Stores the filename of the generated event poster image in the event-posters bucket';
CREATE TABLE IF NOT EXISTS "public"."profiles" (
  "id" "uuid" NOT NULL, 
  "username" "text", 
  "full_name" "text", 
  "avatar_url" "text", 
  "email" "text", 
  "created_at" timestamp with time zone DEFAULT "now"() NOT NULL, 
  "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL, 
  "is_admin" boolean DEFAULT false NOT NULL, 
  "phone_number" "text", 
  "location" "extensions"."geography"(Point, 4326), 
  "search_radius_km" integer DEFAULT 2, 
  "location_updated_at" timestamp with time zone, 
  "locality_short_name" character varying(100)
);
ALTER TABLE 
  "public"."profiles" OWNER TO "postgres";
COMMENT ON COLUMN "public"."profiles"."location_updated_at" IS 'Timestamp when the user location was last updated';
CREATE TABLE IF NOT EXISTS "public"."organizations" (
  "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
  "name" "text" NOT NULL,
  "description" "text",
  "logo_url" "text",
  "website_url" "text",
  "contact_email" "text",
  "contact_phone" "text",
  "address" "text",
  "city" "text",
  "state" "text",
  "zip_code" "text",
  "locality" "text",
  "place_id" "text",
  "location" "extensions"."geography"(Point, 4326),
  "approval_status" "text" DEFAULT 'pending' :: "text" NOT NULL,
  "approval_notes" "text",
  "created_by" "uuid" NOT NULL,
  "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
  "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);

ALTER TABLE "public"."organizations" OWNER TO "postgres";
COMMENT ON TABLE "public"."organizations" IS 'Organizations that can host events. Includes approval workflow similar to events.';
COMMENT ON COLUMN "public"."organizations"."locality" IS 'Stores the sublocality_level_1 value from Google Places API (e.g., neighborhood or district)';
COMMENT ON COLUMN "public"."organizations"."place_id" IS 'Stores the Google Places ID for precise location linking and additional data retrieval';
COMMENT ON COLUMN "public"."organizations"."approval_status" IS 'Approval status: pending, approved, or rejected';

CREATE TABLE IF NOT EXISTS "public"."organization_members" (
  "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
  "organization_id" "uuid" NOT NULL,
  "user_id" "uuid" NOT NULL,
  "role" "text" DEFAULT 'member' :: "text" NOT NULL,
  "invited_by" "uuid",
  "invited_at" timestamp with time zone,
  "joined_at" timestamp with time zone DEFAULT "now"() NOT NULL,
  "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
  "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);

ALTER TABLE "public"."organization_members" OWNER TO "postgres";
COMMENT ON TABLE "public"."organization_members" IS 'Manages user membership and roles within organizations';
COMMENT ON COLUMN "public"."organization_members"."role" IS 'User role: owner, admin, event_manager, or member';

CREATE TABLE IF NOT EXISTS "public"."registrations" (
  "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
  "event_id" "uuid" NOT NULL,
  "user_id" "uuid" NOT NULL,
  "registration_date" timestamp with time zone DEFAULT "now"() NOT NULL,
  "ticket_type" "text" DEFAULT 'general' :: "text",
  "quantity" integer DEFAULT 1,
  "unit_price" numeric(10, 2),
  "total_amount" numeric(10, 2),
  "payment_status" "text" DEFAULT 'pending' :: "text",
  "payment_method" "text"
);
ALTER TABLE 
  "public"."registrations" OWNER TO "postgres";
ALTER TABLE 
  ONLY "public"."event_categories" 
ADD 
  CONSTRAINT "event_categories_name_key" UNIQUE ("name");
ALTER TABLE 
  ONLY "public"."event_categories" 
ADD 
  CONSTRAINT "event_categories_pkey" PRIMARY KEY ("id");
ALTER TABLE 
  ONLY "public"."event_venues" 
ADD 
  CONSTRAINT "event_venues_name_address_city_key" UNIQUE ("name", "address", "city");
ALTER TABLE 
  ONLY "public"."event_venues" 
ADD 
  CONSTRAINT "event_venues_name_key" UNIQUE ("name");
ALTER TABLE 
  ONLY "public"."event_venues" 
ADD 
  CONSTRAINT "event_venues_pkey" PRIMARY KEY ("id");
ALTER TABLE 
  ONLY "public"."events" 
ADD 
  CONSTRAINT "events_pkey" PRIMARY KEY ("id");
ALTER TABLE 
  ONLY "public"."profiles" 
ADD 
  CONSTRAINT "profiles_pkey" PRIMARY KEY ("id");
ALTER TABLE 
  ONLY "public"."registrations" 
ADD 
  CONSTRAINT "registrations_event_id_user_id_key" UNIQUE ("event_id", "user_id");
ALTER TABLE
  ONLY "public"."registrations"
ADD
  CONSTRAINT "registrations_pkey" PRIMARY KEY ("id");
ALTER TABLE
  ONLY "public"."organizations"
ADD
  CONSTRAINT "organizations_pkey" PRIMARY KEY ("id");
ALTER TABLE
  ONLY "public"."organizations"
ADD
  CONSTRAINT "organizations_name_key" UNIQUE ("name");
ALTER TABLE
  ONLY "public"."organization_members"
ADD
  CONSTRAINT "organization_members_pkey" PRIMARY KEY ("id");
ALTER TABLE
  ONLY "public"."organization_members"
ADD
  CONSTRAINT "organization_members_org_user_key" UNIQUE ("organization_id", "user_id");
CREATE INDEX "idx_event_venues_location" ON "public"."event_venues" USING "gist" ("location");
CREATE INDEX "idx_profiles_location" ON "public"."profiles" USING "gist" ("location");
CREATE INDEX "idx_registrations_user_id" ON "public"."registrations" USING "btree" ("user_id");
CREATE INDEX "idx_organizations_location" ON "public"."organizations" USING "gist" ("location");
CREATE INDEX "idx_organizations_created_by" ON "public"."organizations" USING "btree" ("created_by");
CREATE INDEX "idx_organization_members_org_id" ON "public"."organization_members" USING "btree" ("organization_id");
CREATE INDEX "idx_organization_members_user_id" ON "public"."organization_members" USING "btree" ("user_id");
CREATE INDEX "idx_events_organization_id" ON "public"."events" USING "btree" ("organization_id");
ALTER TABLE 
  ONLY "public"."events" 
ADD 
  CONSTRAINT "events_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."event_categories"("id") ON DELETE 
SET 
  NULL;
ALTER TABLE 
  ONLY "public"."events" 
ADD 
  CONSTRAINT "events_organizer_id_fkey" FOREIGN KEY ("organizer_id") REFERENCES "public"."profiles"("id");
ALTER TABLE 
  ONLY "public"."events" 
ADD 
  CONSTRAINT "fk_events_venue" FOREIGN KEY ("venue_id") REFERENCES "public"."event_venues"("id") ON DELETE RESTRICT;
ALTER TABLE 
  ONLY "public"."profiles" 
ADD 
  CONSTRAINT "profiles_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;
ALTER TABLE 
  ONLY "public"."registrations" 
ADD 
  CONSTRAINT "registrations_event_id_fkey" FOREIGN KEY ("event_id") REFERENCES "public"."events"("id") ON DELETE CASCADE;
ALTER TABLE
  ONLY "public"."registrations"
ADD
  CONSTRAINT "registrations_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;
ALTER TABLE
  ONLY "public"."organizations"
ADD
  CONSTRAINT "organizations_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;
ALTER TABLE
  ONLY "public"."organization_members"
ADD
  CONSTRAINT "organization_members_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE CASCADE;
ALTER TABLE
  ONLY "public"."organization_members"
ADD
  CONSTRAINT "organization_members_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles"("id") ON DELETE CASCADE;
ALTER TABLE
  ONLY "public"."organization_members"
ADD
  CONSTRAINT "organization_members_invited_by_fkey" FOREIGN KEY ("invited_by") REFERENCES "public"."profiles"("id") ON DELETE SET NULL;
ALTER TABLE
  ONLY "public"."events"
ADD
  CONSTRAINT "events_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE SET NULL;
CREATE POLICY "Admins can update events" ON "public"."events" FOR 
UPDATE 
  USING (
    (
      (
        SELECT 
          "profiles"."is_admin" 
        FROM 
          "public"."profiles" 
        WHERE 
          (
            "profiles"."id" = "auth"."uid"()
          )
      ) = true
    )
  );
CREATE POLICY "Admins can view all events" ON "public"."events" FOR 
SELECT 
  USING (
    (
      (
        SELECT 
          "profiles"."is_admin" 
        FROM 
          "public"."profiles" 
        WHERE 
          (
            "profiles"."id" = "auth"."uid"()
          )
      ) = true
    )
  );
CREATE POLICY "Allow anyone to read events" ON "public"."events" FOR 
SELECT 
  USING (true);
CREATE POLICY "Allow anyone to read profiles" ON "public"."profiles" FOR 
SELECT 
  USING (true);
CREATE POLICY "Allow authenticated users to create events" ON "public"."events" FOR INSERT TO "authenticated" WITH CHECK (
  (
    "organizer_id" = "auth"."uid"()
  )
);
CREATE POLICY "Allow event creators to delete their events" ON "public"."events" FOR DELETE TO "authenticated" USING (
  (
    "organizer_id" = "auth"."uid"()
  )
);
CREATE POLICY "Allow event creators to modify their events" ON "public"."events" FOR 
UPDATE 
  TO "authenticated" USING (
    (
      "organizer_id" = "auth"."uid"()
    )
  );
CREATE POLICY "Allow event organizers to view registrations" ON "public"."registrations" FOR 
SELECT 
  TO "authenticated" USING (
    (
      EXISTS (
        SELECT 
          1 
        FROM 
          "public"."events" 
        WHERE 
          (
            (
              "events"."id" = "registrations"."event_id"
            ) 
            AND (
              "events"."organizer_id" = "auth"."uid"()
            )
          )
      )
    )
  );
CREATE POLICY "Allow users to create their own registrations" ON "public"."registrations" FOR INSERT TO "authenticated" WITH CHECK (
  (
    "user_id" = "auth"."uid"()
  )
);
CREATE POLICY "Allow users to delete their own registrations" ON "public"."registrations" FOR DELETE TO "authenticated" USING (
  (
    "user_id" = "auth"."uid"()
  )
);
CREATE POLICY "Allow users to read their own registrations" ON "public"."registrations" FOR 
SELECT 
  TO "authenticated" USING (
    (
      "user_id" = "auth"."uid"()
    )
  );
CREATE POLICY "Allow users to update their own profiles" ON "public"."profiles" FOR 
UPDATE 
  TO "authenticated" USING (
    (
      "id" = "auth"."uid"()
    )
  );
CREATE POLICY "Allow users to update their own registrations" ON "public"."registrations" FOR 
UPDATE 
  TO "authenticated" USING (
    (
      "user_id" = "auth"."uid"()
    )
  );
CREATE POLICY "Anyone can create venues" ON "public"."event_venues" FOR INSERT WITH CHECK (true);
CREATE POLICY "Categories are visible to everyone" ON "public"."event_categories" FOR 
SELECT 
  USING (true);
CREATE POLICY "Only admins can delete categories" ON "public"."event_categories" FOR DELETE USING (
  (
    EXISTS (
      SELECT 
        1 
      FROM 
        "public"."profiles" 
      WHERE 
        (
          (
            "profiles"."id" = "auth"."uid"()
          ) 
          AND ("profiles"."is_admin" = true)
        )
    )
  )
);
CREATE POLICY "Only admins can delete venues" ON "public"."event_venues" FOR DELETE USING (
  (
    EXISTS (
      SELECT 
        1 
      FROM 
        "public"."profiles" 
      WHERE 
        (
          (
            "profiles"."id" = "auth"."uid"()
          ) 
          AND ("profiles"."is_admin" = true)
        )
    )
  )
);
CREATE POLICY "Only admins can insert categories" ON "public"."event_categories" FOR INSERT WITH CHECK (
  (
    EXISTS (
      SELECT 
        1 
      FROM 
        "public"."profiles" 
      WHERE 
        (
          (
            "profiles"."id" = "auth"."uid"()
          ) 
          AND ("profiles"."is_admin" = true)
        )
    )
  )
);
CREATE POLICY "Only admins can update categories" ON "public"."event_categories" FOR 
UPDATE 
  USING (
    (
      EXISTS (
        SELECT 
          1 
        FROM 
          "public"."profiles" 
        WHERE 
          (
            (
              "profiles"."id" = "auth"."uid"()
            ) 
            AND ("profiles"."is_admin" = true)
          )
      )
    )
  );
CREATE POLICY "Only admins can update venues" ON "public"."event_venues" FOR 
UPDATE 
  USING (
    (
      EXISTS (
        SELECT 
          1 
        FROM 
          "public"."profiles" 
        WHERE 
          (
            (
              "profiles"."id" = "auth"."uid"()
            ) 
            AND ("profiles"."is_admin" = true)
          )
      )
    )
  );
CREATE POLICY "Users can view only approved events" ON "public"."events" FOR 
SELECT 
  USING (
    (
      (
        "approval_status" = 'approved' :: "text"
      ) 
      OR (
        "organizer_id" = "auth"."uid"()
      )
    )
  );
CREATE POLICY "Venues are visible to everyone" ON "public"."event_venues" FOR
SELECT
  USING (true);

-- Organization policies
-- Admin policy first to take precedence
CREATE POLICY "Admins can view all organizations" ON "public"."organizations" FOR
SELECT
  USING (
    EXISTS (
      SELECT 1 FROM "public"."profiles"
      WHERE "id" = "auth"."uid"() AND "is_admin" = true
    )
  );

CREATE POLICY "Admins can update organizations" ON "public"."organizations" FOR
UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM "public"."profiles"
      WHERE "id" = "auth"."uid"() AND "is_admin" = true
    )
  );

-- Regular user policies
CREATE POLICY "Anyone can view approved organizations" ON "public"."organizations" FOR
SELECT
  USING (
    (
      "approval_status" = 'approved' :: "text"
    )
    OR (
      "created_by" = "auth"."uid"()
    )
    OR (
      EXISTS (
        SELECT 1 FROM "public"."organization_members"
        WHERE "organization_id" = "organizations"."id"
        AND "user_id" = "auth"."uid"()
      )
    )
  );

CREATE POLICY "Authenticated users can create organizations" ON "public"."organizations" FOR INSERT TO "authenticated" WITH CHECK (
  "created_by" = "auth"."uid"()
);

CREATE POLICY "Organization creators can update their organizations" ON "public"."organizations" FOR
UPDATE
  TO "authenticated" USING (
    "created_by" = "auth"."uid"()
  );

-- Organization members policies
CREATE POLICY "Organization members can view their memberships" ON "public"."organization_members" FOR
SELECT
  TO "authenticated" USING (
    "user_id" = "auth"."uid"()
    OR EXISTS (
      SELECT 1 FROM "public"."organization_members" om
      WHERE om."organization_id" = "organization_members"."organization_id"
      AND om."user_id" = "auth"."uid"()
      AND om."role" IN ('owner', 'admin')
    )
  );

CREATE POLICY "Organization owners and admins can manage members" ON "public"."organization_members" FOR INSERT TO "authenticated" WITH CHECK (
  EXISTS (
    SELECT 1 FROM "public"."organization_members"
    WHERE "organization_id" = "organization_members"."organization_id"
    AND "user_id" = "auth"."uid"()
    AND "role" IN ('owner', 'admin')
  )
);

CREATE POLICY "Organization owners and admins can update members" ON "public"."organization_members" FOR
UPDATE
  TO "authenticated" USING (
    EXISTS (
      SELECT 1 FROM "public"."organization_members" om
      WHERE om."organization_id" = "organization_members"."organization_id"
      AND om."user_id" = "auth"."uid"()
      AND om."role" IN ('owner', 'admin')
    )
  );

CREATE POLICY "Users can leave organizations" ON "public"."organization_members" FOR DELETE TO "authenticated" USING (
  "user_id" = "auth"."uid"()
);

-- Admin helper functions
CREATE OR REPLACE FUNCTION get_all_organizations_admin()
RETURNS TABLE (
  id uuid,
  name text,
  description text,
  logo_url text,
  website_url text,
  contact_email text,
  contact_phone text,
  address text,
  city text,
  state text,
  country text,
  created_by uuid,
  created_at timestamptz,
  updated_at timestamptz,
  approval_status text,
  approval_notes text,
  approved_by uuid,
  approved_at timestamptz
)
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT
    o.id, o.name, o.description, o.logo_url, o.website_url,
    o.contact_email, o.contact_phone, o.address, o.city, o.state, o.country,
    o.created_by, o.created_at, o.updated_at, o.approval_status, o.approval_notes,
    o.approved_by, o.approved_at
  FROM organizations o
  WHERE EXISTS (
    SELECT 1 FROM profiles p
    WHERE p.id = auth.uid() AND p.is_admin = true
  );
$$;

ALTER TABLE
  "public"."event_categories" ENABLE ROW LEVEL SECURITY;
ALTER TABLE 
  "public"."event_venues" ENABLE ROW LEVEL SECURITY;
ALTER TABLE 
  "public"."events" ENABLE ROW LEVEL SECURITY;
ALTER TABLE 
  "public"."profiles" ENABLE ROW LEVEL SECURITY;
ALTER TABLE
  "public"."registrations" ENABLE ROW LEVEL SECURITY;
ALTER TABLE
  "public"."organizations" ENABLE ROW LEVEL SECURITY;
ALTER TABLE
  "public"."organization_members" ENABLE ROW LEVEL SECURITY;

-- Create trigger for automatic organization owner membership
CREATE TRIGGER "on_organization_created"
  AFTER INSERT ON "public"."organizations"
  FOR EACH ROW
  EXECUTE FUNCTION "public"."handle_new_organization"();
ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";
GRANT ALL ON FUNCTION "public"."find_events"(
  "featured" boolean, "include_past_events" boolean, 
  "limit_count" integer, "user_id" "uuid"
) TO "anon";
GRANT ALL ON FUNCTION "public"."find_events"(
  "featured" boolean, "include_past_events" boolean, 
  "limit_count" integer, "user_id" "uuid"
) TO "authenticated";
GRANT ALL ON FUNCTION "public"."find_events"(
  "featured" boolean, "include_past_events" boolean, 
  "limit_count" integer, "user_id" "uuid"
) TO "service_role";
GRANT ALL ON FUNCTION "public"."find_events_within_radius"(
  "search_lat" double precision, "search_lng" double precision, 
  "radius_km" double precision, "date_start" timestamp with time zone, 
  "date_end" timestamp with time zone, 
  "filter_category_id" "uuid", "search_term" "text", 
  "include_past_events" boolean, "user_id" "uuid", 
  "result_limit" integer, "result_offset" integer
) TO "anon";
GRANT ALL ON FUNCTION "public"."find_events_within_radius"(
  "search_lat" double precision, "search_lng" double precision, 
  "radius_km" double precision, "date_start" timestamp with time zone, 
  "date_end" timestamp with time zone, 
  "filter_category_id" "uuid", "search_term" "text", 
  "include_past_events" boolean, "user_id" "uuid", 
  "result_limit" integer, "result_offset" integer
) TO "authenticated";
GRANT ALL ON FUNCTION "public"."find_events_within_radius"(
  "search_lat" double precision, "search_lng" double precision, 
  "radius_km" double precision, "date_start" timestamp with time zone, 
  "date_end" timestamp with time zone, 
  "filter_category_id" "uuid", "search_term" "text", 
  "include_past_events" boolean, "user_id" "uuid", 
  "result_limit" integer, "result_offset" integer
) TO "service_role";
GRANT ALL ON FUNCTION "public"."find_venues_within_radius"(
  "search_lat" double precision, "search_lng" double precision, 
  "radius_km" double precision, "result_limit" integer
) TO "anon";
GRANT ALL ON FUNCTION "public"."find_venues_within_radius"(
  "search_lat" double precision, "search_lng" double precision, 
  "radius_km" double precision, "result_limit" integer
) TO "authenticated";
GRANT ALL ON FUNCTION "public"."find_venues_within_radius"(
  "search_lat" double precision, "search_lng" double precision, 
  "radius_km" double precision, "result_limit" integer
) TO "service_role";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";
GRANT ALL ON FUNCTION "public"."handle_new_organization"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_organization"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_organization"() TO "service_role";
GRANT ALL ON TABLE "public"."event_categories" TO "anon";
GRANT ALL ON TABLE "public"."event_categories" TO "authenticated";
GRANT ALL ON TABLE "public"."event_categories" TO "service_role";
GRANT ALL ON TABLE "public"."event_venues" TO "anon";
GRANT ALL ON TABLE "public"."event_venues" TO "authenticated";
GRANT ALL ON TABLE "public"."event_venues" TO "service_role";
GRANT ALL ON TABLE "public"."events" TO "anon";
GRANT ALL ON TABLE "public"."events" TO "authenticated";
GRANT ALL ON TABLE "public"."events" TO "service_role";
GRANT ALL ON TABLE "public"."profiles" TO "anon";
GRANT ALL ON TABLE "public"."profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."profiles" TO "service_role";
GRANT ALL ON TABLE "public"."registrations" TO "anon";
GRANT ALL ON TABLE "public"."registrations" TO "authenticated";
GRANT ALL ON TABLE "public"."registrations" TO "service_role";
GRANT ALL ON TABLE "public"."organizations" TO "anon";
GRANT ALL ON TABLE "public"."organizations" TO "authenticated";
GRANT ALL ON TABLE "public"."organizations" TO "service_role";
GRANT ALL ON TABLE "public"."organization_members" TO "anon";
GRANT ALL ON TABLE "public"."organization_members" TO "authenticated";
GRANT ALL ON TABLE "public"."organization_members" TO "service_role";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES TO "service_role";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS TO "service_role";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES TO "service_role";
RESET ALL;
