<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Title Test - TheLocalAdda</title>
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-K5ERWJ7N4Y"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-K5ERWJ7N4Y', {
        send_page_view: false,
        allow_google_signals: true,
        allow_ad_personalization_signals: false,
        enhanced_measurement: {
          scrolls: false,
          outbound_clicks: false,
          site_search: false,
          video_engagement: false,
          file_downloads: false
        }
      });
    </script>
    
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 50px auto; 
            padding: 20px;
        }
        .event-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: #f9f9f9;
            cursor: pointer;
        }
        .event-card:hover {
            background: #f0f0f0;
        }
        button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .log {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>🔍 Event Title Tracking Test</h1>
    <p>This page tests if event titles are being properly tracked in Google Analytics.</p>
    
    <div class="event-card" onclick="testEventTracking('event-123', 'Community Cooking Workshop')">
        <h3>Community Cooking Workshop</h3>
        <p>ID: event-123</p>
        <p>Click this card to test event tracking</p>
    </div>
    
    <div class="event-card" onclick="testEventTracking('event-456', 'Yoga & Meditation Session')">
        <h3>Yoga & Meditation Session</h3>
        <p>ID: event-456</p>
        <p>Click this card to test event tracking</p>
    </div>
    
    <div class="event-card" onclick="testEventTracking('event-789', '')">
        <h3>Event with Empty Title</h3>
        <p>ID: event-789</p>
        <p>This tests what happens with empty title</p>
    </div>
    
    <div class="event-card" onclick="testEventTracking('', 'Event with Empty ID')">
        <h3>Event with Empty ID</h3>
        <p>ID: (empty)</p>
        <p>This tests what happens with empty ID</p>
    </div>
    
    <button onclick="clearLog()">🧹 Clear Log</button>
    <button onclick="checkGoogleAnalytics()">📊 Check GA Real-time</button>
    
    <div id="test-results"></div>
    <div class="log" id="log"></div>
    
    <h3>Instructions:</h3>
    <ol>
        <li>Click on the event cards above</li>
        <li>Check the log below for tracking data</li>
        <li>Open Google Analytics Real-time → Events</li>
        <li>Look for events with format: "Event Title (ID: event-123)"</li>
        <li>Verify that titles are showing correctly</li>
    </ol>

    <script>
        let testResults = [];
        
        function log(message, type = 'info') {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'color: red;' : type === 'success' ? 'color: green;' : '';
            logEl.innerHTML += `<div style="${className}">[${timestamp}] ${message}</div>`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(message);
        }
        
        function addTestResult(test, passed, details) {
            testResults.push({ test, passed, details });
            updateTestResults();
        }
        
        function updateTestResults() {
            const resultsEl = document.getElementById('test-results');
            resultsEl.innerHTML = '<h3>Test Results:</h3>';
            
            testResults.forEach(result => {
                const div = document.createElement('div');
                div.className = `test-result ${result.passed ? 'success' : 'error'}`;
                div.innerHTML = `
                    <strong>${result.passed ? '✅' : '❌'} ${result.test}</strong><br>
                    ${result.details}
                `;
                resultsEl.appendChild(div);
            });
        }
        
        function testEventTracking(eventId, eventTitle) {
            log(`🧪 Testing event tracking: ID="${eventId}", Title="${eventTitle}"`);
            
            // Test 1: Check parameters
            const hasId = eventId && eventId.trim() !== '';
            const hasTitle = eventTitle && eventTitle.trim() !== '';
            
            log(`📋 Parameters: ID=${hasId ? '✅' : '❌'}, Title=${hasTitle ? '✅' : '❌'}`);
            
            // Test 2: Create combined label (like our analytics function does)
            const combinedLabel = eventTitle && eventId 
                ? `${eventTitle} (ID: ${eventId})`
                : eventTitle || eventId || 'Unknown Event';
            
            log(`🏷️ Combined label: "${combinedLabel}"`);
            
            // Test 3: Send to Google Analytics
            if (typeof gtag === 'function') {
                gtag('event', 'localadda_event_view', {
                    event_category: 'Event Interaction',
                    event_label: combinedLabel,
                    event_id: eventId,
                    event_title: eventTitle,
                    custom_event_type: 'view',
                    source: 'localadda_custom_tracking'
                });
                
                log(`📤 Event sent to GA with label: "${combinedLabel}"`, 'success');
                
                // Record test result
                const testName = `Event: ${eventTitle || 'No Title'} (${eventId || 'No ID'})`;
                const passed = combinedLabel !== 'Unknown Event';
                const details = `Label: "${combinedLabel}"`;
                addTestResult(testName, passed, details);
                
            } else {
                log('❌ gtag function not available', 'error');
                addTestResult('gtag availability', false, 'gtag function not found');
            }
            
            log('---');
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('test-results').innerHTML = '';
            testResults = [];
        }
        
        function checkGoogleAnalytics() {
            log('📊 Opening Google Analytics Real-time reports...');
            window.open('https://analytics.google.com/analytics/web/#/p/realtime/rt-event/a/YOUR_ACCOUNT_ID/p/YOUR_PROPERTY_ID/', '_blank');
        }
        
        // Monitor all gtag calls
        if (typeof window.gtag === 'function') {
            const originalGtag = window.gtag;
            
            window.gtag = function(...args) {
                if (args[0] === 'event' && args[1].includes('localadda_event')) {
                    log(`📊 GA Event Sent: ${args[1]}`);
                    log(`   Label: "${args[2]?.event_label || 'No label'}"`);
                    log(`   ID: "${args[2]?.event_id || 'No ID'}"`);
                    log(`   Title: "${args[2]?.event_title || 'No title'}"`);
                }
                return originalGtag.apply(this, args);
            };
        }
        
        // Initial setup
        window.addEventListener('load', function() {
            log('🚀 Event title test page loaded');
            log('Click on event cards above to test tracking');
            
            // Test gtag availability
            if (typeof gtag === 'function') {
                log('✅ Google Analytics (gtag) is loaded and ready');
            } else {
                log('❌ Google Analytics (gtag) is not loaded', 'error');
            }
        });
    </script>
</body>
</html>
