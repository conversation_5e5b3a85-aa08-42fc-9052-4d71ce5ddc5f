# 🔍 Event Title Debugging Guide

## 🚨 **Issue**: Event titles not showing in Google Analytics

You mentioned that when clicking events, you only see the event ID instead of the title. Let's debug this step by step.

## 🧪 **Step 1: Test with Debug Script**

### **Run this in your browser console on your live site:**

```javascript
// Event Title Debug Script
console.log('🔍 Event Title Debug Script');
console.log('============================');

// Monitor all gtag calls
if (typeof window.gtag === 'function') {
    const originalGtag = window.gtag;
    
    window.gtag = function(...args) {
        if (args[0] === 'event' && args[1].includes('localadda_event')) {
            console.log('📊 Event Data Sent:');
            console.log('- Action:', args[1]);
            console.log('- Event Label:', args[2]?.event_label);
            console.log('- Event ID:', args[2]?.event_id);
            console.log('- Event Title:', args[2]?.event_title);
            console.log('- Full Parameters:', args[2]);
            console.log('---');
        }
        return originalGtag.apply(this, args);
    };
    
    console.log('✅ Event monitoring enabled - click on events now');
} else {
    console.log('❌ gtag not found');
}
```

### **What to look for:**
- **Event Label**: Should show `"Event Title (ID: event-123)"`
- **Event Title**: Should show the actual event title
- **Event ID**: Should show the event ID

## 🔧 **Step 2: Check Event Data in DOM**

### **Run this to see what event data is available:**

```javascript
// Check event data in DOM
console.log('🔍 Checking DOM for event data...');
const eventCards = document.querySelectorAll('[href*="/events/"], .event-card, [data-event-id]');
console.log(`Found ${eventCards.length} potential event elements`);

eventCards.forEach((card, index) => {
    const title = card.querySelector('h1, h2, h3, h4, .title, [class*="title"]')?.textContent?.trim();
    const link = card.querySelector('a[href*="/events/"]') || card;
    const href = link.getAttribute('href');
    const eventId = href ? href.split('/').pop() : 'unknown';
    
    console.log(`Event ${index + 1}:`, {
        title: title || 'No title found',
        eventId: eventId,
        element: card
    });
});
```

## 🎯 **Step 3: Manual Test**

### **Test the tracking function directly:**

```javascript
// Test the tracking function manually
if (typeof window.gtag === 'function') {
    // Test with good data
    gtag('event', 'localadda_event_view', {
        event_category: 'Event Interaction',
        event_label: 'Test Event Title (ID: test-123)',
        event_id: 'test-123',
        event_title: 'Test Event Title',
        custom_event_type: 'view',
        source: 'manual_test'
    });
    
    console.log('✅ Manual test event sent');
} else {
    console.log('❌ gtag not available');
}
```

## 🔍 **Step 4: Check Google Analytics**

### **In Google Analytics:**
1. **Go to Reports** → **Realtime** → **Events**
2. **Look for events** starting with `localadda_event_`
3. **Check the Event Label column** - this should show the title + ID
4. **Click on an event** to see detailed parameters

### **What you should see:**
- **Event Name**: `localadda_event_view`
- **Event Label**: `"Community Cooking Class (ID: event-123)"`
- **Custom Parameters**: 
  - `event_id`: `event-123`
  - `event_title`: `Community Cooking Class`

## 🚨 **Common Issues & Solutions:**

### **Issue 1: Event Label shows only ID**
**Symptoms**: Label shows `"event-123"` instead of `"Event Title (ID: event-123)"`
**Cause**: Event title is undefined/empty
**Solution**: Check if event data is being fetched correctly

### **Issue 2: Event Label shows "Unknown Event"**
**Symptoms**: Label shows `"Unknown Event"`
**Cause**: Both title and ID are undefined
**Solution**: Check component props and data flow

### **Issue 3: No events showing in GA**
**Symptoms**: No `localadda_event_*` events in Real-time reports
**Cause**: Tracking not firing or blocked
**Solution**: Check console for errors, disable ad blockers

## 🛠️ **Fixes Applied:**

### **1. Enhanced Data Cleaning**
```javascript
// Now cleans and validates data before sending
const cleanEventId = eventId?.toString().trim() || '';
const cleanEventTitle = eventTitle?.toString().trim() || '';
```

### **2. Fallback Title Generation**
```javascript
// EventCard now provides fallback title
const eventTitle = title?.trim() || `Event ${id}` || 'Untitled Event';
```

### **3. Enhanced Debugging**
- Added console logging in development mode
- Shows original vs cleaned values
- Tracks data flow from component to analytics

## 📊 **Expected Results After Fix:**

### **Before Fix:**
- Event Label: `"event-123"`
- Event Title: `undefined`

### **After Fix:**
- Event Label: `"Community Cooking Class (ID: event-123)"`
- Event Title: `"Community Cooking Class"`

## 🚀 **Deploy and Test:**

1. **Deploy the updated code**
2. **Open your live website**
3. **Run the debug script** in console
4. **Click on event cards**
5. **Check Google Analytics Real-time reports**

## 📝 **Test Checklist:**

- [ ] Debug script shows event data in console
- [ ] Event Label includes both title and ID
- [ ] Google Analytics shows descriptive event names
- [ ] Real-time reports update when clicking events
- [ ] Event parameters include both `event_id` and `event_title`

## 🆘 **If Still Not Working:**

### **Check these:**
1. **Event data structure** - Are titles actually in the database?
2. **Component props** - Is title being passed to EventCard?
3. **Network requests** - Are events being fetched successfully?
4. **Browser console** - Any JavaScript errors?
5. **Ad blockers** - Temporarily disable for testing

### **Get more help:**
Run this comprehensive diagnostic:

```javascript
// Comprehensive diagnostic
console.log('🔍 Comprehensive Event Tracking Diagnostic');
console.log('==========================================');

// Check gtag
console.log('1. gtag availability:', typeof window.gtag);

// Check dataLayer
console.log('2. dataLayer length:', window.dataLayer?.length || 0);

// Check recent events
const recentEvents = window.dataLayer?.filter(item => 
    item[0] === 'event' && item[1]?.includes('localadda_event')
).slice(-5) || [];
console.log('3. Recent localadda events:', recentEvents);

// Check DOM for events
const eventElements = document.querySelectorAll('[href*="/events/"]');
console.log('4. Event elements found:', eventElements.length);

// Check for React components
console.log('5. React detected:', !!window.React || !!document.querySelector('[data-reactroot]'));

console.log('==========================================');
```

This should help identify exactly where the issue is occurring!
