# 👥 Distinct User Tracking Guide

## ✅ **How Distinct Users Are Tracked:**

Google Analytics 4 uses multiple methods to identify distinct users, and we've enhanced this for TheLocalAdda:

### **1. Authenticated Users (Logged In)**
- **User ID**: Set to Supabase user ID when logged in
- **Cross-device tracking**: Same user across multiple devices
- **Persistent identification**: User remains the same across sessions

### **2. Anonymous Users (Not Logged In)**
- **Client ID**: Browser-based identifier (cookies)
- **Anonymous ID**: Custom persistent identifier we generate
- **Session-based**: Tracked until cookies are cleared

### **3. Enhanced User Identification**
- **Google Signals**: Cross-device tracking for signed-in Google users
- **Custom Properties**: Additional user metadata for better segmentation

## 🔧 **Technical Implementation:**

### **Authenticated User Flow:**
```javascript
// When user logs in
setUserId(session.user.id); // Supabase user ID
setUserProperties({
  user_id: session.user.id,
  user_name: session.user.user_metadata.full_name,
  user_email: session.user.email,
  user_type: 'authenticated',
  login_method: 'google'
});
```

### **Anonymous User Flow:**
```javascript
// When user is not logged in
const anonymousId = generateAnonymousUserId(); // e.g., "anon_abc123_xyz789"
setUserProperties({
  user_type: 'anonymous',
  anonymous_id: anonymousId,
  login_method: 'none'
});
```

### **User Logout Flow:**
```javascript
// When user logs out
clearUserId();
setAnonymousUser(); // Switch back to anonymous tracking
```

## 📊 **What This Means in Google Analytics:**

### **Active Users Report:**
- **Authenticated users**: Counted by User ID (Supabase ID)
- **Anonymous users**: Counted by Client ID + Anonymous ID
- **Cross-device**: Same authenticated user on phone + laptop = 1 user
- **Same browser**: Multiple visits from same browser = 1 user

### **User Identification Hierarchy:**
1. **User ID** (highest priority) - For logged-in users
2. **Client ID** (medium priority) - Browser-based
3. **Anonymous ID** (custom) - Our fallback identifier

## 🎯 **Viewing Distinct Users in GA4:**

### **Real-time Reports:**
- **Reports** → **Realtime** → **Overview**
- Shows currently active distinct users

### **Standard Reports:**
- **Reports** → **Engagement** → **Overview**
- **Active users** metric shows distinct users over time periods

### **User Explorer:**
- **Explore** → **User Explorer**
- See individual user journeys (anonymized)

### **Audience Reports:**
- **Reports** → **Demographics** → **Overview**
- Distinct user counts by demographics

## 🔍 **Custom Dimensions for Better Tracking:**

We've added custom user properties you can use for segmentation:

### **User Type Segmentation:**
- `user_type`: "authenticated" or "anonymous"
- `login_method`: "google" or "none"
- `first_login`: "yes" or "no"

### **Creating Custom Dimensions in GA4:**
1. **Admin** → **Custom Definitions** → **Custom Dimensions**
2. **Create Custom Dimension**:
   - Dimension name: "User Type"
   - Parameter name: "user_type"
   - Scope: "User"

## 📈 **Advanced User Analysis:**

### **Cohort Analysis:**
- **Explore** → **Cohort Exploration**
- Track user retention over time
- See how many users return after first visit

### **User Lifetime Value:**
- **Reports** → **Monetization** → **User Lifetime Value**
- Track value of distinct users over time

### **Path Analysis:**
- **Explore** → **Path Exploration**
- See how distinct users navigate your site

## 🔧 **Testing Distinct User Tracking:**

### **Test Script:**
```javascript
// Run in browser console to check user identification
console.log('🔍 User Identification Test');
console.log('==========================');

// Check current user properties
if (window.dataLayer) {
  const userEvents = window.dataLayer.filter(item => 
    item[0] === 'set' || (item[0] === 'config' && item[2]?.user_id)
  );
  console.log('User identification events:', userEvents);
}

// Check localStorage for anonymous ID
const anonymousId = localStorage.getItem('localadda_anonymous_id');
console.log('Anonymous ID:', anonymousId);

// Check if user is logged in (you'll need to adapt this)
console.log('Current user status: Check your auth state');
```

### **Manual Testing:**
1. **Visit site without logging in** - Should get anonymous ID
2. **Log in with Google** - Should set User ID
3. **Log out** - Should clear User ID, set anonymous ID
4. **Use different browser** - Should get different anonymous ID
5. **Same browser, different session** - Should keep same anonymous ID

## 📊 **Expected Results:**

### **Distinct User Counting:**
- ✅ **Same user, multiple sessions**: Counted as 1 user
- ✅ **Same user, multiple devices** (if logged in): Counted as 1 user
- ✅ **Different browsers, not logged in**: Counted as separate users
- ✅ **User logs in/out multiple times**: Counted as 1 user
- ✅ **Anonymous user becomes authenticated**: Transitions smoothly

### **User Segmentation:**
- **Authenticated users**: Can track across devices and sessions
- **Anonymous users**: Tracked per browser/device
- **Returning users**: Properly identified on return visits
- **New vs returning**: Accurate classification

## 🚨 **Privacy Considerations:**

### **GDPR/Privacy Compliance:**
- **IP Anonymization**: Enabled by default
- **User consent**: Consider implementing cookie consent
- **Data retention**: Configurable in GA4 settings
- **User deletion**: Can be handled through GA4 API

### **Privacy-Friendly Settings:**
```javascript
gtag('config', 'G-K5ERWJ7N4Y', {
  anonymize_ip: true,
  allow_ad_personalization_signals: false, // Privacy-friendly
  allow_google_signals: true // Cross-device tracking only
});
```

## 🎯 **Key Benefits:**

1. **Accurate user counts** - No inflation from multiple sessions
2. **Cross-device tracking** - Understand full user journey
3. **Better segmentation** - Authenticated vs anonymous users
4. **Improved analytics** - More meaningful user behavior data
5. **Privacy compliant** - Respects user privacy while providing insights

Your distinct user tracking is now properly configured and will provide accurate, privacy-compliant user analytics for TheLocalAdda!
