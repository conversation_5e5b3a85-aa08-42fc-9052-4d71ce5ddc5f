# 🔍 Search Functionality Testing Guide

This guide covers the comprehensive search testing suite for TheLocalAdda platform.

## 📋 Test Overview

The search functionality tests are organized into three main test files:

### 1. **`tests/homepage-elements.spec.ts`** - Basic Search Tests
- ✅ **Basic search with count verification** - Tests search for "cook" and verifies UI count matches database
- ✅ **Multi-keyword search** - Tests different search terms (music, workshop, food, art)
- ✅ **Edge cases** - Tests empty search, whitespace, non-existent terms, single characters
- ✅ **Special characters** - Tests search with café, music & dance, art@gallery, etc.

### 2. **`tests/search-functionality.spec.ts`** - Advanced Search Tests
- ✅ **Category-specific search** - Tests filtering by event categories
- ✅ **Search result interaction** - Tests clicking on search results and navigation
- ✅ **Performance testing** - Measures search response times
- ✅ **URL parameters** - Tests deep linking with search parameters

### 3. **`tests/search-validation.spec.ts`** - Data Validation Tests
- ✅ **Content accuracy** - Validates search results match database content
- ✅ **Combined filters** - Tests search + additional filters
- ✅ **Result ordering** - Validates chronological and relevance ordering
- ✅ **No results handling** - Tests graceful handling of empty results

## 🚀 Running the Tests

### Individual Test Files
```bash
# Basic search functionality
npm run test:homepage-elements

# Advanced search features
npm run test:search

# Search result validation
npm run test:search-validation

# All search tests together
npm run test:search-all
```

### Specific Test Cases
```bash
# Run only the basic count verification test
npm run test:tests -- tests/homepage-elements.spec.ts --grep "should search for events and verify UI count matches database count"

# Run edge cases test
npm run test:tests -- tests/homepage-elements.spec.ts --grep "should handle search edge cases"

# Run performance test
npm run test:tests -- tests/search-functionality.spec.ts --grep "should handle search operations efficiently"
```

### Debug Mode (Visible Browser)
```bash
# Run with visible browser for debugging
npm run test:tests -- tests/homepage-elements.spec.ts --headed

# Run specific test with debugging
npm run test:tests -- tests/search-functionality.spec.ts --grep "interaction" --headed --debug
```

## 🔧 Test Configuration

### Environment Setup
The tests use a dedicated Supabase client (`tests/helpers/test-supabase-client.ts`) that:
- Uses `process.env` instead of `import.meta.env` for Node.js compatibility
- Loads environment variables from `.env` file
- Provides database access for result verification

### Key Selectors Used
```typescript
// Event cards (main selector)
'div.group.w-full.rounded-2xl[class*="shadow-smooth"]'

// Search input (flexible)
'input[type="search"], input[placeholder*="search" i], input[placeholder*="find" i]'

// Event links
'a[href*="/events/"]'

// No results messages
'text=/no events|no results|nothing found/i'
```

## 📊 Test Scenarios Covered

### ✅ **Basic Search Functionality**
- [x] Search input detection and interaction
- [x] Search term submission (Enter key)
- [x] Navigation to results page
- [x] URL parameter handling (`?search=term`)
- [x] Results page loading and stability

### ✅ **Data Consistency**
- [x] Database query matching UI search logic
- [x] Count verification (DB vs UI)
- [x] Content accuracy (titles, dates, locations)
- [x] Approval status filtering (only approved events)

### ✅ **Edge Cases**
- [x] Empty search terms
- [x] Whitespace-only search
- [x] Non-existent search terms
- [x] Single character searches
- [x] Very long search terms
- [x] Special characters (café, &, @, -, #)

### ✅ **User Experience**
- [x] Search result interaction (clicking events)
- [x] Navigation to event details
- [x] Back navigation to results
- [x] No results messaging
- [x] Loading states and performance

### ✅ **Advanced Features**
- [x] Category filtering
- [x] Combined search + filters
- [x] Result ordering (chronological)
- [x] Deep linking with search parameters
- [x] URL encoding/decoding

## 🐛 Debugging Tips

### Common Issues and Solutions

1. **Selector Not Found**
   ```bash
   # Take screenshot for debugging
   await page.screenshot({ path: 'debug-search.png' });
   
   # Log page structure
   const pageContent = await page.content();
   console.log('Page HTML:', pageContent.substring(0, 1000));
   ```

2. **Count Mismatch**
   ```typescript
   // Debug database query
   console.log('Database query result:', { data: dbEvents, count: dbCount });
   
   // Debug UI selectors
   const allCards = await page.locator('[class*="card"]').count();
   console.log('All card-like elements:', allCards);
   ```

3. **Environment Variables**
   ```bash
   # Check if .env is loaded
   console.log('SUPABASE_URL:', process.env.VITE_SUPABASE_URL?.substring(0, 20));
   ```

### Test Debugging Commands
```bash
# Run with maximum debugging
npm run test:tests -- tests/search-functionality.spec.ts --headed --debug --timeout=0

# Generate detailed HTML report
npx playwright show-report

# Run with trace for detailed analysis
npm run test:tests -- tests/search-validation.spec.ts --trace=on
```

## 📈 Performance Expectations

### Response Time Targets
- **Search execution**: < 3 seconds average
- **Page navigation**: < 2 seconds
- **Database queries**: < 1 second
- **UI rendering**: < 1 second

### Test Thresholds
```typescript
// Performance test assertion
expect(averageTime).toBeLessThan(5000); // 5 seconds max average
```

## 🔄 Maintenance

### Adding New Search Tests
1. Choose appropriate test file based on test type
2. Follow existing patterns for setup and teardown
3. Use `performLocationSetupOnly(page)` for consistent setup
4. Include comprehensive logging for debugging
5. Add database verification where applicable

### Updating Selectors
When UI changes occur, update selectors in:
- `tests/homepage-elements.spec.ts` - Basic search selectors
- `tests/search-functionality.spec.ts` - Advanced feature selectors  
- `tests/search-validation.spec.ts` - Content validation selectors

### Database Schema Changes
If event schema changes, update:
- `tests/helpers/test-supabase-client.ts` - Type definitions
- Database queries in test files
- Content validation logic

## 🎯 Success Criteria

A successful search test run should show:
- ✅ All database connections established
- ✅ Search input interactions working
- ✅ UI count matches database count
- ✅ No JavaScript errors in console
- ✅ Proper navigation and URL handling
- ✅ Graceful handling of edge cases
- ✅ Performance within acceptable limits

## 📞 Support

For issues with search tests:
1. Check the HTML report: `npx playwright show-report`
2. Review screenshots in `test-results/` directory
3. Check console logs for database connection issues
4. Verify `.env` file contains correct Supabase credentials
5. Ensure test database has sample events for testing
