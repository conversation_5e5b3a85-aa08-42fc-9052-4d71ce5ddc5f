
import React from 'react';

import Header from '../components/layout/Header';
import Footer from '../components/layout/Footer';
import Hero from '../components/home/<USER>';
import FeaturedEvents from '../components/home/<USER>';
import FeaturedVenues from '../components/home/<USER>';
import HostEventCTA from '../components/home/<USER>';
import LocationGate from '../components/location/LocationGate';

const CATEGORIES = [
  {
    name: 'Families',
    icon: '👪',
    description: 'Activities for parents and children',
    color: 'from-pink-50 to-pink-100',
    hoverColor: 'group-hover:bg-pink-400'
  },
  {
    name: 'Culture',
    icon: '🎭',
    description: 'Arts, music, and performances',
    color: 'from-purple-50 to-purple-100',
    hoverColor: 'group-hover:bg-purple-400'
  },
  {
    name: 'Education',
    icon: '📚',
    description: 'Workshops, classes, and seminars',
    color: 'from-blue-50 to-blue-100',
    hoverColor: 'group-hover:bg-blue-400'
  },
  {
    name: 'Sports',
    icon: '⚽',
    description: 'Games, fitness, and outdoor activities',
    color: 'from-green-50 to-green-100',
    hoverColor: 'group-hover:bg-green-400'
  },
  {
    name: 'Food',
    icon: '🍽️',
    description: 'Tastings, cookouts, and food festivals',
    color: 'from-yellow-50 to-yellow-100',
    hoverColor: 'group-hover:bg-yellow-400'
  },
  {
    name: 'Networking',
    icon: '🤝',
    description: 'Professional and social connections',
    color: 'from-cyan-50 to-cyan-100',
    hoverColor: 'group-hover:bg-cyan-400'
  }
];

const HOW_IT_WORKS_STEPS = [
  {
    number: '01',
    title: 'Discover Events',
    description: 'Browse through local events happening in your neighborhood, filtered by your interests and location.'
  },
  {
    number: '02',
    title: 'Join Activities',
    description: "Register for events that catch your eye, whether they're free meetups or ticketed gatherings."
  },
  {
    number: '03',
    title: 'Connect & Share',
    description: 'Meet new people, build relationships, and create a stronger community together.'
  }
];

const Index: React.FC = () => {
  // Ensure page scrolls to top on mount
  React.useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <LocationGate>
        <main className="flex-grow">
          <Hero />
          <FeaturedEvents />
          <FeaturedVenues />
          <HostEventCTA />
        </main>
      </LocationGate>

      <Footer />
    </div>
  );
};

export default Index;
