import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { OrganizationRegistrationForm } from '@/components/organizations/OrganizationRegistrationForm';
import { OrganizationList } from '@/components/organizations/OrganizationList';
import { useOrganizations } from '@/hooks/useOrganizations';
import { UserOrganization } from '@/types/organization';
import { Building2, Plus, Loader2 } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

export const OrganizationsPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { organizations, loading, error } = useOrganizations();
  const [showRegistrationForm, setShowRegistrationForm] = useState(false);
  const [activeTab, setActiveTab] = useState('list');

  // Check if we came from event creation
  const returnTo = location.state?.returnTo;
  const returnContext = location.state?.returnContext;

  const handleCreateEvent = (organization: UserOrganization) => {
    // Navigate to create event page with organization pre-selected
    navigate('/create', {
      state: {
        preSelectedOrganization: organization
      }
    });
  };

  const handleManageOrganization = (organization: UserOrganization) => {
    navigate(`/organizations/${organization.id}/manage`);
  };

  const handleRegistrationSuccess = (newOrganization?: UserOrganization) => {
    setShowRegistrationForm(false);
    setActiveTab('list');

    // If we came from event creation and have a new organization, return to event creation
    if (returnTo && returnContext === 'event-creation' && newOrganization) {
      navigate(returnTo, {
        state: {
          preSelectedOrganization: newOrganization
        }
      });
    }
  };

  // Set initial tab based on return context
  useEffect(() => {
    if (returnContext === 'event-creation') {
      setActiveTab('register');
    }
  }, [returnContext]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <p className="text-red-600 mb-4">Error loading organizations: {error}</p>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow">
        <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Building2 className="h-8 w-8 text-primary" />
          <h1 className="text-3xl font-bold">Organizations</h1>
        </div>
        <p className="text-gray-600">
          Manage your organizations and create events on behalf of them.
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="list">My Organizations</TabsTrigger>
            <TabsTrigger value="register">Register New</TabsTrigger>
          </TabsList>
          
          {activeTab === 'list' && (
            <Button
              onClick={() => setActiveTab('register')}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Register Organization
            </Button>
          )}
        </div>

        <TabsContent value="list" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Your Organizations</CardTitle>
              <CardDescription>
                Organizations you're a member of. You can create events on behalf of approved organizations.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <OrganizationList
                organizations={organizations}
                onCreateEvent={handleCreateEvent}
                onManageOrganization={handleManageOrganization}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="register" className="space-y-6">
          <OrganizationRegistrationForm
            onSuccess={handleRegistrationSuccess}
            onCancel={() => setActiveTab('list')}
          />
        </TabsContent>
      </Tabs>
        </div>
      </main>
      <Footer />
    </div>
  );
};
