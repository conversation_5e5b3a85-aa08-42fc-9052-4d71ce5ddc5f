import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { OrganizationRegistrationForm } from '@/components/organizations/OrganizationRegistrationForm';
import { OrganizationList } from '@/components/organizations/OrganizationList';
import { useOrganizations } from '@/hooks/useOrganizations';
import { UserOrganization } from '@/types/organization';
import { Building2, Plus, Loader2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

export const OrganizationsPage: React.FC = () => {
  const navigate = useNavigate();
  const { organizations, loading, error } = useOrganizations();
  const [showRegistrationForm, setShowRegistrationForm] = useState(false);
  const [activeTab, setActiveTab] = useState('list');

  const handleCreateEvent = (organization: UserOrganization) => {
    // Navigate to create event page with organization pre-selected
    navigate('/create', {
      state: {
        preSelectedOrganization: organization
      }
    });
  };

  const handleManageOrganization = (organization: UserOrganization) => {
    navigate(`/organizations/${organization.id}/manage`);
  };

  const handleRegistrationSuccess = () => {
    setShowRegistrationForm(false);
    setActiveTab('list');
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <p className="text-red-600 mb-4">Error loading organizations: {error}</p>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Building2 className="h-8 w-8 text-primary" />
          <h1 className="text-3xl font-bold">Organizations</h1>
        </div>
        <p className="text-gray-600">
          Manage your organizations and create events on behalf of them.
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="list">My Organizations</TabsTrigger>
            <TabsTrigger value="register">Register New</TabsTrigger>
          </TabsList>
          
          {activeTab === 'list' && (
            <Button
              onClick={() => setActiveTab('register')}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Register Organization
            </Button>
          )}
        </div>

        <TabsContent value="list" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Your Organizations</CardTitle>
              <CardDescription>
                Organizations you're a member of. You can create events on behalf of approved organizations.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <OrganizationList
                organizations={organizations}
                onCreateEvent={handleCreateEvent}
                onManageOrganization={handleManageOrganization}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="register" className="space-y-6">
          <OrganizationRegistrationForm
            onSuccess={handleRegistrationSuccess}
            onCancel={() => setActiveTab('list')}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};
