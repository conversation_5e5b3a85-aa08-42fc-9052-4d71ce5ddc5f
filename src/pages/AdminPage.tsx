import React, { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle, DrawerDescription, DrawerFooter } from '@/components/ui/drawer';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, UserCheck, Calendar, Info, CheckCircle, XCircle, Tag, MapPin, Settings, Building2 } from 'lucide-react';
import { useMobile } from '@/hooks/use-mobile';
import CategoryManager from '@/components/admin/CategoryManager';
import VenueManager from '@/components/admin/VenueManager';
import { generateEventPoster, updateEventWithPosterFilename } from '@/utils/posterGenerator';
import { Organization } from '@/types/organization';

type ApprovalStatus = 'pending' | 'approved' | 'rejected';

interface Event {
  id: string;
  title: string;
  description: string;
  approval_status: string;
  created_at: string;
  image_url?: string | null;
  start_date: string;
  end_date?: string | null;
  category: {
    id: string;
    name: string;
    color: string;
    text_color: string;
    icon: string;
  };
  venue: {
    id: string;
    name: string;
    address: string;
    city: string;
    state: string;
    zip_code?: string | null;
    locality?: string | null;
    place_id?: string | null;
  };
  organizer_name: string;
  organizer_email: string;
  approval_notes: string;
  // These fields are for backward compatibility
  venue_name?: string;
  address?: string;
  city?: string;
  state?: string;
}

interface User {
  id: string;
  email: string;
  full_name?: string;
  is_admin?: boolean;
  created_at: string;
  username?: string;
}

const AdminPage: React.FC = () => {
  const { user, loading } = useAuth();
  const { toast } = useToast();
  const { isMobile } = useMobile();
  const [events, setEvents] = useState<Event[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [organizationsLoading, setOrganizationsLoading] = useState(false);
  const [selectedItem, setSelectedItem] = useState<Event | User | Organization | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [approvalNotes, setApprovalNotes] = useState(''); // This will store the notes for the event/organization in AdminPage
  const [selectedStatus, setSelectedStatus] = useState<ApprovalStatus | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check if user is admin
  const [isAdmin, setIsAdmin] = useState(false);

  const fetchEvents = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('events')
        .select(`
          *,
          category:event_categories(id, name, color, text_color, icon),
          venue:event_venues(*)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setEvents(data || []);
    } catch (error) {
      console.error('Error fetching events:', error);
      toast({
        title: 'Error fetching events',
        description: 'Could not load events data.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const fetchUsers = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: 'Error fetching users',
        description: 'Could not load users data.',
        variant: 'destructive',
      });
    }
  }, [toast]);

  const fetchOrganizations = useCallback(async () => {
    try {
      setOrganizationsLoading(true);
      console.log('=== FETCHING ORGANIZATIONS ===');
      console.log('Current user:', user?.id);
      console.log('Is admin:', isAdmin);

      // Try multiple approaches to get organizations
      let data = null;
      let error = null;

      if (isAdmin) {
        console.log('Trying admin-specific query approaches...');

        // Approach 1: Standard query (should work with RLS)
        const result1 = await supabase
          .from('organizations')
          .select('*', { count: 'exact' })
          .order('created_at', { ascending: false });

        console.log('Approach 1 (standard):', result1);

        if (result1.data && result1.data.length > 0) {
          data = result1.data;
          error = result1.error;
        } else {
          // Approach 2: Query with explicit admin check in WHERE clause
          const result2 = await supabase
            .from('organizations')
            .select('*')
            .or(`approval_status.eq.pending,approval_status.eq.approved,approval_status.eq.rejected`)
            .order('created_at', { ascending: false });

          console.log('Approach 2 (explicit filter):', result2);
          data = result2.data;
          error = result2.error;
        }
      } else {
        // Non-admin query
        const result = await supabase
          .from('organizations')
          .select('*')
          .order('created_at', { ascending: false });

        data = result.data;
        error = result.error;
      }

      console.log('Final result:');
      console.log('- Data:', data);
      console.log('- Error:', error);

      if (error) {
        console.error('Supabase error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        throw error;
      }

      console.log('Setting organizations state with:', data?.length || 0, 'items');
      setOrganizations(data || []);
      console.log('=== FETCH COMPLETE ===');
    } catch (error) {
      console.error('Error fetching organizations:', error);
      toast({
        title: 'Error fetching organizations',
        description: error instanceof Error ? error.message : 'Could not load organizations data.',
        variant: 'destructive',
      });
    } finally {
      setOrganizationsLoading(false);
    }
  }, [toast, user?.id, isAdmin]);

  const createTestOrganization = async () => {
    if (!user) return;

    try {
      console.log('Creating test organization...');
      const { data, error } = await supabase
        .from('organizations')
        .insert({
          name: 'Test Organization',
          description: 'This is a test organization for debugging',
          contact_email: '<EMAIL>',
          created_by: user.id,
          approval_status: 'pending'
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating test organization:', error);
        throw error;
      }

      console.log('Test organization created:', data);
      toast({
        title: 'Test organization created',
        description: 'A test organization has been created for debugging.',
      });

      // Refresh the organizations list
      fetchOrganizations();
    } catch (error) {
      console.error('Error creating test organization:', error);
      toast({
        title: 'Error creating test organization',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      });
    }
  };

  useEffect(() => {
    const checkAdminStatus = async () => {
      if (user) {
        try {
          const { data, error } = await supabase
            .from('profiles')
            .select('is_admin')
            .eq('id', user.id)
            .single();

          if (error) throw error;
          setIsAdmin(data?.is_admin || false);
        } catch (error) {
          console.error('Error checking admin status:', error);
          setIsAdmin(false);
        }
      }
    };

    checkAdminStatus();
  }, [user]);

  useEffect(() => {
    console.log('AdminPage useEffect - user:', user?.id, 'isAdmin:', isAdmin);
    if (user && isAdmin) {
      console.log('Fetching admin data...');
      fetchEvents();
      fetchUsers();
      fetchOrganizations();
    }
  }, [user, isAdmin, fetchEvents, fetchUsers, fetchOrganizations]);

  const handleViewDetails = (item: Event | User | Organization) => {
    setSelectedItem(item);
    setIsDetailsOpen(true);
  };

  const openApprovalDialog = (item: Event | Organization, status: ApprovalStatus) => {
    setSelectedItem(item);
    setSelectedStatus(status);
    // Initialize approvalNotes with existing notes from the event/organization or empty string
    setApprovalNotes(item.approval_notes || '');
    setApprovalDialogOpen(true);
  };

  const handleApprovalSubmit = async (notesToSave: string) => {
    if (!selectedItem || !selectedStatus) return;

    setIsSubmitting(true);

    try {
      // Determine if we're updating an event or organization
      const isEvent = 'title' in selectedItem;
      const isOrganization = 'name' in selectedItem && !('title' in selectedItem);

      if (isEvent) {
        // Update event approval status
        const { error } = await supabase
          .from('events')
          .update({
            approval_status: selectedStatus,
            approval_notes: notesToSave,
          })
          .eq('id', selectedItem.id);

        if (error) throw error;
      } else if (isOrganization) {
        // Update organization approval status
        const { error } = await supabase
          .from('organizations')
          .update({
            approval_status: selectedStatus,
            approval_notes: notesToSave,
          })
          .eq('id', selectedItem.id);

        if (error) throw error;
      }

      // If the event is being approved, generate a poster (only for events, not organizations)
      if (selectedStatus === 'approved' && isEvent) {
        // Get the full event details to generate the poster
        const { data: eventData, error: eventError } = await supabase
          .from('events')
          .select(`
            *,
            category: event_categories (*),
            venue: event_venues (*)
          `)
          .eq('id', selectedItem.id)
          .single();

        if (eventError) {
          console.error('Error fetching event details for poster generation:', eventError);
        } else if (eventData && eventData.image_url) {
          // Extract the filename from the image URL
          const imageUrlParts = eventData.image_url.split('/');
          const imageFilename = imageUrlParts[imageUrlParts.length - 1];

          // Format the location string for the poster
          const venue = eventData.venue as any; // Type assertion to handle dynamic fields
          const locationString = venue
            ? `${venue.name}, ${venue.locality || venue.city}`
            : 'Location TBD';

          // Parse the start date
          const startDate = new Date(eventData.start_date);

          // Format the start time
          const startTime = new Date(eventData.start_date).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
          });

          // Format the end time if available
          const endTime = eventData.end_date
            ? new Date(eventData.end_date).toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
              })
            : null;

          // Generate the poster
          const category = eventData.category as any; // Type assertion to handle dynamic fields
          const posterFilename = await generateEventPoster(
            eventData.id,
            imageFilename,
            eventData.title,
            startDate,
            startTime,
            endTime,
            locationString,
            category.name,
            category.color,
            category.text_color
          );

          if (posterFilename) {
            // Update the event with the poster filename
            const success = await updateEventWithPosterFilename(eventData.id, posterFilename);

            if (success) {
              console.log(`Poster generated and saved for event: ${eventData.title}`);
            }
          }
        }
      }

      // Update local state
      if (isEvent) {
        setEvents(events.map(event =>
          event.id === selectedItem.id
            ? { ...event, approval_status: selectedStatus, approval_notes: notesToSave }
            : event
        ));

        toast({
          title: `Event ${selectedStatus}`,
          description: `The event "${(selectedItem as Event).title}" has been ${selectedStatus}.`,
        });
      } else if (isOrganization) {
        setOrganizations(organizations.map(org =>
          org.id === selectedItem.id
            ? { ...org, approval_status: selectedStatus, approval_notes: notesToSave }
            : org
        ));

        toast({
          title: `Organization ${selectedStatus}`,
          description: `The organization "${(selectedItem as Organization).name}" has been ${selectedStatus}.`,
        });
      }

      setApprovalDialogOpen(false);
    } catch (error) {
      const isEvent = 'title' in selectedItem;
      const itemType = isEvent ? 'event' : 'organization';

      console.error(`Error updating ${itemType} approval status:`, error);
      toast({
        title: `Error updating ${itemType}`,
        description: `Could not update ${itemType} approval status.`,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-500">Approved</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Rejected</Badge>;
      case 'pending':
      default:
        return <Badge variant="outline" className="border-amber-500 text-amber-500 bg-amber-500/20">Pending</Badge>;
    }
  };

  // If not admin, show unauthorized message
  if (user && !loading && !isAdmin) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-grow flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <CardTitle>Unauthorized</CardTitle>
              <CardDescription>
                You do not have permission to access the admin dashboard.
              </CardDescription>
            </CardHeader>
          </Card>
        </main>
        <Footer />
      </div>
    );
  }

  // If still loading or not logged in, show loading
  if (loading || !user) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-grow flex items-center justify-center">
          <Loader2 className="h-10 w-10 animate-spin text-blue-primary" />
        </main>
        <Footer />
      </div>
    );
  }

  const DetailDialog = () => {
    if (!selectedItem) return null;

    const isEvent = 'title' in selectedItem;
    const isOrganization = 'name' in selectedItem && !('title' in selectedItem);
    const isUser = !isEvent && !isOrganization;

    const details = isEvent ? (
      <div className="space-y-4">
        <div>
          <h3 className="font-semibold">Title</h3>
          <p>{(selectedItem as Event).title}</p>
        </div>
        <div>
          <h3 className="font-semibold">Description</h3>
          <p className="text-sm">{(selectedItem as Event).description}</p>
        </div>
        <div>
          <h3 className="font-semibold">Category</h3>
          <p>{(selectedItem as Event).category?.name || 'Not specified'}</p>
        </div>
        <div>
          <h3 className="font-semibold">Venue</h3>
          <p>{(selectedItem as Event).venue?.name || (selectedItem as Event).venue_name}</p>
        </div>
        <div>
          <h3 className="font-semibold">Address</h3>
          <p>
            {(selectedItem as Event).venue ? (
              `${(selectedItem as Event).venue.address}, ${(selectedItem as Event).venue.city}, ${(selectedItem as Event).venue.state}`
            ) : (
              `${(selectedItem as Event).address}, ${(selectedItem as Event).city}, ${(selectedItem as Event).state}`
            )}
          </p>
        </div>
        <div>
          <h3 className="font-semibold">Organizer</h3>
          <p>{(selectedItem as Event).organizer_name} ({(selectedItem as Event).organizer_email})</p>
        </div>
        {(selectedItem as Event).approval_status && (
          <div>
            <h3 className="font-semibold">Approval Status</h3>
            <div className="mt-1">{getStatusBadge((selectedItem as Event).approval_status)}</div>
          </div>
        )}
        {(selectedItem as Event).approval_notes && (
          <div>
            <h3 className="font-semibold">Approval Notes</h3>
            <p className="text-sm">{(selectedItem as Event).approval_notes}</p>
          </div>
        )}
      </div>
    ) : isOrganization ? (
      <div className="space-y-4">
        <div>
          <h3 className="font-semibold">Organization Name</h3>
          <p>{(selectedItem as Organization).name}</p>
        </div>
        {(selectedItem as Organization).description && (
          <div>
            <h3 className="font-semibold">Description</h3>
            <p className="text-sm">{(selectedItem as Organization).description}</p>
          </div>
        )}
        {(selectedItem as Organization).website_url && (
          <div>
            <h3 className="font-semibold">Website</h3>
            <p className="text-sm">
              <a
                href={(selectedItem as Organization).website_url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                {(selectedItem as Organization).website_url}
              </a>
            </p>
          </div>
        )}
        {(selectedItem as Organization).contact_email && (
          <div>
            <h3 className="font-semibold">Contact Email</h3>
            <p>{(selectedItem as Organization).contact_email}</p>
          </div>
        )}
        {(selectedItem as Organization).contact_phone && (
          <div>
            <h3 className="font-semibold">Contact Phone</h3>
            <p>{(selectedItem as Organization).contact_phone}</p>
          </div>
        )}
        {((selectedItem as Organization).address || (selectedItem as Organization).city || (selectedItem as Organization).state) && (
          <div>
            <h3 className="font-semibold">Address</h3>
            <p>
              {[
                (selectedItem as Organization).address,
                (selectedItem as Organization).city,
                (selectedItem as Organization).state
              ].filter(Boolean).join(', ')}
            </p>
          </div>
        )}
        <div>
          <h3 className="font-semibold">Approval Status</h3>
          <div className="mt-1">{getStatusBadge((selectedItem as Organization).approval_status)}</div>
        </div>
        {(selectedItem as Organization).approval_notes && (
          <div>
            <h3 className="font-semibold">Approval Notes</h3>
            <p className="text-sm">{(selectedItem as Organization).approval_notes}</p>
          </div>
        )}
        <div>
          <h3 className="font-semibold">Created At</h3>
          <p>{new Date((selectedItem as Organization).created_at).toLocaleString()}</p>
        </div>
      </div>
    ) : (
      <div className="space-y-4">
        <div>
          <h3 className="font-semibold">Username</h3>
          <p>{(selectedItem as User).username}</p>
        </div>
        <div>
          <h3 className="font-semibold">Full Name</h3>
          <p>{(selectedItem as User).full_name}</p>
        </div>
        <div>
          <h3 className="font-semibold">Email</h3>
          <p>{(selectedItem as User).email}</p>
        </div>
        <div>
          <h3 className="font-semibold">Admin Status</h3>
          <p>{(selectedItem as User).is_admin ? 'Admin' : 'Regular User'}</p>
        </div>
        <div>
          <h3 className="font-semibold">Created At</h3>
          <p>{new Date((selectedItem as User).created_at).toLocaleString()}</p>
        </div>
      </div>
    );

    if (isMobile) {
      return (
        <Drawer open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
          <DrawerContent>
            <DrawerHeader>
              <DrawerTitle>{isEvent ? 'Event Details' : 'User Details'}</DrawerTitle>
              <DrawerDescription>
                {isEvent ? 'View event information' : 'View user information'}
              </DrawerDescription>
            </DrawerHeader>
            <div className="p-4">
              {details}
            </div>
          </DrawerContent>
        </Drawer>
      );
    }

    return (
      <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{isEvent ? 'Event Details' : 'User Details'}</DialogTitle>
            <DialogDescription>
              {isEvent ? 'View event information' : 'View user information'}
            </DialogDescription>
          </DialogHeader>
          {details}
        </DialogContent>
      </Dialog>
    );
  };

  const ApprovalDialog = () => {
    if (!selectedItem || !selectedStatus) return null;

    // Local state for notes within the dialog
    const [localApprovalNotes, setLocalApprovalNotes] = useState(approvalNotes);

    // Effect to update local notes if the global notes change (e.g. dialog reopened for same item after notes were cleared)
    useEffect(() => {
      setLocalApprovalNotes(approvalNotes);
    }, [approvalNotes]);

    const isEvent = 'title' in selectedItem;
    const isOrganization = 'name' in selectedItem && !('title' in selectedItem);
    const itemType = isEvent ? 'Event' : 'Organization';

    const title = selectedStatus === 'approved'
      ? `Approve ${itemType}`
      : `Reject ${itemType}`;

    const description = selectedStatus === 'approved'
      ? `Confirm that you want to approve this ${itemType.toLowerCase()}. It will be visible to all users.`
      : `Confirm that you want to reject this ${itemType.toLowerCase()}. It will only be visible to the ${isEvent ? 'organizer' : 'organization owner'}.`;

    const handleDialogClose = (open: boolean) => {
      if (!open) {
        // Reset notes if dialog is closed without submitting
        // setLocalApprovalNotes(''); // This might clear notes prematurely if reopened
      }
      setApprovalDialogOpen(open);
    };

    const handleSubmitAndClose = async () => {
      // Call handleApprovalSubmit with localApprovalNotes directly
      await handleApprovalSubmit(localApprovalNotes);
      // Update AdminPage's approvalNotes for UI consistency after successful submission
      setApprovalNotes(localApprovalNotes);
      // handleApprovalSubmit already closes the dialog and resets isSubmitting
    };


    const content = (
      <div className="space-y-4 py-4">
        <div>
          <h3 className="text-sm font-medium">Event:</h3>
          <p className="font-semibold">{(selectedItem as Event).title}</p>
        </div>
        <div>
          <h3 className="text-sm font-medium mb-2">Add notes (optional):</h3>
          <Textarea
            value={localApprovalNotes}
            onChange={(e) => setLocalApprovalNotes(e.target.value)}
            placeholder="Provide any feedback or reason for the decision"
            className="w-full"
          />
        </div>
      </div>
    );

    const footer = (
      <>
        <Button
          variant="outline"
          onClick={() => handleDialogClose(false)}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          variant={selectedStatus === 'approved' ? 'default' : 'destructive'}
          onClick={handleSubmitAndClose}
          disabled={isSubmitting}
          className={selectedStatus === 'approved' ? 'bg-green-600 hover:bg-green-700' : ''}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              {selectedStatus === 'approved' ? 'Approve' : 'Reject'}
            </>
          )}
        </Button>
      </>
    );

    if (isMobile) {
      return (
        <Drawer open={approvalDialogOpen} onOpenChange={handleDialogClose}>
          <DrawerContent>
            <DrawerHeader>
              <DrawerTitle>{title}</DrawerTitle>
              <DrawerDescription>{description}</DrawerDescription>
            </DrawerHeader>
            <div className="px-4">
              {content}
            </div>
            <DrawerFooter className="flex flex-row justify-end space-x-2">
              {footer}
            </DrawerFooter>
          </DrawerContent>
        </Drawer>
      );
    }

    return (
      <Dialog open={approvalDialogOpen} onOpenChange={handleDialogClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
            <DialogDescription>{description}</DialogDescription>
          </DialogHeader>
          {content}
          <DialogFooter className="flex flex-row justify-end space-x-2">
            {footer}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-grow container mx-auto px-4 py-24">
        <h1 className="text-3xl font-bold mb-8">Admin Dashboard</h1>

        <Tabs defaultValue="events" className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="events" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Events
            </TabsTrigger>
            <TabsTrigger value="users" className="flex items-center gap-2">
              <UserCheck className="h-4 w-4" />
              Users
            </TabsTrigger>
            <TabsTrigger value="organizations" className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Organizations
            </TabsTrigger>
            <TabsTrigger value="categories" className="flex items-center gap-2">
              <Tag className="h-4 w-4" />
              Categories
            </TabsTrigger>
            <TabsTrigger value="venues" className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Venues
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="events" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Events Management</CardTitle>
                <CardDescription>Manage and approve events on the platform.</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center p-8">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-primary" />
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Title</TableHead>
                          <TableHead>Category</TableHead>
                          <TableHead>Organizer</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {events.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center py-4">
                              No events found
                            </TableCell>
                          </TableRow>
                        ) : (
                          events.map((event) => (
                            <TableRow key={event.id}>
                              <TableCell className="font-medium">{event.title}</TableCell>
                              <TableCell>{event.category?.name || 'Not specified'}</TableCell>
                              <TableCell>{event.organizer_name}</TableCell>
                              <TableCell>
                                {getStatusBadge(event.approval_status || 'pending')}
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex justify-end space-x-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleViewDetails(event)}
                                  >
                                    <Info className="h-4 w-4" />
                                    <span className="sr-only">Details</span>
                                  </Button>

                                  {event.approval_status !== 'approved' && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => openApprovalDialog(event, 'approved')}
                                      className="text-green-600 hover:text-green-700 hover:bg-green-50"
                                    >
                                      <CheckCircle className="h-4 w-4" />
                                      <span className="sr-only">Approve</span>
                                    </Button>
                                  )}

                                  {event.approval_status !== 'rejected' && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => openApprovalDialog(event, 'rejected')}
                                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                    >
                                      <XCircle className="h-4 w-4" />
                                      <span className="sr-only">Reject</span>
                                    </Button>
                                  )}
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="users" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Users Management</CardTitle>
                <CardDescription>Manage all users on the platform.</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center p-8">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-primary" />
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Username</TableHead>
                          <TableHead>Full Name</TableHead>
                          <TableHead>Email</TableHead>
                          <TableHead>Role</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {users.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center py-4">
                              No users found
                            </TableCell>
                          </TableRow>
                        ) : (
                          users.map((user) => (
                            <TableRow key={user.id}>
                              <TableCell className="font-medium">{user.username || 'N/A'}</TableCell>
                              <TableCell>{user.full_name || 'N/A'}</TableCell>
                              <TableCell>{user.email || 'N/A'}</TableCell>
                              <TableCell>
                                {user.is_admin ? (
                                  <Badge className="bg-blue-primary">Admin</Badge>
                                ) : (
                                  <Badge variant="outline">User</Badge>
                                )}
                              </TableCell>
                              <TableCell className="text-right">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleViewDetails(user)}
                                >
                                  <Info className="h-4 w-4" />
                                  <span className="sr-only">Details</span>
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="organizations" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Organizations Management ({organizations.length})</CardTitle>
                <CardDescription>Manage and approve organizations on the platform.</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-4 p-4 bg-gray-100 rounded">
                  <h4 className="font-semibold mb-2">Debug Info:</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p><strong>User ID:</strong> {user?.id || 'Not logged in'}</p>
                      <p><strong>Is Admin:</strong> {isAdmin ? 'Yes' : 'No'}</p>
                      <p><strong>Organizations Count:</strong> {organizations.length}</p>
                      <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
                      <p><strong>Orgs Loading:</strong> {organizationsLoading ? 'Yes' : 'No'}</p>
                    </div>
                    <div>
                      <p><strong>Events Count:</strong> {events.length}</p>
                      <p><strong>Users Count:</strong> {users.length}</p>
                      <p><strong>Auth Status:</strong> {user ? 'Authenticated' : 'Not authenticated'}</p>
                    </div>
                  </div>
                  <div className="flex gap-2 mt-3">
                    <Button
                      onClick={() => {
                        console.log('Manual fetch triggered');
                        fetchOrganizations();
                      }}
                      size="sm"
                    >
                      Refresh Organizations
                    </Button>
                    <Button
                      onClick={createTestOrganization}
                      size="sm"
                      variant="outline"
                    >
                      Create Test Organization
                    </Button>
                    <Button
                      onClick={async () => {
                        console.log('Testing direct database query...');

                        // Test 1: Query all organizations without RLS
                        const { data: allOrgs, error: allError, count: allCount } = await supabase
                          .from('organizations')
                          .select('*', { count: 'exact' });
                        console.log('All organizations query:', { data: allOrgs, error: allError, count: allCount });

                        // Test 2: Query with specific filters
                        const { data: pendingOrgs, error: pendingError } = await supabase
                          .from('organizations')
                          .select('*')
                          .eq('approval_status', 'pending');
                        console.log('Pending organizations:', { data: pendingOrgs, error: pendingError });

                        // Test 3: Query with explicit admin check
                        const { data: adminData, error: adminError } = await supabase
                          .from('organizations')
                          .select('*')
                          .order('created_at', { ascending: false });
                        console.log('Admin query (same as fetchOrganizations):', { data: adminData, error: adminError });

                        // Test 4: Check if we can see organizations created by others
                        const { data: otherOrgs, error: otherError } = await supabase
                          .from('organizations')
                          .select('*')
                          .neq('created_by', user?.id);
                        console.log('Organizations by others:', { data: otherOrgs, error: otherError });

                        // Display results in UI
                        toast({
                          title: 'Database Query Results',
                          description: `Total: ${allCount || 0}, Pending: ${pendingOrgs?.length || 0}, By Others: ${otherOrgs?.length || 0}. Check console.`,
                        });
                      }}
                      size="sm"
                      variant="secondary"
                    >
                      Deep DB Query
                    </Button>
                    <Button
                      onClick={async () => {
                        console.log('=== CHECKING USER PROFILE ===');
                        const { data, error } = await supabase
                          .from('profiles')
                          .select('*')
                          .eq('id', user?.id)
                          .single();
                        console.log('User profile query result:', { data, error });

                        if (data) {
                          console.log('User is_admin status:', data.is_admin);
                          console.log('Profile data:', data);
                        }

                        // Test the admin check that RLS uses
                        const { data: adminCheck, error: adminError } = await supabase
                          .from('profiles')
                          .select('is_admin')
                          .eq('id', user?.id)
                          .eq('is_admin', true)
                          .single();
                        console.log('Admin check (RLS style):', { data: adminCheck, error: adminError });

                        toast({
                          title: 'Profile Check Complete',
                          description: `Admin status: ${data?.is_admin ? 'Yes' : 'No'}. Check console for details.`,
                        });
                      }}
                      size="sm"
                      variant="secondary"
                    >
                      Check Profile
                    </Button>
                    <Button
                      onClick={async () => {
                        if (!user) return;
                        console.log('Setting user as admin...');
                        const { data, error } = await supabase
                          .from('profiles')
                          .update({ is_admin: true })
                          .eq('id', user.id)
                          .select()
                          .single();
                        console.log('Admin update result:', { data, error });
                        if (!error) {
                          setIsAdmin(true);
                          toast({
                            title: 'Admin status updated',
                            description: 'You are now an admin. Refreshing data...',
                          });
                          fetchOrganizations();
                        }
                      }}
                      size="sm"
                      variant="destructive"
                    >
                      Make Admin (Debug)
                    </Button>

                  </div>
                </div>
                {organizationsLoading ? (
                  <div className="flex justify-center p-8">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-primary" />
                    <span className="ml-2">Loading organizations...</span>
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Contact Email</TableHead>
                          <TableHead>Created By</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {organizations.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center py-4">
                              No organizations found
                            </TableCell>
                          </TableRow>
                        ) : (
                          organizations.map((organization) => (
                            <TableRow key={organization.id}>
                              <TableCell className="font-medium">{organization.name}</TableCell>
                              <TableCell>{organization.contact_email || 'N/A'}</TableCell>
                              <TableCell>{organization.created_by || 'N/A'}</TableCell>
                              <TableCell>
                                {getStatusBadge(organization.approval_status || 'pending')}
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex justify-end gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleViewDetails(organization)}
                                  >
                                    <Info className="h-4 w-4" />
                                    <span className="sr-only">Details</span>
                                  </Button>
                                  {organization.approval_status === 'pending' && (
                                    <>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => openApprovalDialog(organization, 'approved')}
                                        className="text-green-600 hover:text-green-700"
                                      >
                                        <CheckCircle className="h-4 w-4" />
                                        <span className="sr-only">Approve</span>
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => openApprovalDialog(organization, 'rejected')}
                                        className="text-red-600 hover:text-red-700"
                                      >
                                        <XCircle className="h-4 w-4" />
                                        <span className="sr-only">Reject</span>
                                      </Button>
                                    </>
                                  )}
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="categories" className="space-y-4">
            <CategoryManager />
          </TabsContent>

          <TabsContent value="venues" className="space-y-4">
            <VenueManager />
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Site Settings</CardTitle>
                <CardDescription>Manage global settings for the platform</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground text-center py-8">
                  Settings management coming soon!
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>

      <DetailDialog />
      <ApprovalDialog />
      <Footer />
    </div>
  );
};

export default AdminPage;
