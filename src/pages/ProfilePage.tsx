import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Mail, Phone, Calendar, Edit } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import EditProfileModal from '@/components/profile/EditProfileModal';

const ProfilePage: React.FC = () => {
  const { user, userProfile, loading, userProfileLoading } = useAuth();
  const navigate = useNavigate();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  
  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth');
    }
  }, [user, loading, navigate]);

  if (loading || userProfileLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-grow container mx-auto px-4 pt-24 pb-12 md:pt-28 md:pb-16">
          <LoadingProfileSkeleton />
        </main>
        <Footer />
      </div>
    );
  }

  if (!user || !userProfile) {
    return null;
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-grow container mx-auto px-4 pt-24 pb-12 md:pt-28 md:pb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-4xl mx-auto"
        >
          <h1 className="text-3xl font-bold mb-8 text-center">My Profile</h1>
          
          <Card className="overflow-hidden border border-blue-primary/20 shadow-md">
            <div className="bg-gradient-to-r from-blue-primary to-blue-primary/80 p-6 flex flex-col md:flex-row items-center gap-6">
              <Avatar className="h-24 w-24 border-4 border-white/20 shadow-lg">
                <AvatarImage src={userProfile.avatar_url || undefined} alt={userProfile.full_name || 'User'} />
                <AvatarFallback className="text-2xl bg-white/10 text-white">
                  {getInitials(userProfile.full_name || user.email || 'User')}
                </AvatarFallback>
              </Avatar>
              
              <div className="text-center md:text-left">
                <h2 className="text-2xl font-bold text-white mb-1">
                  {userProfile.full_name || 'User Profile'}
                </h2>
                <p className="text-white/80">{user.email}</p>
                <Button 
                  variant="secondary" 
                  size="sm" 
                  className="mt-3"
                  onClick={() => setIsEditModalOpen(true)}
                >
                  <Edit size={14} className="mr-1" /> Edit Profile
                </Button>
              </div>
            </div>
            
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Email</p>
                  <div className="flex items-center gap-2 p-3 bg-blue-primary/5 rounded-md">
                    <Mail size={18} className="text-blue-primary" />
                    <p className="font-medium">{user.email}</p>
                  </div>
                </div>
                
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Phone Number</p>
                  <div className="flex items-center gap-2 p-3 bg-blue-primary/5 rounded-md">
                    <Phone size={18} className="text-blue-primary" />
                    <p className="font-medium">{userProfile.phone_number || 'Not provided'}</p>
                  </div>
                </div>
                
                <div className="space-y-1 md:col-span-2">
                  <p className="text-sm text-muted-foreground">Member Since</p>
                  <div className="flex items-center gap-2 p-3 bg-blue-primary/5 rounded-md">
                    <Calendar size={18} className="text-blue-primary" />
                    <p className="font-medium">
                      {new Date(user.created_at).toLocaleDateString('en-IN', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </main>
      
      <Footer />
      <EditProfileModal open={isEditModalOpen} onOpenChange={setIsEditModalOpen} />
    </div>
  );
};

const LoadingProfileSkeleton = () => (
  <div className="max-w-4xl mx-auto">
    <Skeleton className="h-10 w-48 mx-auto mb-8" />
    
    <Card className="overflow-hidden">
      <div className="p-6 flex flex-col md:flex-row items-center gap-6">
        <Skeleton className="h-24 w-24 rounded-full" />
        <div className="space-y-2 w-full max-w-xs">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-8 w-32 mt-2" />
        </div>
      </div>
      
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-12 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-12 w-full" />
          </div>
          <div className="space-y-2 md:col-span-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-12 w-full" />
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
);

export default ProfilePage;
