import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Mail, Phone, Calendar, Edit, Building2, Plus, Loader2 } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { useOrganizations } from '@/hooks/useOrganizations';
import { OrganizationList } from '@/components/organizations/OrganizationList';
import { OrganizationRegistrationForm } from '@/components/organizations/OrganizationRegistrationForm';
import { UserOrganization } from '@/types/organization';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import EditProfileModal from '@/components/profile/EditProfileModal';

const ProfilePage: React.FC = () => {
  const { user, userProfile, loading: authLoading, userProfileLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation(); // Get location object
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // State and hooks for Organizations section
  const {
    organizations,
    loading: orgsLoading,
    error: orgsError,
    refetch: refetchOrganizations,
    acceptInvitation,
    declineInvitation,
  } = useOrganizations();
  const [orgActiveTab, setOrgActiveTab] = useState('list');
  
  useEffect(() => {
    if (!authLoading && !user) {
      navigate('/auth');
    }
  }, [user, authLoading, navigate]);

  // Effect to handle incoming navigation state for organization registration
  useEffect(() => {
    if (location.state?.openOrgRegistration) {
      setOrgActiveTab('register');
      // Scroll to the organizations section
      const orgSection = document.getElementById('my-organizations-section');
      if (orgSection) {
        // Use setTimeout to ensure the tab content is rendered before scrolling
        setTimeout(() => {
          orgSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }, 100); // Small delay might be needed
      }
      // Optional: Clear the state to prevent re-triggering on refresh if not desired
      // navigate(location.pathname, { replace: true, state: { ...location.state, openOrgRegistration: undefined } });
    }
  }, [location.state, navigate]);

  if (authLoading || userProfileLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-grow container mx-auto px-4 pt-24 pb-12 md:pt-28 md:pb-16">
          <LoadingProfileSkeleton />
        </main>
        <Footer />
      </div>
    );
  }

  if (!user || !userProfile) {
    return null;
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-grow container mx-auto px-4 pt-24 pb-12 md:pt-28 md:pb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-4xl mx-auto"
        >
          <h1 className="text-3xl font-bold mb-8 text-center">My Profile</h1>
          
          <Card className="overflow-hidden border border-blue-primary/20 shadow-md">
            <div className="bg-gradient-to-r from-blue-primary to-blue-primary/80 p-6 flex flex-col md:flex-row items-center gap-6">
              <Avatar className="h-24 w-24 border-4 border-white/20 shadow-lg">
                <AvatarImage src={userProfile.avatar_url || undefined} alt={userProfile.full_name || 'User'} />
                <AvatarFallback className="text-2xl bg-white/10 text-white">
                  {getInitials(userProfile.full_name || user.email || 'User')}
                </AvatarFallback>
              </Avatar>
              
              <div className="text-center md:text-left">
                <h2 className="text-2xl font-bold text-white mb-1">
                  {userProfile.full_name || 'User Profile'}
                </h2>
                <p className="text-white/80">{user.email}</p>
                <Button 
                  variant="secondary" 
                  size="sm" 
                  className="mt-3"
                  onClick={() => setIsEditModalOpen(true)}
                >
                  <Edit size={14} className="mr-1" /> Edit Profile
                </Button>
              </div>
            </div>
            
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Email</p>
                  <div className="flex items-center gap-2 p-3 bg-blue-primary/5 rounded-md">
                    <Mail size={18} className="text-blue-primary" />
                    <p className="font-medium">{user.email}</p>
                  </div>
                </div>
                
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Phone Number</p>
                  <div className="flex items-center gap-2 p-3 bg-blue-primary/5 rounded-md">
                    <Phone size={18} className="text-blue-primary" />
                    <p className="font-medium">{userProfile.phone_number || 'Not provided'}</p>
                  </div>
                </div>
                
                <div className="space-y-1 md:col-span-2">
                  <p className="text-sm text-muted-foreground">Member Since</p>
                  <div className="flex items-center gap-2 p-3 bg-blue-primary/5 rounded-md">
                    <Calendar size={18} className="text-blue-primary" />
                    <p className="font-medium">
                      {new Date(user.created_at).toLocaleDateString('en-IN', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Organizations Section */}
          <motion.div
            id="my-organizations-section" // Added ID for footer link
            className="mt-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="mb-8">
              <div className="flex items-center gap-3 mb-4">
                <Building2 className="h-8 w-8 text-blue-primary" />
                <h2 className="text-2xl font-bold">My Organizations</h2>
              </div>
              <p className="text-gray-600 dark:text-gray-400">
                Manage your organizations or register a new one.
              </p>
            </div>

            {orgsLoading && !organizations.length && (
              <div className="flex items-center justify-center min-h-[200px]">
                <Loader2 className="h-8 w-8 animate-spin text-blue-primary" />
              </div>
            )}

            {orgsError && (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <p className="text-red-600 mb-4">Error loading organizations: {typeof orgsError === 'string' ? orgsError : (orgsError as Error).message}</p>
                  <Button onClick={() => refetchOrganizations()}>
                    Try Again
                  </Button>
                </CardContent>
              </Card>
            )}

            {!orgsLoading && !orgsError && (
              <Tabs value={orgActiveTab} onValueChange={setOrgActiveTab} className="space-y-6">
                <div className="flex items-center justify-between">
                  <TabsList>
                    <TabsTrigger value="list">My Organizations</TabsTrigger>
                    <TabsTrigger value="register">Register New</TabsTrigger>
                  </TabsList>

                  {orgActiveTab === 'list' && (
                    <Button
                      onClick={() => setOrgActiveTab('register')}
                      className="flex items-center gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      Register Organization
                    </Button>
                  )}
                </div>

                <TabsContent value="list" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Your Organizations</CardTitle>
                      <CardDescription>
                        Organizations you're a member of. You can create events on behalf of approved organizations.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <OrganizationList
                        organizations={organizations}
                        onCreateEvent={(organization: UserOrganization) => navigate('/create', { state: { preSelectedOrganization: organization } })}
                        onManageOrganization={(organization: UserOrganization) => navigate(`/organizations/${organization.id}/manage`)}
                        acceptInvitation={acceptInvitation}
                        declineInvitation={declineInvitation}
                        isProcessingInvitation={orgsLoading} // Use the loading state from the ProfilePage's hook
                      />
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="register" className="space-y-6">
                  <OrganizationRegistrationForm
                    onSuccess={(newOrganization?: UserOrganization) => {
                      setOrgActiveTab('list');
                      refetchOrganizations(); // Refresh the list
                      // If coming from event creation, this logic might need adjustment
                      // For now, just refresh and switch tab.
                      if (newOrganization) {
                        // Potentially navigate back if 'returnTo' logic was needed here.
                        // For simplicity, Profile page doesn't handle 'returnTo' for org registration directly.
                      }
                    }}
                    onCancel={() => setOrgActiveTab('list')}
                  />
                </TabsContent>
              </Tabs>
            )}
          </motion.div>
        </motion.div>
      </main>
      
      <Footer />
      <EditProfileModal open={isEditModalOpen} onOpenChange={setIsEditModalOpen} />
    </div>
  );
};

const LoadingProfileSkeleton = () => (
  <div className="max-w-4xl mx-auto">
    <Skeleton className="h-10 w-48 mx-auto mb-8" />
    
    <Card className="overflow-hidden">
      <div className="p-6 flex flex-col md:flex-row items-center gap-6">
        <Skeleton className="h-24 w-24 rounded-full" />
        <div className="space-y-2 w-full max-w-xs">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-8 w-32 mt-2" />
        </div>
      </div>
      
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-12 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-12 w-full" />
          </div>
          <div className="space-y-2 md:col-span-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-12 w-full" />
          </div>
        </div>
      </CardContent>
    </Card>
    {/* Skeleton for Organizations Section - can be added if desired */}
    <div className="mt-12">
      <Skeleton className="h-8 w-64 mb-4" />
      <Skeleton className="h-4 w-full mb-8" />
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-40 mb-2" />
          <Skeleton className="h-4 w-full" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-20 w-full" />
        </CardContent>
      </Card>
    </div>
  </div>
);

export default ProfilePage;
