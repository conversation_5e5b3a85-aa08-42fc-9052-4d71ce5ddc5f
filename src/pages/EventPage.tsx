
import React from 'react';
import { useParams } from 'react-router-dom';
import Header from '../components/layout/Header';
import Footer from '../components/layout/Footer';
import EventDetail from '../components/events/EventDetail';

const EventPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-grow">
        <EventDetail />
      </main>
      
      <Footer />
    </div>
  );
};

export default EventPage;
