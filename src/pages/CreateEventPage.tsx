import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { Form } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Accordion } from "@/components/ui/enhanced-accordion";

// Layout components
import Header from '../components/layout/Header';
import Footer from '../components/layout/Footer';
import LocationGate from '../components/location/LocationGate';

// Form sections
import BasicInfoSection from '../components/events/BasicInfoSection';
import EventDetailsSection from '../components/events/EventDetailsSection';
import LocationSection from '../components/events/LocationSection';
import OrganizerSection from '../components/events/OrganizerSection';
import TicketingSection from '../components/events/TicketingSection';
import OrganizerTermsSection from '../components/events/OrganizerTermsSection'; // Added import
import TermsSection from '../components/events/TermsSection';
import FormErrorSummary from '../components/events/FormErrorSummary';

// Hooks
import { useEventForm } from '@/hooks/useEventForm';
import { UserOrganization } from '@/types/organization';

const CreateEventPage: React.FC = () => {
  const location = useLocation();
  const preSelectedOrganization = location.state?.selectedOrganization as UserOrganization | undefined;
  const {
    form,
    isFree,
    isMultiDay,
    hasEarlyBird,
    coverImageUrl,
    additionalImageUrls,
    handleImageChange,
    handleImageClick,
    handleAdditionalImagesChange,
    handleAdditionalImagesClick,
    removeAdditionalImage,
    createVenue,
    onSubmit,
    getSectionErrorCount,
    getFirstErrorSection,
    getAllErrorSummary,
    getSectionCompletionStatus,
  } = useEventForm();

  const [accordionValue, setAccordionValue] = useState<string | undefined>("item-1");
  const [showValidation, setShowValidation] = useState(false);

  // Watch form values to trigger progress indicator updates
  const watchedValues = form.watch();
  const [hasHandledInitialSubmission, setHasHandledInitialSubmission] = useState(false);

  // Show validation and scroll to first error section when validation fails (only on initial submission)
  useEffect(() => {
    if (form.formState.isSubmitted && !form.formState.isValid && !hasHandledInitialSubmission) {
      setShowValidation(true);
      setHasHandledInitialSubmission(true);

      const firstErrorSection = getFirstErrorSection();
      if (firstErrorSection) {
        // Auto-expand the first error section
        setAccordionValue(firstErrorSection);

        // Scroll to the first error section
        setTimeout(() => {
          const element = document.querySelector(`[data-value="${firstErrorSection}"]`);
          if (element) {
            element.scrollIntoView({
              behavior: 'smooth',
              block: 'start',
              inline: 'nearest'
            });
          }
        }, 100);
      }
    }
  }, [form.formState.isSubmitted, form.formState.isValid, hasHandledInitialSubmission, getFirstErrorSection]);

  // Set default values for pre-selected organization
  useEffect(() => {
    if (preSelectedOrganization) {
      form.setValue('hostedByType', 'organization');
      form.setValue('organizationId', preSelectedOrganization.id);
    }
  }, [preSelectedOrganization, form]);

  // Reset the flag when form becomes valid or when starting a new submission
  useEffect(() => {
    if (form.formState.isValid || !form.formState.isSubmitted) {
      setHasHandledInitialSubmission(false);
    }
  }, [form.formState.isValid, form.formState.isSubmitted]);

  const handleSectionClick = (sectionId: string) => {
    // Allow free navigation to any section
    setAccordionValue(sectionId);

    // Scroll to the section after a short delay
    setTimeout(() => {
      const element = document.querySelector(`[data-value="${sectionId}"]`);
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      }
    }, 100);
  };

  // Create an empty venue data object for the LocationSection
  const emptyVenueData = {
    venue_id: null,
    venue_name: '',
    address: '',
    city: '',
    state: '',
    zip_code: ''
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <LocationGate>
        <main className="flex-grow pt-24 pb-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-3xl font-bold font-raleway mb-6">Host an Event</h1>

            <div className="glass-card p-6 rounded-xl mb-8">
              <p className="text-muted-foreground mb-6">
                Share your passion with the community. Fill out the form below to create a new event.
              </p>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">

                  {/* Error Summary */}
                  {showValidation && (
                    <FormErrorSummary
                      errorSummary={getAllErrorSummary()}
                      onSectionClick={handleSectionClick}
                      className="mb-6"
                    />
                  )}

                  <Accordion
                    type="single"
                    collapsible
                    value={accordionValue}
                    onValueChange={(value) => {
                      // Allow free navigation - including collapsing all sections
                      setAccordionValue(value);
                    }}
                  >
                    <BasicInfoSection
                      control={form.control}
                      coverImageUrl={coverImageUrl}
                      additionalImageUrls={additionalImageUrls}
                      onImageSelect={handleImageClick}
                      onAdditionalImagesSelect={handleAdditionalImagesClick}
                      onRemoveAdditionalImage={removeAdditionalImage}
                      errorCount={getSectionErrorCount('item-1')}
                      isValid={getSectionErrorCount('item-1') === 0}
                      showValidation={showValidation}
                      completionStatus={getSectionCompletionStatus('item-1')}
                      showProgressIndicators={!showValidation}
                    />

                    <EventDetailsSection
                      control={form.control}
                      isMultiDay={isMultiDay}
                      getValues={form.getValues}
                      errorCount={getSectionErrorCount('item-2')}
                      isValid={getSectionErrorCount('item-2') === 0}
                      showValidation={showValidation}
                      completionStatus={getSectionCompletionStatus('item-2')}
                      showProgressIndicators={!showValidation}
                    />

                    <LocationSection
                      control={form.control}
                      errorCount={getSectionErrorCount('item-3')}
                      isValid={getSectionErrorCount('item-3') === 0}
                      showValidation={showValidation}
                      completionStatus={getSectionCompletionStatus('item-3')}
                      showProgressIndicators={!showValidation}
                    />

                    <OrganizerSection
                      control={form.control}
                      errorCount={getSectionErrorCount('item-4')}
                      isValid={getSectionErrorCount('item-4') === 0}
                      showValidation={showValidation}
                      completionStatus={getSectionCompletionStatus('item-4')}
                      showProgressIndicators={!showValidation}
                      preSelectedOrganization={preSelectedOrganization}
                    />

                    <TicketingSection
                      control={form.control}
                      isFree={isFree}
                      hasEarlyBird={hasEarlyBird}
                      getValues={form.getValues}
                      setValue={form.setValue}
                      errorCount={getSectionErrorCount('item-5')}
                      isValid={getSectionErrorCount('item-5') === 0}
                      showValidation={showValidation}
                      completionStatus={getSectionCompletionStatus('item-5')}
                      showProgressIndicators={!showValidation}
                    />

                    <OrganizerTermsSection
                      control={form.control}
                      errorCount={getSectionErrorCount('item-6')}
                      isValid={getSectionErrorCount('item-6') === 0}
                      showValidation={showValidation}
                      completionStatus={getSectionCompletionStatus('item-6')}
                      showProgressIndicators={!showValidation}
                    />

                    <TermsSection
                      control={form.control}
                      errorCount={getSectionErrorCount('item-7')}
                      isValid={getSectionErrorCount('item-7') === 0}
                      showValidation={showValidation}
                      completionStatus={getSectionCompletionStatus('item-7')}
                      showProgressIndicators={!showValidation}
                    />
                  </Accordion>

                  <div className="flex items-center justify-end space-x-4 pt-6">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => history.back()}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="bg-blue-primary hover:bg-blue-secondary text-white"
                    >
                      Submit Event
                    </Button>
                  </div>

                  {/* Hidden file inputs for image uploads */}
                  <input
                    id="eventImage"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleImageChange}
                  />
                  <input
                    id="additionalImages"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    multiple
                    onChange={handleAdditionalImagesChange}
                  />
                </form>
              </Form>
            </div>
          </div>
        </div>
        </main>
      </LocationGate>

      <Footer />
    </div>
  );
};

export default CreateEventPage;
