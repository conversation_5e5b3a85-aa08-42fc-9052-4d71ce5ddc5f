
import React from 'react';
import { motion } from 'framer-motion';
import Header from '../components/layout/Header';
import Footer from '../components/layout/Footer';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const AboutPage: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-grow pt-24 pb-16">
        <div className="container mx-auto px-4 md:px-6">
          {/* Hero Section */}
          <section className="mb-16">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <h1 className="text-4xl font-bold font-raleway mb-4">About TheLocalAdda</h1>
                <p className="text-xl text-muted-foreground mb-6">
                  Connecting communities through local events
                </p>
                <p className="mb-6">
                  TheLocalAdda was born out of the need for a local solution to help parents in Delhi connect and find playmates for their children—an area where such connections are currently scarce.
                </p>
                <p className="mb-6">
                  Our vision goes beyond just children's play dates. We aim to serve as a dynamic community hub for all local events and gatherings, enabling organizers to showcase a diverse range of activities while residents discover opportunities that match their interests.
                </p>
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <img 
                  src="https://images.unsplash.com/photo-1529156069898-49953e39b3ac?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2078&q=80" 
                  alt="Community gathering"
                  className="w-full h-auto rounded-2xl shadow-smooth"
                />
              </motion.div>
            </div>
          </section>
          
          {/* Mission Section */}
          <section className="mb-16 py-16 bg-gradient-to-b from-white to-muted/30 rounded-2xl">
            <div className="max-w-3xl mx-auto text-center">
              <motion.h2 
                className="text-3xl font-bold font-raleway mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                Our Mission
              </motion.h2>
              <motion.p
                className="text-xl mb-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                To build stronger, more connected communities by making local events more accessible and discoverable.
              </motion.p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="glass-card p-6 rounded-xl"
                >
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-blue-primary/10 flex items-center justify-center">
                    <span className="text-2xl">🤝</span>
                  </div>
                  <h3 className="text-xl font-semibold font-raleway mb-2">Connect</h3>
                  <p className="text-muted-foreground">
                    Bringing families and individuals together through shared experiences and interests
                  </p>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="glass-card p-6 rounded-xl"
                >
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-blue-primary/10 flex items-center justify-center">
                    <span className="text-2xl">🎯</span>
                  </div>
                  <h3 className="text-xl font-semibold font-raleway mb-2">Discover</h3>
                  <p className="text-muted-foreground">
                    Making it easy to find events that match your interests and are close to home
                  </p>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  className="glass-card p-6 rounded-xl"
                >
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-blue-primary/10 flex items-center justify-center">
                    <span className="text-2xl">🌱</span>
                  </div>
                  <h3 className="text-xl font-semibold font-raleway mb-2">Grow</h3>
                  <p className="text-muted-foreground">
                    Building stronger communities by fostering meaningful relationships
                  </p>
                </motion.div>
              </div>
            </div>
          </section>
          
          {/* Team Section */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold font-raleway mb-8 text-center">Our Team</h2>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  name: "Priya Sharma",
                  role: "Founder & CEO",
                  bio: "Former community organizer with a passion for bringing people together.",
                  image: "https://randomuser.me/api/portraits/women/44.jpg"
                },
                {
                  name: "Arjun Patel",
                  role: "Head of Operations",
                  bio: "Event management expert with 10+ years of experience in the Delhi region.",
                  image: "https://randomuser.me/api/portraits/men/32.jpg"
                },
                {
                  name: "Neha Gupta",
                  role: "Community Manager",
                  bio: "Passionate about creating meaningful connections in local communities.",
                  image: "https://randomuser.me/api/portraits/women/68.jpg"
                }
              ].map((member, index) => (
                <motion.div
                  key={member.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 * index }}
                  className="glass-card p-6 rounded-xl text-center"
                >
                  <img 
                    src={member.image} 
                    alt={member.name}
                    className="w-24 h-24 mx-auto mb-4 rounded-full object-cover"
                  />
                  <h3 className="text-xl font-semibold font-raleway mb-1">{member.name}</h3>
                  <p className="text-blue-primary mb-2">{member.role}</p>
                  <p className="text-muted-foreground">{member.bio}</p>
                </motion.div>
              ))}
            </div>
          </section>
          
          {/* CTA Section */}
          <section className="mb-16">
            <div className="glass-card overflow-hidden rounded-2xl shadow-smooth-lg">
              <div className="p-8 md:p-12 text-center">
                <h2 className="text-3xl font-bold font-raleway mb-4">Ready to Join Our Community?</h2>
                <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
                  Start exploring events in your neighborhood or create your own to bring people together.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button 
                    className="bg-blue-primary hover:bg-blue-secondary text-white"
                    size="lg"
                    asChild
                  >
                    <Link to="/events">
                      Explore Events
                    </Link>
                  </Button>
                  <Button 
                    variant="outline"
                    className="border-blue-primary text-blue-primary hover:bg-blue-primary hover:text-white"
                    size="lg"
                    asChild
                  >
                    <Link to="/create">
                      Host an Event
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </section>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default AboutPage;
