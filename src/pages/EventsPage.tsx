import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { CalendarDays, MapPin, Clock4, Filter, X, ChevronDown, PlusCircle, Calendar, Loader2, SlidersHorizontal } from 'lucide-react';
import { format, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, addDays, isWithinInterval } from 'date-fns';

import Header from '../components/layout/Header';
import Footer from '../components/layout/Footer';
import EventCard from '../components/home/<USER>';
import LocationGate from '../components/location/LocationGate';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '../components/ui/pagination';
import { useEvents, Event as EventType } from '../hooks/useEvents';
import SearchBar from '../components/ui/SearchBar';
import { useCategories } from '@/hooks/useCategories';
import { useVenuesWithEvents } from '@/hooks/useVenuesWithEvents';
import CategoryBadge from '@/components/ui/CategoryBadge';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle, DrawerTrigger } from '@/components/ui/drawer';
import { useAuth } from '@/context/AuthContext';

type DateFilter = 'all' | 'today' | 'week' | 'next-week' | 'weekend' | 'next-weekend' | 'month' | 'next-month' | 'past';

const EventsPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const queryParams = new URLSearchParams(location.search);
  const { user } = useAuth();
  const observerTarget = useRef<HTMLDivElement>(null);

  const [searchTerm, setSearchTerm] = useState(queryParams.get('search') || '');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(queryParams.get('category'));
  const [selectedVenue, setSelectedVenue] = useState<string | null>(queryParams.get('venue'));
  const [dateFilter, setDateFilter] = useState<DateFilter>('all');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [totalEvents, setTotalEvents] = useState(0);
  const [showAllCategories, setShowAllCategories] = useState(false);
  const [showAllVenues, setShowAllVenues] = useState(false);
  const [activeFilter, setActiveFilter] = useState<'category' | 'venue' | 'date' | null>(null);
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [isDesktopPanelCollapsed, setIsDesktopPanelCollapsed] = useState(false);
  const [collapsedSections, setCollapsedSections] = useState<{[key: string]: boolean}>({
    categories: true,
    venues: true,
    dates: true
  });
  const [categorySearchTerm, setCategorySearchTerm] = useState('');
  const [venueSearchTerm, setVenueSearchTerm] = useState('');
  const [events, setEvents] = useState<EventType[]>([]);
  const [loading, setLoading] = useState(true);
  const [initialLoad, setInitialLoad] = useState(true);

  const { fetchPaginatedEvents } = useEvents();
  const { activeCategories } = useCategories();
  const { venues: venuesWithEvents } = useVenuesWithEvents(50); // Get up to 50 venues with active events
  const eventsPerPage = 10;

  // Venues are already sorted by event count (descending) from the hook
  const sortedVenues = venuesWithEvents;

  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams();

    if (searchTerm) {
      params.set('search', searchTerm);
    }

    if (selectedCategory) {
      params.set('category', selectedCategory);
    }

    if (selectedVenue) {
      params.set('venue', selectedVenue);
    }

    const newUrl = `${location.pathname}${params.toString() ? `?${params.toString()}` : ''}`;
    navigate(newUrl, { replace: true });
  }, [searchTerm, selectedCategory, selectedVenue, navigate, location.pathname]);

  // Date filter functions
  const getDateRange = (filter: DateFilter): { start: Date, end: Date } | null => {
    const today = new Date();

    switch (filter) {
      case 'today': {
        return {
          start: startOfDay(today),
          end: endOfDay(today)
        };
      }
      case 'week': {
        return {
          start: startOfWeek(today, { weekStartsOn: 1 }),
          end: endOfWeek(today, { weekStartsOn: 1 })
        };
      }
      case 'next-week': {
        // Find next week's date range (Monday to Sunday after this week)
        const thisWeekEnd = endOfWeek(today, { weekStartsOn: 1 });
        const nextWeekStart = startOfDay(addDays(thisWeekEnd, 1)); // Monday of next week
        const nextWeekEnd = endOfDay(addDays(nextWeekStart, 6)); // Sunday of next week
        return {
          start: nextWeekStart,
          end: nextWeekEnd
        };
      }
      case 'weekend': {
        // Find the upcoming weekend (Saturday and Sunday)
        const saturday = startOfDay(addDays(today, (6 - today.getDay()) % 7));
        const sunday = endOfDay(addDays(saturday, 1));
        return {
          start: saturday,
          end: sunday
        };
      }
      case 'next-weekend': {
        // Find next weekend (Saturday and Sunday after the upcoming one)
        const thisWeekend = addDays(today, (6 - today.getDay()) % 7); // This Saturday
        const nextWeekendStart = startOfDay(addDays(thisWeekend, 7)); // Next Saturday
        const nextWeekendEnd = endOfDay(addDays(nextWeekendStart, 1)); // Next Sunday
        return {
          start: nextWeekendStart,
          end: nextWeekendEnd
        };
      }
      case 'month': {
        return {
          start: startOfMonth(today),
          end: endOfMonth(today)
        };
      }
      case 'next-month': {
        // Find next month's date range
        const nextMonthDate = addDays(endOfMonth(today), 1); // First day of next month
        return {
          start: startOfMonth(nextMonthDate),
          end: endOfMonth(nextMonthDate)
        };
      }
      case 'past': {
        // For past events, we don't need to set a date range
        // We'll handle this in the loadEvents function
        return null;
      }
      default:
        return null; // No date filtering
    }
  };

  // Load events with server-side filtering and pagination
  const loadEvents = useCallback(async (resetEvents = false) => {
    try {
      setLoading(true);

      // Prepare date range for filtering
      const dateRange = getDateRange(dateFilter);

      // Fetch events with pagination and filters
      const result = await fetchPaginatedEvents({
        search: searchTerm || undefined,
        categoryId: selectedCategory || undefined,
        venueId: selectedVenue || undefined,
        dateStart: dateRange?.start ? dateRange.start.toISOString() : undefined,
        dateEnd: dateRange?.end ? dateRange.end.toISOString() : undefined,
        page: resetEvents ? 1 : page,
        limit: eventsPerPage,
        includePastEvents: dateFilter === 'past'
      });

      setTotalEvents(result.count);
      setHasMore(result.hasMore);

      // If resetting events (due to filter change), replace the current list
      // Otherwise append to the existing list for infinite scrolling
      if (resetEvents) {
        setEvents(result.data);
        setPage(1);
      } else {
        setEvents(prev => [...prev, ...result.data]);
      }
    } catch (error) {
      console.error('Error loading events:', error);
    } finally {
      setLoading(false);
      setInitialLoad(false);
    }
  }, [
    fetchPaginatedEvents,
    searchTerm,
    selectedCategory,
    selectedVenue,
    dateFilter,
    page,
    eventsPerPage
  ]);

  // Initial load and filter changes
  useEffect(() => {
    // Reset events and load first page when filters change
    loadEvents(true);
  }, [searchTerm, selectedCategory, selectedVenue, dateFilter]);

  // Set up intersection observer for infinite scrolling
  useEffect(() => {
    if (initialLoad) return;

    const observer = new IntersectionObserver(
      entries => {
        // If the target element is visible and we have more events to load
        if (entries[0].isIntersecting && hasMore && !loading) {
          setPage(prevPage => prevPage + 1);
        }
      },
      { threshold: 0.1 }
    );

    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => {
      if (observerTarget.current) {
        observer.unobserve(observerTarget.current);
      }
    };
  }, [hasMore, loading, initialLoad]);

  // Load more events when page changes
  useEffect(() => {
    if (page > 1 && !initialLoad) {
      loadEvents(false);
    }
  }, [page, initialLoad, loadEvents]);

  const handleCategoryChange = (categoryId: string | null) => {
    if (selectedCategory === categoryId) {
      setSelectedCategory(null); // Toggle off if already selected
    } else {
      setSelectedCategory(categoryId);
    }
    // Reset will happen in the useEffect
  };

  const handleVenueChange = (venueId: string | null) => {
    if (selectedVenue === venueId) {
      setSelectedVenue(null); // Toggle off if already selected
    } else {
      setSelectedVenue(venueId);
    }
    // Reset will happen in the useEffect
  };

  const handleDateChange = (filter: DateFilter) => {
    setDateFilter(filter);
    // Reset will happen in the useEffect
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    // Reset will happen in the useEffect
  };

  // Filter categories based on search term
  const filteredCategories = activeCategories?.filter(category =>
    category.name.toLowerCase().includes(categorySearchTerm.toLowerCase())
  ) || [];

  // Filter venues based on search term
  const filteredVenues = sortedVenues.filter(venue =>
    venue.name.toLowerCase().includes(venueSearchTerm.toLowerCase())
  );

  // Get top featured categories and venues for quick filters
  const featuredCategories = filteredCategories.slice(0, 5) || [];
  const remainingCategories = filteredCategories.slice(5) || [];

  // Show all venues with active events (filtered by search)
  const allVenuesWithEvents = filteredVenues;

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCategory(null);
    setSelectedVenue(null);
    setDateFilter('all');
    navigate('/events');
  };

  const toggleSection = (section: string) => {
    setCollapsedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const isFiltersApplied = searchTerm || selectedCategory || selectedVenue || dateFilter !== 'all';

  // Mobile filter UI logic
  const toggleMobileFilter = (filter: 'category' | 'venue' | 'date') => {
    setActiveFilter(activeFilter === filter ? null : filter);
  };

  // Get label for active date filter
  const getDateFilterLabel = (filter: DateFilter): string => {
    switch (filter) {
      case 'today': return 'Today';
      case 'week': return 'This Week';
      case 'next-week': return 'Next Week';
      case 'weekend': return 'This Weekend';
      case 'next-weekend': return 'Next Weekend';
      case 'month': return 'This Month';
      case 'next-month': return 'Next Month';
      case 'past': return 'Past Events';
      default: return 'Any Date';
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <LocationGate>
        <div className="flex-grow relative min-h-screen pt-16">
          <div className="flex h-full">
          {/* Filter Panel Overlay */}
          {isFilterPanelOpen && (
            <div
              className="fixed inset-0 bg-black/50 z-40 lg:hidden"
              onClick={() => setIsFilterPanelOpen(false)}
            />
          )}

            {/* Left Filter Panel */}
            <div className={`
              fixed lg:sticky top-0 left-0 h-screen lg:h-[calc(100vh-4rem)]
              ${isDesktopPanelCollapsed ? 'w-12 lg:w-12' : 'w-80'} bg-gray-50/80 backdrop-blur-sm border-r border-gray-200 z-50 lg:z-auto
              transform transition-all duration-300 ease-in-out
              ${isFilterPanelOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
              overflow-y-auto overflow-x-hidden lg:self-start flex-shrink-0
            `}>
            {/* Desktop Collapse Toggle - Always visible on desktop */}
            <div className="hidden lg:block absolute -right-4 top-4 z-10">
              <Button
                variant="outline"
                size="lg"
                onClick={() => setIsDesktopPanelCollapsed(!isDesktopPanelCollapsed)}
                className="w-10 h-12 p-0 bg-yellow-400 border-2 border-yellow-500 rounded-lg shadow-lg hover:bg-yellow-300 hover:shadow-xl transition-all duration-200"
                title={isDesktopPanelCollapsed ? "Expand filters" : "Collapse filters"}
              >
                <SlidersHorizontal size={20} className="text-gray-800" />
              </Button>
            </div>

            {/* Panel Content */}
            <div className={`transition-opacity duration-300 ${isDesktopPanelCollapsed ? 'lg:opacity-0 lg:pointer-events-none' : 'opacity-100'} p-6`}>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-base font-semibold">Filters</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsFilterPanelOpen(false)}
                  className="lg:hidden"
                >
                  <X size={16} />
                </Button>
              </div>

              {/* Filter Content */}
              <div className="space-y-4">
                {/* Categories Filter */}
                <div>
                  <button
                    onClick={() => toggleSection('categories')}
                    className="flex items-center justify-between w-full text-left mb-2 hover:bg-gray-50 p-2 rounded-md"
                  >
                    <h3 className="text-xs font-medium uppercase tracking-wide text-gray-600">Categories</h3>
                    <ChevronDown
                      size={14}
                      className={`transform transition-transform ${collapsedSections.categories ? '-rotate-90' : ''}`}
                    />
                  </button>
                  {!collapsedSections.categories && (
                    <div className="space-y-2 pl-2">
                      {/* Category Search */}
                      <div className="relative">
                        <input
                          type="text"
                          placeholder="Search categories..."
                          value={categorySearchTerm}
                          onChange={(e) => setCategorySearchTerm(e.target.value)}
                          className="w-full px-3 py-2 text-xs border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        {categorySearchTerm && (
                          <button
                            onClick={() => setCategorySearchTerm('')}
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                          >
                            <X size={14} />
                          </button>
                        )}
                      </div>

                      {/* Categories List */}
                      <div className="space-y-1">
                        {featuredCategories.map(category => (
                        <button
                          key={category.id}
                          onClick={(e) => {
                            e.preventDefault();
                            handleCategoryChange(category.id);
                          }}
                          className={`w-full text-left px-2 py-1.5 rounded-md transition-colors text-xs ${
                            selectedCategory === category.id
                              ? 'bg-blue-50 text-blue-700 border border-blue-200'
                              : 'hover:bg-gray-50'
                          }`}
                        >
                          <div className="flex items-center gap-2">
                            <div
                              className="w-2 h-2 rounded-full"
                              style={{ backgroundColor: category.color }}
                            />
                            {category.name}
                          </div>
                        </button>
                      ))}

                      {showAllCategories && remainingCategories.map(category => (
                        <button
                          key={category.id}
                          onClick={() => handleCategoryChange(category.id)}
                          className={`w-full text-left px-2 py-1.5 rounded-md transition-colors text-xs ${
                            selectedCategory === category.id
                              ? 'bg-blue-50 text-blue-700 border border-blue-200'
                              : 'hover:bg-gray-50'
                          }`}
                        >
                          <div className="flex items-center gap-2">
                            <div
                              className="w-2 h-2 rounded-full"
                              style={{ backgroundColor: category.color }}
                            />
                            {category.name}
                          </div>
                        </button>
                      ))}

                      {remainingCategories.length > 0 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowAllCategories(!showAllCategories)}
                          className="w-full justify-start text-blue-600 hover:text-blue-700 text-xs h-7"
                        >
                          {showAllCategories ? 'Show Less' : `+ ${remainingCategories.length} More`}
                        </Button>
                      )}

                      {filteredCategories.length === 0 && categorySearchTerm && (
                        <div className="text-xs text-gray-500 px-2 py-1">
                          No categories found for "{categorySearchTerm}"
                        </div>
                      )}
                      </div>
                    </div>
                  )}
                </div>

                {/* Venues Filter */}
                <div>
                  <button
                    onClick={() => toggleSection('venues')}
                    className="flex items-center justify-between w-full text-left mb-2 hover:bg-gray-50 p-2 rounded-md"
                  >
                    <h3 className="text-xs font-medium uppercase tracking-wide text-gray-600">Venues</h3>
                    <ChevronDown
                      size={14}
                      className={`transform transition-transform ${collapsedSections.venues ? '-rotate-90' : ''}`}
                    />
                  </button>
                  {!collapsedSections.venues && (
                    <div className="space-y-2 pl-2">
                      {/* Venue Search */}
                      <div className="relative">
                        <input
                          type="text"
                          placeholder="Search venues..."
                          value={venueSearchTerm}
                          onChange={(e) => setVenueSearchTerm(e.target.value)}
                          className="w-full px-3 py-2 text-xs border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        {venueSearchTerm && (
                          <button
                            onClick={() => setVenueSearchTerm('')}
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                          >
                            <X size={14} />
                          </button>
                        )}
                      </div>

                      {/* Venues List */}
                      <div className="space-y-1">
                        {allVenuesWithEvents.map(venue => (
                        <button
                          key={venue.id}
                          onClick={(e) => {
                            e.preventDefault();
                            handleVenueChange(venue.id);
                          }}
                          className={`w-full text-left px-2 py-1.5 rounded-md transition-colors text-xs ${
                            selectedVenue === venue.id
                              ? 'bg-blue-50 text-blue-700 border border-blue-200'
                              : 'hover:bg-gray-50'
                          }`}
                        >
                          <div className="flex items-center gap-2">
                            <MapPin size={12} className="text-gray-500" />
                            <span className="truncate">{venue.name}</span>
                            <span className="text-xs text-gray-400 ml-auto">({venue.eventCount})</span>
                          </div>
                        </button>
                      ))}

                      {allVenuesWithEvents.length === 0 && !venueSearchTerm && (
                        <div className="text-xs text-gray-500 px-2 py-1">
                          No venues with active events found
                        </div>
                      )}

                      {allVenuesWithEvents.length === 0 && venueSearchTerm && (
                        <div className="text-xs text-gray-500 px-2 py-1">
                          No venues found for "{venueSearchTerm}"
                        </div>
                      )}
                      </div>
                    </div>
                  )}
                </div>

                {/* Date Filter */}
                <div>
                  <button
                    onClick={() => toggleSection('dates')}
                    className="flex items-center justify-between w-full text-left mb-2 hover:bg-gray-50 p-2 rounded-md"
                  >
                    <h3 className="text-xs font-medium uppercase tracking-wide text-gray-600">Date</h3>
                    <ChevronDown
                      size={14}
                      className={`transform transition-transform ${collapsedSections.dates ? '-rotate-90' : ''}`}
                    />
                  </button>
                  {!collapsedSections.dates && (
                    <div className="space-y-1 pl-2">
                      {(['all', 'today', 'week', 'next-week', 'weekend', 'next-weekend', 'month', 'next-month', 'past'] as DateFilter[]).map((filter) => (
                        <button
                          key={filter}
                          onClick={() => handleDateChange(filter)}
                          className={`w-full text-left px-2 py-1.5 rounded-md transition-colors text-xs ${
                            dateFilter === filter
                              ? 'bg-blue-50 text-blue-700 border border-blue-200'
                              : 'hover:bg-gray-50'
                          }`}
                        >
                          <div className="flex items-center gap-2">
                            <Calendar size={12} className="text-gray-500" />
                            {getDateFilterLabel(filter)}
                          </div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                {/* Clear Filters */}
                {isFiltersApplied && (
                  <div className="pt-4 border-t">
                    <Button
                      variant="outline"
                      onClick={clearFilters}
                      className="w-full"
                    >
                      <X size={16} className="mr-2" />
                      Clear All Filters
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>

            {/* Main Content */}
            <main className={`
              flex-1 overflow-y-auto
              container mx-auto px-4 py-6 w-full
            `}>
            <div className="mb-6">
              <h1 className="text-3xl font-bold mb-2">Discover Events</h1>
              <p className="text-muted-foreground">Find events that matter to you and your community</p>
            </div>

            {/* Search and Filter Toggle */}
            <div className="flex flex-col sm:flex-row gap-3 justify-between items-start sm:items-center mb-6">
              <div className="flex items-center gap-3 flex-1">
                {/* Filter Toggle Button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsFilterPanelOpen(!isFilterPanelOpen)}
                  className="lg:hidden flex items-center gap-2"
                >
                  <SlidersHorizontal size={16} />
                  Filters
                  {isFiltersApplied && (
                    <span className="bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {[selectedCategory, selectedVenue, dateFilter !== 'all' ? dateFilter : null, searchTerm].filter(Boolean).length}
                    </span>
                  )}
                </Button>

                <div className="flex-1">
                  <SearchBar onSearch={handleSearch} placeholder="Search events..." initialValue={searchTerm} />
                </div>
              </div>

              {/* Applied Filters Summary - Mobile */}
              {isFiltersApplied && (
                <div className="lg:hidden w-full">
                  <div className="bg-blue-50/50 rounded-lg p-3 flex items-center justify-between">
                    <div className="text-sm">
                      <span className="font-medium">Active filters: </span>
                      {[selectedCategory && activeCategories?.find(c => c.id === selectedCategory)?.name,
                        selectedVenue && allVenuesWithEvents.find(v => v.id === selectedVenue)?.name,
                        dateFilter !== 'all' && getDateFilterLabel(dateFilter),
                        searchTerm && `"${searchTerm}"`
                      ].filter(Boolean).join(', ')}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearFilters}
                      className="text-blue-500 hover:text-blue-700"
                    >
                      Clear
                    </Button>
                  </div>
                </div>
              )}
            </div>

            {/* Applied Filters Pills - Desktop */}
            {isFiltersApplied && (
              <div className="hidden lg:block mb-6">
                <div className="flex flex-wrap gap-2 items-center">
                  <span className="text-sm font-medium text-gray-600 mr-2">Active filters:</span>

                  {selectedCategory && (
                    <div className="flex items-center gap-1 bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs">
                      <div
                        className="w-2 h-2 rounded-full mr-1"
                        style={{ backgroundColor: activeCategories?.find(c => c.id === selectedCategory)?.color }}
                      />
                      {activeCategories?.find(c => c.id === selectedCategory)?.name}
                      <button
                        onClick={() => handleCategoryChange(null)}
                        className="ml-1 hover:bg-blue-200 rounded-full p-0.5"
                      >
                        <X size={12} />
                      </button>
                    </div>
                  )}

                  {selectedVenue && (
                    <div className="flex items-center gap-1 bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs">
                      <MapPin size={12} className="mr-1" />
                      {allVenuesWithEvents.find(v => v.id === selectedVenue)?.name}
                      <button
                        onClick={() => handleVenueChange(null)}
                        className="ml-1 hover:bg-green-200 rounded-full p-0.5"
                      >
                        <X size={12} />
                      </button>
                    </div>
                  )}

                  {dateFilter !== 'all' && (
                    <div className="flex items-center gap-1 bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-xs">
                      <Calendar size={12} className="mr-1" />
                      {getDateFilterLabel(dateFilter)}
                      <button
                        onClick={() => handleDateChange('all')}
                        className="ml-1 hover:bg-purple-200 rounded-full p-0.5"
                      >
                        <X size={12} />
                      </button>
                    </div>
                  )}

                  {searchTerm && (
                    <div className="flex items-center gap-1 bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-xs">
                      Search: "{searchTerm}"
                      <button
                        onClick={() => handleSearch('')}
                        className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                      >
                        <X size={12} />
                      </button>
                    </div>
                  )}

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearFilters}
                    className="text-blue-600 hover:text-blue-700 text-xs h-6 px-2"
                  >
                    Clear all
                  </Button>
                </div>
              </div>
            )}

        {/* Events grid */}
        <div className="mt-8">
          {loading && initialLoad ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="bg-card rounded-xl overflow-hidden shadow-md animate-pulse">
                  <div className="h-48 bg-muted"></div>
                  <div className="p-4 space-y-3">
                    <div className="h-6 bg-muted rounded w-3/4"></div>
                    <div className="h-4 bg-muted rounded w-1/2"></div>
                    <div className="h-4 bg-muted rounded w-full"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : events.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {events.map(event => (
                  <EventCard
                    key={event.id}
                    id={event.id}
                    title={event.title}
                    date={new Date(event.start_date)}
                    location={`${event.venue?.name}`}
                    imageUrl={event.image_url}
                    category={{
                      name: event.category?.name || 'Uncategorized',
                      color: event.category?.color || '#888888',
                      text_color: event.category?.text_color || '#FFFFFF',
                      icon: event.category?.icon
                    }}
                    isFree={event.is_free}
                    price={event.general_admission_price}
                    isMultiDay={event.is_multi_day}
                    endDate={event.end_date ? new Date(event.end_date) : undefined}
                    registrations={event.registrations}
                    maxAttendees={event.max_attendees}
                  />
                ))}
              </div>

              {/* Loading indicator for infinite scroll */}
              <div ref={observerTarget} className="mt-8 flex justify-center">
                {loading && !initialLoad && (
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Loader2 className="h-5 w-5 animate-spin" />
                    <span>Loading more events...</span>
                  </div>
                )}
              </div>

              {/* End of results message */}
              {!hasMore && events.length > 0 && (
                <div className="mt-8 text-center text-muted-foreground">
                  <p>You've reached the end of the list.</p>
                  <p className="text-sm">Showing {events.length} of {totalEvents} events</p>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
                <Calendar className="h-12 w-12 text-muted-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-2">No events found</h3>
              <p className="text-muted-foreground mb-6">
                {isFiltersApplied
                  ? "Try adjusting your filters to see more events."
                  : "There are no events available at this time."}
              </p>
              {isFiltersApplied && (
                <Button onClick={clearFilters} variant="outline">
                  Clear all filters
                </Button>
              )}
            </div>
          )}
        </div>

        {/* Create Event CTA at the end of list */}
        <div className="mt-12 text-center p-6 bg-blue-50/50 rounded-lg border border-blue-100 shadow-sm">
          <h3 className="text-xl font-semibold mb-2">Want to host your own event?</h3>
          <p className="text-muted-foreground mb-4">Share your event with the community and reach more people</p>

          {user ? (
            <Button
              className="bg-blue-600 hover:bg-blue-700"
              size="lg"
              asChild
            >
              <Link to="/create">
                <PlusCircle className="mr-2" size={18} />
                Host an Event
              </Link>
            </Button>
          ) : (
            <div className="space-y-3">
              <Button
                variant="outline"
                className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white"
                size="lg"
                asChild
              >
                <Link to="/auth">
                  Sign in to Create Events
                </Link>
              </Button>
            </div>
          )}
          </div>
            </main>
          </div>
        </div>
      </LocationGate>

      <Footer />
    </div>
  );
};

export default EventsPage;
