import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { OrganizationMemberManagement } from '@/components/organizations/OrganizationMemberManagement';
import { useOrganizations } from '@/hooks/useOrganizations';
import { UserOrganization, OrganizationMember } from '@/types/organization';
import { Building2, ArrowLeft, Settings, Users, BarChart3, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export const OrganizationManagementPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { organizations, getOrganizationMembers } = useOrganizations();
  
  const [organization, setOrganization] = useState<UserOrganization | null>(null);
  const [members, setMembers] = useState<OrganizationMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [membersLoading, setMembersLoading] = useState(false);

  useEffect(() => {
    if (!id) {
      navigate('/organizations');
      return;
    }

    const org = organizations.find(o => o.id === id);
    if (!org) {
      toast({
        title: "Organization not found",
        description: "The organization you're looking for doesn't exist or you don't have access to it.",
        variant: "destructive",
      });
      navigate('/organizations');
      return;
    }

    // Check if user has management permissions
    if (!['owner', 'admin'].includes(org.user_role)) {
      toast({
        title: "Access denied",
        description: "You don't have permission to manage this organization.",
        variant: "destructive",
      });
      navigate('/organizations');
      return;
    }

    setOrganization(org);
    setLoading(false);
    loadMembers();
  }, [id, organizations, navigate, toast]);

  const loadMembers = async () => {
    if (!id) return;
    
    setMembersLoading(true);
    try {
      const orgMembers = await getOrganizationMembers(id);
      setMembers(orgMembers);
    } catch (error) {
      console.error('Error loading members:', error);
      toast({
        title: "Error loading members",
        description: "Failed to load organization members. Please try again.",
        variant: "destructive",
      });
    } finally {
      setMembersLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'owner':
        return 'bg-purple-100 text-purple-800';
      case 'admin':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (!organization) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => navigate('/organizations')}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Organizations
        </Button>
        
        <div className="flex items-center gap-4 mb-4">
          {organization.logo_url ? (
            <img
              src={organization.logo_url}
              alt={`${organization.name} logo`}
              className="w-16 h-16 rounded-lg object-cover"
            />
          ) : (
            <div className="w-16 h-16 bg-primary/10 rounded-lg flex items-center justify-center">
              <Building2 className="h-8 w-8 text-primary" />
            </div>
          )}
          <div>
            <h1 className="text-3xl font-bold">{organization.name}</h1>
            <div className="flex items-center gap-2 mt-2">
              <Badge className={getStatusColor(organization.approval_status)}>
                {organization.approval_status}
              </Badge>
              <Badge className={getRoleColor(organization.user_role)}>
                Your role: {organization.user_role}
              </Badge>
            </div>
          </div>
        </div>
        
        {organization.description && (
          <p className="text-gray-600 max-w-3xl">
            {organization.description}
          </p>
        )}
      </div>

      {/* Management Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">
            <Settings className="h-4 w-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="members">
            <Users className="h-4 w-4 mr-2" />
            Members
          </TabsTrigger>
          <TabsTrigger value="events">
            <BarChart3 className="h-4 w-4 mr-2" />
            Events
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Organization Details</CardTitle>
              <CardDescription>
                Basic information about your organization
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Name</label>
                  <p className="text-gray-900">{organization.name}</p>
                </div>
                
                {organization.contact_email && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Contact Email</label>
                    <p className="text-gray-900">{organization.contact_email}</p>
                  </div>
                )}
                
                {organization.contact_phone && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Contact Phone</label>
                    <p className="text-gray-900">{organization.contact_phone}</p>
                  </div>
                )}
                
                {organization.website_url && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Website</label>
                    <a
                      href={organization.website_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline"
                    >
                      {organization.website_url}
                    </a>
                  </div>
                )}
                
                {(organization.city || organization.state) && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Location</label>
                    <p className="text-gray-900">
                      {[organization.city, organization.state].filter(Boolean).join(', ')}
                    </p>
                  </div>
                )}
              </div>
              
              {organization.approval_status === 'pending' && (
                <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-800">
                    <strong>Pending Approval:</strong> Your organization is under review. 
                    You'll be notified once it's approved.
                  </p>
                </div>
              )}
              
              {organization.approval_status === 'rejected' && organization.approval_notes && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-800">
                    <strong>Rejected:</strong> {organization.approval_notes}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="members" className="space-y-6">
          {membersLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <OrganizationMemberManagement
              organizationId={organization.id}
              members={members}
              currentUserRole={organization.user_role}
              onMembersUpdate={loadMembers}
            />
          )}
        </TabsContent>

        <TabsContent value="events" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Organization Events</CardTitle>
              <CardDescription>
                Events hosted by this organization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-muted-foreground">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Event management coming soon</p>
                <p className="text-sm">View and manage events hosted by your organization</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
