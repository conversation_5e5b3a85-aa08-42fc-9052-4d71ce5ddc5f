import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { useAuth } from '@/context/AuthContext';

const AuthPage: React.FC = () => {
  const { user, loading, signInWithGoogle } = useAuth();
  const location = useLocation();

  // Extract redirect path from URL query parameters
  const getRedirectPath = () => {
    const searchParams = new URLSearchParams(location.search);
    return searchParams.get('redirect') || undefined;
  };

  // Handle Google sign-in with redirect
  const handleGoogleSignIn = () => {
    const redirectPath = getRedirectPath();
    signInWithGoogle(redirectPath);
  };

  // Redirect if user is already logged in
  if (user && !loading) {
    const redirectPath = getRedirectPath();
    if (redirectPath) {
      return <Navigate to={redirectPath} replace />;
    }
    return <Navigate to="/" replace />;
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-grow flex items-center justify-center p-4 pt-20 md:pt-24">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-raleway">Welcome to TheLocalAdda</CardTitle>
            <CardDescription>
              Connect with your Google account to discover and create events in your community
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col gap-4">
            <Button 
              onClick={handleGoogleSignIn}
              className="w-full py-6 flex items-center justify-center gap-2 bg-white hover:bg-gray-50 text-gray-800 border border-gray-300"
              variant="outline"
              disabled={loading}
            >
              <img src="/google-logo.svg" alt="Google" className="w-6 h-6" />
              Sign in with Google
            </Button>
          </CardContent>
          <CardFooter className="flex flex-col text-center text-sm text-muted-foreground">
            <p>By continuing, you agree to our Terms of Service and Privacy Policy.</p>
          </CardFooter>
        </Card>
      </main>
      
      <Footer />
    </div>
  );
};

export default AuthPage;
