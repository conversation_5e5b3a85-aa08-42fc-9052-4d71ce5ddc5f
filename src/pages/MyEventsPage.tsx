import React from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { CalendarIcon, MapPinIcon, IndianRupee, Clock, Users, Tag, ExternalLink } from 'lucide-react';
import { format } from 'date-fns';
import { useAuth } from '@/context/AuthContext';
import { useUserRegistrations } from '@/hooks/useUserRegistrations';
import { useUserHostedEvents } from '@/hooks/useUserHostedEvents';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import EventAttendeesSection from '@/components/events/EventAttendeesSection';
import { RegistrationManagement } from '@/components/events/RegistrationManagement';

const MyEventsPage: React.FC = () => {
  const { user, loading } = useAuth();
  const { registrations, loading: registrationsLoading } = useUserRegistrations();
  const { events: hostedEvents, loading: hostedEventsLoading } = useUserHostedEvents();
  const navigate = useNavigate();

  React.useEffect(() => {
    if (!loading && !user) {
      navigate('/auth');
    }
  }, [user, loading, navigate]);

  // Format date for display
  const formatEventDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy • h:mm a');
    } catch (e) {
      return dateString;
    }
  };

  // Format price for display
  const formatPrice = (price: number | null) => {
    if (price === null || price === 0) return 'Free';
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(price);
  };

  // Get payment status badge color
  const getPaymentStatusColor = (status: string | null) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500 text-white';
      case 'pending':
        return 'bg-yellow-500 text-black';
      case 'failed':
        return 'bg-red-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  // Get approval status badge color
  const getApprovalStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-500 text-white';
      case 'pending':
        return 'bg-yellow-500 text-black';
      case 'rejected':
        return 'bg-red-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-grow container mx-auto px-4 pt-24 pb-12 md:pt-28 md:pb-16">
          <LoadingSkeleton />
        </main>
        <Footer />
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-grow container mx-auto px-4 pt-24 pb-12 md:pt-28 md:pb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-5xl mx-auto"
        >
          <h1 className="text-3xl font-bold mb-6">My Events</h1>
          
          <Tabs defaultValue="registrations" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-8">
              <TabsTrigger value="registrations">My Registrations</TabsTrigger>
              <TabsTrigger value="hosted">My Hosted Events</TabsTrigger>
            </TabsList>
            
            {/* My Registrations Tab */}
            <TabsContent value="registrations">
              {registrationsLoading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <Skeleton key={i} className="h-40 w-full rounded-lg" />
                  ))}
                </div>
              ) : registrations.length === 0 ? (
                <EmptyState
                  title="No registrations yet"
                  description="You haven't registered for any events yet."
                  buttonText="Browse Events"
                  buttonAction={() => navigate('/events')}
                />
              ) : (
                <div className="space-y-6">
                  {registrations.map((registration) => (
                    <motion.div
                      key={registration.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                      whileHover={{ y: -5 }}
                      className="group"
                    >
                      <Card className="overflow-hidden border-blue-primary/10 transition-all duration-300 group-hover:shadow-md">
                        <div className="flex flex-col md:flex-row">
                          {/* Event Image */}
                          <div 
                            className="w-full md:w-1/4 h-40 md:h-auto bg-muted cursor-pointer"
                            onClick={() => navigate(`/events/${registration.event_id}`)}
                          >
                            {registration.event.image_url ? (
                              <img 
                                src={registration.event.image_url} 
                                alt={registration.event.title} 
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center bg-blue-primary/10">
                                <CalendarIcon className="h-12 w-12 text-blue-primary/50" />
                              </div>
                            )}
                          </div>
                          
                          {/* Event Details */}
                          <div className="p-6 flex-1">
                            <div className="flex flex-col md:flex-row md:items-start md:justify-between">
                              <div>
                                <h3 
                                  className="text-xl font-semibold mb-2 hover:text-blue-primary cursor-pointer"
                                  onClick={() => navigate(`/events/${registration.event_id}`)}
                                >
                                  {registration.event.title}
                                </h3>
                                
                                <div className="space-y-2 mb-4">
                                  <div className="flex items-center text-sm text-muted-foreground">
                                    <Clock size={16} className="mr-2" />
                                    {formatEventDate(registration.event.start_date)}
                                  </div>
                                  
                                  <div className="flex items-center text-sm text-muted-foreground">
                                    <MapPinIcon size={16} className="mr-2" />
                                    {registration.event.venue?.name || registration.event.venue_name}
                                  </div>
                                  
                                  {registration.event.category && (
                                    <div className="flex items-center text-sm">
                                      <Tag size={16} className="mr-2 text-muted-foreground" />
                                      <Badge 
                                        className="bg-blue-primary/10 text-blue-primary hover:bg-blue-primary/20"
                                        variant="secondary"
                                      >
                                        {registration.event.category.name}
                                      </Badge>
                                    </div>
                                  )}
                                </div>
                              </div>
                              
                              <div className="mt-4 md:mt-0 space-y-2">
                                <div className="flex items-center justify-between md:flex-col md:items-end">
                                  <div className="flex items-center">
                                    <Users size={16} className="mr-1 text-muted-foreground" />
                                    <span className="text-sm font-medium">
                                      {registration.quantity} {registration.quantity > 1 ? 'tickets' : 'ticket'}
                                    </span>
                                  </div>
                                  
                                  <div className="text-sm font-semibold">
                                    <span className="flex items-center">
                                      <IndianRupee size={14} className="mr-1" />
                                      {formatPrice(registration.total_amount)}
                                    </span>
                                  </div>
                                </div>
                                
                                <Badge 
                                  className={getPaymentStatusColor(registration.payment_status)}
                                  variant="outline"
                                >
                                  {registration.payment_status || 'Pending'}
                                </Badge>
                              </div>
                            </div>
                            
                            <div className="mt-4 pt-4 border-t flex justify-end">
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => navigate(`/events/${registration.event_id}`)}
                              >
                                View Event <ExternalLink size={14} className="ml-1" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              )}
            </TabsContent>
            
            {/* My Hosted Events Tab */}
            <TabsContent value="hosted">
              {hostedEventsLoading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <Skeleton key={i} className="h-40 w-full rounded-lg" />
                  ))}
                </div>
              ) : hostedEvents.length === 0 ? (
                <EmptyState
                  title="No hosted events yet"
                  description="You haven't created any events yet."
                  buttonText="Create Event"
                  buttonAction={() => navigate('/create')}
                />
              ) : (
                <div className="space-y-6">
                  {hostedEvents.map((event) => (
                    <motion.div
                      key={event.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                      whileHover={{ y: -5 }}
                      className="group"
                    >
                      <Card className="overflow-hidden border-blue-primary/10 transition-all duration-300 group-hover:shadow-md">
                        <div className="flex flex-col md:flex-row">
                          {/* Event Image */}
                          <div 
                            className="w-full md:w-1/4 h-40 md:h-auto bg-muted cursor-pointer"
                            onClick={() => navigate(`/events/${event.id}`)}
                          >
                            {event.image_url ? (
                              <img 
                                src={event.image_url} 
                                alt={event.title} 
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center bg-blue-primary/10">
                                <CalendarIcon className="h-12 w-12 text-blue-primary/50" />
                              </div>
                            )}
                          </div>
                          
                          {/* Event Details */}
                          <div className="p-6 flex-1">
                            <div className="flex flex-col md:flex-row md:items-start md:justify-between">
                              <div>
                                <h3 
                                  className="text-xl font-semibold mb-2 hover:text-blue-primary cursor-pointer"
                                  onClick={() => navigate(`/events/${event.id}`)}
                                >
                                  {event.title}
                                </h3>
                                
                                <div className="space-y-2 mb-4">
                                  <div className="flex items-center text-sm text-muted-foreground">
                                    <Clock size={16} className="mr-2" />
                                    {formatEventDate(event.start_date)}
                                  </div>
                                  
                                  <div className="flex items-center text-sm text-muted-foreground">
                                    <MapPinIcon size={16} className="mr-2" />
                                    {event.venue?.name || event.venue_name}
                                  </div>
                                  
                                  {event.category && (
                                    <div className="flex items-center text-sm">
                                      <Tag size={16} className="mr-2 text-muted-foreground" />
                                      <Badge 
                                        className="bg-blue-primary/10 text-blue-primary hover:bg-blue-primary/20"
                                        variant="secondary"
                                      >
                                        {event.category.name}
                                      </Badge>
                                    </div>
                                  )}
                                </div>
                              </div>
                              
                              <div className="mt-4 md:mt-0 space-y-2">
                                <div className="text-sm font-semibold">
                                  <span className="flex items-center">
                                    <IndianRupee size={14} className="mr-1" />
                                    {formatPrice(event.general_admission_price)}
                                  </span>
                                </div>
                                
                                <Badge 
                                  className={getApprovalStatusColor(event.approval_status)}
                                  variant="outline"
                                >
                                  {event.approval_status.charAt(0).toUpperCase() + event.approval_status.slice(1)}
                                </Badge>
                              </div>
                            </div>
                            
                            {/* Event Attendees Section */}
                            <EventAttendeesSection eventId={event.id} />

                            {/* Registration Management Section for Paid Events */}
                            {!event.is_free && (
                              <div className="mt-6">
                                <RegistrationManagement
                                  eventId={event.id}
                                  eventTitle={event.title}
                                  isEventFree={event.is_free}
                                />
                              </div>
                            )}
                            
                            <div className="mt-4 pt-4 border-t flex justify-end space-x-2">
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => navigate(`/events/${event.id}`)}
                              >
                                View Event <ExternalLink size={14} className="ml-1" />
                              </Button>
                              
                              <Button 
                                variant="default" 
                                size="sm"
                                onClick={() => navigate(`/edit-event/${event.id}`)}
                              >
                                Edit Event
                              </Button>
                            </div>
                          </div>
                        </div>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </motion.div>
      </main>
      
      <Footer />
    </div>
  );
};

interface EmptyStateProps {
  title: string;
  description: string;
  buttonText: string;
  buttonAction: () => void;
}

const EmptyState: React.FC<EmptyStateProps> = ({ title, description, buttonText, buttonAction }) => (
  <Card className="w-full">
    <CardContent className="flex flex-col items-center justify-center py-12">
      <CalendarIcon className="h-16 w-16 text-muted-foreground mb-4" />
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground mb-6 text-center">{description}</p>
      <Button onClick={buttonAction}>{buttonText}</Button>
    </CardContent>
  </Card>
);

const LoadingSkeleton = () => (
  <div className="max-w-5xl mx-auto p-4 pt-24 pb-12 md:pt-28 md:pb-16">
    <Skeleton className="h-10 w-48 mb-8" />
    
    <Skeleton className="h-12 w-full mb-8 rounded-lg" />
    
    <div className="space-y-4">
      {[1, 2, 3].map((i) => (
        <Skeleton key={i} className="h-40 w-full rounded-lg" />
      ))}
    </div>
  </div>
);

export default MyEventsPage;
