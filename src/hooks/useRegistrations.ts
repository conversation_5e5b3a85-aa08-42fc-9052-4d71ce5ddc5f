import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from './use-toast';
import { useAuth } from '@/context/AuthContext';
import { 
  EnhancedRegistration, 
  RegistrationManagement, 
  RegistrationStatus, 
  PaymentStatus,
  RegistrationReview 
} from '@/types/payment';

export const useRegistrations = () => {
  const [registrations, setRegistrations] = useState<EnhancedRegistration[]>([]);
  const [managementRegistrations, setManagementRegistrations] = useState<RegistrationManagement[]>([]);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  // Fetch user's registrations
  const fetchUserRegistrations = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('registrations')
        .select('*')
        .eq('user_id', user.id)
        .order('registration_date', { ascending: false });

      if (error) throw error;
      setRegistrations(data || []);
    } catch (error) {
      console.error('Error fetching registrations:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch registrations',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [user, toast]);

  // Fetch registrations for events organized by user (for management)
  const fetchManagementRegistrations = useCallback(async (eventId?: string) => {
    if (!user) return;

    setLoading(true);
    try {
      // Select all direct columns from the view + explicitly joined and aliased profiles
      const selectQuery = `
        id,
        event_id,
        user_id,
        registration_date,
        ticket_type,
        quantity,
        unit_price,
        total_amount,
        payment_status,
        payment_method,
        registration_status,
        payment_proof_url,
        payment_transaction_ref,
        payment_proof_uploaded_at,
        reviewed_by,
        reviewed_at,
        review_notes,
        approved_at,
        event_title,
        event_is_free,
        event_organizer_id,
        event_organization_id,
        user:profiles!registrations_user_id_fkey(full_name, email, phone_number),
        reviewer:profiles!registrations_reviewed_by_fkey(full_name)
      `;

      let query = supabase
        .from('registration_management')
        .select(selectQuery)
        .order('registration_date', { ascending: false });

      if (eventId) {
        query = query.eq('event_id', eventId);
      }

      const { data, error } = await query;

      if (error) throw error;
      setManagementRegistrations(data || []);
    } catch (error) {
      console.error('Error fetching management registrations:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch event registrations',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [user, toast]);

  // Register for an event
  const registerForEvent = useCallback(async (
    eventId: string,
    ticketType: string = 'general',
    quantity: number = 1,
    unitPrice?: number,
    paymentMethod?: string
  ) => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please sign in to register for events',
        variant: 'destructive',
      });
      return null;
    }

    try {
      const totalAmount = unitPrice ? unitPrice * quantity : 0;

      const { data, error } = await supabase
        .from('registrations')
        .insert({
          event_id: eventId,
          user_id: user.id,
          ticket_type: ticketType,
          quantity,
          unit_price: unitPrice,
          total_amount: totalAmount,
          payment_method: paymentMethod,
          payment_status: unitPrice ? 'pending' : 'completed',
          registration_status: 'pending' // Will be auto-approved for free events by trigger
        })
        .select()
        .single();

      if (error) throw error;

      toast({
        title: 'Registration Successful',
        description: unitPrice 
          ? 'Please complete payment and upload proof to confirm your registration'
          : 'You have successfully registered for this event',
      });

      // Refresh registrations
      await fetchUserRegistrations();
      return data;
    } catch (error) {
      console.error('Error registering for event:', error);
      toast({
        title: 'Registration Failed',
        description: 'Failed to register for event. Please try again.',
        variant: 'destructive',
      });
      return null;
    }
  }, [user, toast, fetchUserRegistrations]);

  // Upload payment proof
  const uploadPaymentProof = useCallback(async (
    registrationId: string,
    proofFile: File,
    transactionRef?: string
  ) => {
    if (!user) return false;

    try {
      // Upload file to Supabase Storage
      const fileExt = proofFile.name.split('.').pop();
      const fileName = `payment-proof-${registrationId}-${Date.now()}.${fileExt}`;
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('payment-proofs')
        .upload(fileName, proofFile);

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('payment-proofs')
        .getPublicUrl(fileName);

      // Update registration with proof details
      const { error: updateError } = await supabase
        .from('registrations')
        .update({
          payment_proof_url: publicUrl,
          payment_transaction_ref: transactionRef,
          payment_proof_uploaded_at: new Date().toISOString(),
        })
        .eq('id', registrationId)
        .eq('user_id', user.id); // Ensure user can only update their own registrations

      if (updateError) throw updateError;

      toast({
        title: 'Payment Proof Uploaded',
        description: 'Your payment proof has been uploaded successfully. The organizer will review it shortly.',
      });

      // Refresh registrations
      await fetchUserRegistrations();
      return true;
    } catch (error) {
      console.error('Error uploading payment proof:', error);
      toast({
        title: 'Upload Failed',
        description: 'Failed to upload payment proof. Please try again.',
        variant: 'destructive',
      });
      return false;
    }
  }, [user, toast, fetchUserRegistrations]);

  // Review registration (for organizers)
  const reviewRegistration = useCallback(async (
    registrationId: string,
    review: RegistrationReview
  ) => {
    if (!user) return false;

    try {
      const updateData: any = {
        registration_status: review.status,
        reviewed_by: user.id,
        reviewed_at: new Date().toISOString(),
        review_notes: review.notes,
      };

      // Set payment status based on registration status
      if (review.status === 'approved') {
        updateData.payment_status = 'completed';
      } else if (review.status === 'declined') {
        updateData.payment_status = 'failed';
      }

      const { error } = await supabase
        .from('registrations')
        .update(updateData)
        .eq('id', registrationId);

      if (error) throw error;

      toast({
        title: 'Registration Reviewed',
        description: `Registration has been ${review.status}`,
      });

      // Refresh management registrations
      await fetchManagementRegistrations();
      return true;
    } catch (error) {
      console.error('Error reviewing registration:', error);
      toast({
        title: 'Review Failed',
        description: 'Failed to review registration. Please try again.',
        variant: 'destructive',
      });
      return false;
    }
  }, [user, toast, fetchManagementRegistrations]);

  // Cancel registration
  const cancelRegistration = useCallback(async (registrationId: string) => {
    if (!user) return false;

    try {
      const { error } = await supabase
        .from('registrations')
        .update({
          registration_status: 'cancelled',
          reviewed_at: new Date().toISOString(),
        })
        .eq('id', registrationId)
        .eq('user_id', user.id);

      if (error) throw error;

      toast({
        title: 'Registration Cancelled',
        description: 'Your registration has been cancelled',
      });

      // Refresh registrations
      await fetchUserRegistrations();
      return true;
    } catch (error) {
      console.error('Error cancelling registration:', error);
      toast({
        title: 'Cancellation Failed',
        description: 'Failed to cancel registration. Please try again.',
        variant: 'destructive',
      });
      return false;
    }
  }, [user, toast, fetchUserRegistrations]);

  // Check if user is registered for an event
  const isRegisteredForEvent = useCallback((eventId: string) => {
    return registrations.some(reg => 
      reg.event_id === eventId && 
      reg.registration_status !== 'cancelled'
    );
  }, [registrations]);

  // Get registration for specific event
  const getRegistrationForEvent = useCallback((eventId: string) => {
    return registrations.find(reg => 
      reg.event_id === eventId && 
      reg.registration_status !== 'cancelled'
    );
  }, [registrations]);

  useEffect(() => {
    if (user) {
      fetchUserRegistrations();
    }
  }, [user, fetchUserRegistrations]);

  return {
    registrations,
    managementRegistrations,
    loading,
    registerForEvent,
    uploadPaymentProof,
    reviewRegistration,
    cancelRegistration,
    fetchUserRegistrations,
    fetchManagementRegistrations,
    isRegisteredForEvent,
    getRegistrationForEvent,
  };
};
