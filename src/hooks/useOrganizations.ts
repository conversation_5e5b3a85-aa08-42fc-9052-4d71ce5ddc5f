import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { Organization, OrganizationMember, UserOrganization, OrganizationRole, NonMember } from '@/types/organization';
import { toast } from 'sonner';

export const useOrganizations = () => {
  const { user } = useAuth();
  const [organizations, setOrganizations] = useState<UserOrganization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUserOrganizations = async () => {
    if (!user) {
      setOrganizations([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Fetch organizations where user is a member
      const { data: memberData, error: memberError } = await supabase
        .from('organization_members')
        .select(`
          id,
          role,
          status,
          organization:organizations (
            id,
            name,
            description,
            logo_url,
            website_url,
            contact_email,
            contact_phone,
            address,
            city,
            state,
            zip_code,
            locality,
            place_id,
            approval_status,
            approval_notes,
            created_by,
            created_at,
            updated_at
          )
        `)
        .eq('user_id', user.id);

      if (memberError) throw memberError;

      const userOrgs: UserOrganization[] = memberData?.map(member => {
        // Ensure organizations data is not null and is an object, not an array
        // Supabase typically returns the joined table as an object if it's a to-one relationship (FK)
        // or an array if it could be to-many. Here, organization_members.organization_id -> organizations.id is to-one.
        const organizationData = member.organization;
        if (!organizationData || Array.isArray(organizationData)) {
          console.warn('Skipping member record due to missing or malformed organization data:', member);
          return null;
        }
        return {
          ...organizationData, // Spread the organization details
          user_role: member.role as OrganizationRole, // Cast role to OrganizationRole
          membership_id: member.id, // This is the id of the organization_members record
          membership_status: member.status as OrganizationMember['status'], // Cast status
        };
      }).filter(Boolean) as UserOrganization[]; // Filter out any nulls from malformed data

      setOrganizations(userOrgs);
    } catch (err) {
      console.error('Error fetching organizations:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch organizations');
    } finally {
      setLoading(false);
    }
  };

  const declineInvitation = async (membershipId: string) => {
    if (!user) {
      toast.error('Authentication required.');
      throw new Error('User not authenticated');
    }
    if (!membershipId) {
      toast.error('Membership ID required to decline invitation.');
      throw new Error('Membership ID required');
    }

    setLoading(true);
    try {
      const { error } = await supabase
        .from('organization_members')
        .update({
          status: 'declined',
        })
        .eq('id', membershipId)
        .eq('user_id', user.id); // Security: Ensure user can only decline their own invitations

      if (error) {
        console.error('Error declining invitation:', error);
        toast.error(`Failed to decline invitation: ${error.message}`);
        throw error;
      }

      toast.success('Invitation declined.');
      await fetchUserOrganizations(); // Refetch to update the list
    } catch (err) {
      if (!(err instanceof Error && err.message.includes('Failed to decline invitation'))) {
        console.error('Generic error declining invitation:', err);
        toast.error('An unexpected error occurred while declining the invitation.');
      }
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const acceptInvitation = async (membershipId: string) => {
    if (!user) {
      toast.error('Authentication required.');
      throw new Error('User not authenticated');
    }
    if (!membershipId) {
      toast.error('Membership ID required to accept invitation.');
      throw new Error('Membership ID required');
    }

    setLoading(true);
    try {
      const { error } = await supabase
        .from('organization_members')
        .update({
          status: 'active',
          joined_at: new Date().toISOString(),
        })
        .eq('id', membershipId)
        .eq('user_id', user.id); // Security: Ensure user can only accept their own invitations

      if (error) {
        console.error('Error accepting invitation:', error);
        toast.error(`Failed to accept invitation: ${error.message}`);
        throw error;
      }

      toast.success('Invitation accepted! You are now an active member.');
      await fetchUserOrganizations(); // Refetch to update the list
    } catch (err) {
      // Errors already toasted in the try block or if a generic error occurs
      if (!(err instanceof Error && err.message.includes('Failed to accept invitation'))) {
         console.error('Generic error accepting invitation:', err);
         toast.error('An unexpected error occurred while accepting the invitation.');
      }
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const createOrganization = async (organizationData: Omit<Organization, 'id' | 'created_by' | 'created_at' | 'updated_at' | 'approval_status'>) => {
    if (!user) {
      throw new Error('User must be authenticated to create an organization');
    }

    try {
      const { data, error } = await supabase
        .from('organizations')
        .insert({
          ...organizationData,
          created_by: user.id,
        })
        .select()
        .single();

      if (error) throw error;

      const newOrganization = data;

      // The database trigger is now responsible for adding the creator as an active owner.
      // Client-side insertion for the owner is removed.

      // The success message can be simplified as the trigger handles owner addition.
      // The `approval_status` is 'pending' by default from schema, so review message is still relevant.
      toast.success('Organization submitted successfully! It will be reviewed by our team.');

      // fetchUserOrganizations will be called. If the trigger worked,
      // the user should now be a member of this new organization.
      await fetchUserOrganizations();
      return newOrganization;
    } catch (err) {
      console.error('Error creating organization:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to create organization';
      toast.error(errorMessage);
      throw err;
    }
  };

  const updateOrganization = async (id: string, updates: Partial<Organization>) => {
    try {
      const { data, error } = await supabase
        .from('organizations')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      toast.success('Organization updated successfully');
      await fetchUserOrganizations(); // Refresh the list
      return data;
    } catch (err) {
      console.error('Error updating organization:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to update organization';
      toast.error(errorMessage);
      throw err;
    }
  };

  const addMember = async (organizationId: string, newMemberEmail: string, role: OrganizationRole = 'member') => {
    if (!user) {
      toast.error('You must be logged in to add members.');
      throw new Error('User not authenticated');
    }
    if (!newMemberEmail || !newMemberEmail.includes('@')) {
      toast.error('Invalid email address provided.');
      throw new Error('Invalid email address');
    }

    setLoading(true);
    try {
      const emailLower = newMemberEmail.trim().toLowerCase();

      // 1. Check if there's already a non-member invitation for this email
      const { data: existingNonMember, error: nonMemberCheckError } = await supabase
        .from('non_members')
        .select('id, status')
        .eq('organization_id', organizationId)
        .eq('email', emailLower)
        .maybeSingle();

      if (nonMemberCheckError) {
        console.error('Error checking existing non-member invitation:', nonMemberCheckError);
        toast.error('Could not verify existing invitations.');
        throw nonMemberCheckError;
      }

      if (existingNonMember) {
        if (existingNonMember.status === 'pending_invitation') {
          toast.info(`An invitation has already been sent to ${newMemberEmail}.`);
        } else {
          toast.info(`There is already an invitation for ${newMemberEmail} with status: ${existingNonMember.status}.`);
        }
        throw new Error('User already has a pending invitation.');
      }

      // 2. Find the user by email to get their UUID
      const { data: memberProfile, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('email', emailLower)
        .single();

      if (profileError && profileError.code !== 'PGRST116') { // PGRST116: "single row not found"
        console.error('Error fetching profile by email:', profileError);
        toast.error('Error checking user existence.');
        throw profileError;
      }

      // 3. If user exists, check if they're already a member
      if (memberProfile) {
        const memberUserId = memberProfile.id;

        const { data: existingMembership, error: checkError } = await supabase
          .from('organization_members')
          .select('id, status')
          .eq('organization_id', organizationId)
          .eq('user_id', memberUserId)
          .maybeSingle();

        if (checkError) {
          console.error('Error checking existing membership:', checkError);
          toast.error('Could not verify existing membership.');
          throw checkError;
        }

        if (existingMembership) {
          if (existingMembership.status === 'pending_invitation') {
            toast.info(`User ${newMemberEmail} already has a pending invitation for this organization.`);
          } else {
            toast.info(`User ${newMemberEmail} is already a member of this organization (status: ${existingMembership.status}).`);
          }
          throw new Error('User is already a member or has a pending invitation.');
        }

        // 4a. User exists - create organization_members entry
        const { data: newMemberData, error: insertError } = await supabase
          .from('organization_members')
          .insert({
            organization_id: organizationId,
            user_id: memberUserId,
            role,
            status: 'pending_invitation',
            invited_by: user.id,
            invited_at: new Date().toISOString(),
            joined_at: null,
          })
          .select()
          .single();

        if (insertError) {
          console.error('Error inserting organization member:', insertError);
          toast.error(`Failed to send invitation: ${insertError.message}`);
          throw insertError;
        }

        toast.success(`Invitation sent to ${newMemberEmail}.`);
        return newMemberData;
      } else {
        // 4b. User doesn't exist - create non_members entry
        const { data: newNonMemberData, error: nonMemberInsertError } = await supabase
          .from('non_members')
          .insert({
            organization_id: organizationId,
            email: emailLower,
            role,
            status: 'pending_invitation',
            invited_by: user.id,
            invited_at: new Date().toISOString(),
          })
          .select()
          .single();

        if (nonMemberInsertError) {
          console.error('Error inserting non-member invitation:', nonMemberInsertError);
          toast.error(`Failed to send invitation: ${nonMemberInsertError.message}`);
          throw nonMemberInsertError;
        }

        toast.success(`Invitation sent to ${newMemberEmail}. They will be added to the organization when they sign up.`);
        return newNonMemberData;
      }

    } catch (err) {
      // Errors from profile lookup, existing member check, or insert are caught here
      // Specific toasts should have been shown already.
      // This catch is for any other unexpected errors or if specific catches re-throw.
      console.error('General error in addMember:', err);
      // Avoid double-toasting if a specific toast was already shown
      if (err instanceof Error && !['User not found', 'User is already a member', 'Error checking user existence', 'Could not verify existing membership', 'Failed to send invitation'].some(knownMsg => err.message.includes(knownMsg))) {
        toast.error('An unexpected error occurred while adding the member.');
      }
      throw err; // Rethrow so the component can also handle if needed (e.g., stop loading spinner)
    } finally {
      setLoading(false);
    }
  };

  const updateMemberRole = async (memberId: string, role: OrganizationRole) => {
    try {
      const { error } = await supabase
        .from('organization_members')
        .update({ role })
        .eq('id', memberId);
      // Removed .select().single() to avoid PGRST116 error if RLS prevents SELECT after UPDATE

      if (error) {
        // Log the specific Supabase error for better debugging
        console.error('Supabase error updating member role:', error);
        throw error;
      }

      toast.success('Member role updated successfully. Data will refresh.');
      // Data is not returned here as .select() was removed.
      // The calling component should rely on onMembersUpdate() to refresh the view.
      return null;
    } catch (err) {
      // Log the error if it's not already a Supabase error object with details
      if (!(err as any).details && !(err as any).message?.includes('PGRST')) {
        console.error('Generic error updating member role:', err);
      }
      // Use a more specific error message from Supabase if available, otherwise generic.
      const supabaseErrorMessage = (err as any).message || 'Failed to update member role';
      const displayMessage = supabaseErrorMessage.includes('JWSError') || supabaseErrorMessage.includes('AuthApiError') ? 'Authentication error, please try again.' : supabaseErrorMessage;

      toast.error(displayMessage);
      throw err; // Rethrow to be handled by the calling component's try/catch if needed
    }
  };

  const removeMember = async (memberId: string) => {
    try {
      const { error } = await supabase
        .from('organization_members')
        .delete()
        .eq('id', memberId);

      if (error) throw error;

      toast.success('Member removed successfully');
    } catch (err) {
      console.error('Error removing member:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove member';
      toast.error(errorMessage);
      throw err;
    }
  };

  const getApprovedOrganizations = async () => {
    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .eq('approval_status', 'approved')
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (err) {
      console.error('Error fetching approved organizations:', err);
      throw err;
    }
  };

  const getOrganizationMembers = async (organizationId: string): Promise<OrganizationMember[]> => {
    if (!organizationId) {
      console.error('Organization ID is required to fetch members.');
      return [];
    }

    try {
      const { data, error } = await supabase
        .from('organization_members')
        .select(`
          id,
          role,
          joined_at,
          status,
          user:profiles!organization_members_user_id_fkey (
            id,
            full_name,
            avatar_url,
            email
          )
        `)
        .eq('organization_id', organizationId);

      if (error) {
        console.error('Error fetching organization members:', error);
        throw error; // Rethrow to be caught by the calling component
      }

      // The explicit join hint should ensure 'user' is an object, but defensive check remains.
      return (data || []).map(member => ({
        ...member,
        user: Array.isArray(member.user) ? member.user[0] : member.user
      })) as OrganizationMember[];
    } catch (err) {
      // Log the error but rethrow it so the component can handle it (e.g., show a toast)
      console.error('Detailed error in getOrganizationMembers:', err);
      throw err;
    }
  };

  // Function to get non-member invitations for an organization
  const getNonMembers = async (organizationId: string): Promise<NonMember[]> => {
    if (!organizationId) {
      console.error('Organization ID is required to fetch non-members.');
      return [];
    }

    try {
      const { data, error } = await supabase
        .from('non_members')
        .select(`
          id,
          email,
          role,
          status,
          invited_at,
          inviter:profiles!non_members_invited_by_fkey (
            id,
            full_name,
            avatar_url,
            email
          )
        `)
        .eq('organization_id', organizationId);

      if (error) {
        console.error('Error fetching non-members:', error);
        throw error;
      }

      return (data || []).map(nonMember => ({
        ...nonMember,
        organization_id: organizationId,
        created_at: nonMember.invited_at, // Use invited_at as created_at for consistency
        updated_at: nonMember.invited_at, // Use invited_at as updated_at for consistency
        invited_by: nonMember.inviter?.id || '',
        inviter: Array.isArray(nonMember.inviter) ? nonMember.inviter[0] : nonMember.inviter
      })) as NonMember[];
    } catch (err) {
      console.error('Detailed error in getNonMembers:', err);
      throw err;
    }
  };

  // Function to cancel a non-member invitation
  const cancelNonMemberInvitation = async (nonMemberId: string) => {
    if (!user) {
      toast.error('Authentication required.');
      throw new Error('User not authenticated');
    }

    setLoading(true);
    try {
      const { error } = await supabase
        .from('non_members')
        .update({ status: 'cancelled' })
        .eq('id', nonMemberId);

      if (error) {
        console.error('Error cancelling non-member invitation:', error);
        toast.error(`Failed to cancel invitation: ${error.message}`);
        throw error;
      }

      toast.success('Invitation cancelled successfully.');
    } catch (err) {
      console.error('Error in cancelNonMemberInvitation:', err);
      if (!(err instanceof Error && err.message.includes('Failed to cancel invitation'))) {
        toast.error('An unexpected error occurred while cancelling the invitation.');
      }
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Function to resend a non-member invitation (update invited_at timestamp)
  const resendNonMemberInvitation = async (nonMemberId: string) => {
    if (!user) {
      toast.error('Authentication required.');
      throw new Error('User not authenticated');
    }

    setLoading(true);
    try {
      const { error } = await supabase
        .from('non_members')
        .update({
          status: 'pending_invitation',
          invited_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', nonMemberId);

      if (error) {
        console.error('Error resending non-member invitation:', error);
        toast.error(`Failed to resend invitation: ${error.message}`);
        throw error;
      }

      toast.success('Invitation resent successfully.');
    } catch (err) {
      console.error('Error in resendNonMemberInvitation:', err);
      if (!(err instanceof Error && err.message.includes('Failed to resend invitation'))) {
        toast.error('An unexpected error occurred while resending the invitation.');
      }
      throw err;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserOrganizations();
  }, [user]);

  return {
    organizations,
    loading,
    error,
    createOrganization,
    updateOrganization,
    addMember,
    updateMemberRole,
    removeMember,
    getApprovedOrganizations,
    getOrganizationMembers,
    getNonMembers,
    cancelNonMemberInvitation,
    resendNonMemberInvitation,
    acceptInvitation,
    declineInvitation,
    refetch: fetchUserOrganizations,
  };
};
