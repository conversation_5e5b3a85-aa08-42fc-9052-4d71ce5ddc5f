import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { Organization, OrganizationMember, UserOrganization } from '@/types/organization';
import { toast } from 'sonner';

export const useOrganizations = () => {
  const { user } = useAuth();
  const [organizations, setOrganizations] = useState<UserOrganization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUserOrganizations = async () => {
    if (!user) {
      setOrganizations([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Fetch organizations where user is a member
      const { data: memberData, error: memberError } = await supabase
        .from('organization_members')
        .select(`
          role,
          organizations (
            id,
            name,
            description,
            logo_url,
            website_url,
            contact_email,
            contact_phone,
            address,
            city,
            state,
            zip_code,
            locality,
            place_id,
            approval_status,
            approval_notes,
            created_by,
            created_at,
            updated_at
          )
        `)
        .eq('user_id', user.id);

      if (memberError) throw memberError;

      const userOrgs: UserOrganization[] = memberData?.map(member => ({
        ...member.organizations,
        user_role: member.role
      })) || [];

      setOrganizations(userOrgs);
    } catch (err) {
      console.error('Error fetching organizations:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch organizations');
    } finally {
      setLoading(false);
    }
  };

  const createOrganization = async (organizationData: Omit<Organization, 'id' | 'created_by' | 'created_at' | 'updated_at' | 'approval_status'>) => {
    if (!user) {
      throw new Error('User must be authenticated to create an organization');
    }

    try {
      const { data, error } = await supabase
        .from('organizations')
        .insert({
          ...organizationData,
          created_by: user.id,
        })
        .select()
        .single();

      if (error) throw error;

      toast.success('Organization created successfully! It will be reviewed by our team.');
      await fetchUserOrganizations(); // Refresh the list
      return data;
    } catch (err) {
      console.error('Error creating organization:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to create organization';
      toast.error(errorMessage);
      throw err;
    }
  };

  const updateOrganization = async (id: string, updates: Partial<Organization>) => {
    try {
      const { data, error } = await supabase
        .from('organizations')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      toast.success('Organization updated successfully');
      await fetchUserOrganizations(); // Refresh the list
      return data;
    } catch (err) {
      console.error('Error updating organization:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to update organization';
      toast.error(errorMessage);
      throw err;
    }
  };

  const addMember = async (organizationId: string, userId: string, role: string = 'member') => {
    try {
      const { data, error } = await supabase
        .from('organization_members')
        .insert({
          organization_id: organizationId,
          user_id: userId,
          role,
          invited_by: user?.id,
          invited_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;

      toast.success('Member added successfully');
      return data;
    } catch (err) {
      console.error('Error adding member:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to add member';
      toast.error(errorMessage);
      throw err;
    }
  };

  const updateMemberRole = async (memberId: string, role: string) => {
    try {
      const { data, error } = await supabase
        .from('organization_members')
        .update({ role })
        .eq('id', memberId)
        .select()
        .single();

      if (error) throw error;

      toast.success('Member role updated successfully');
      return data;
    } catch (err) {
      console.error('Error updating member role:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to update member role';
      toast.error(errorMessage);
      throw err;
    }
  };

  const removeMember = async (memberId: string) => {
    try {
      const { error } = await supabase
        .from('organization_members')
        .delete()
        .eq('id', memberId);

      if (error) throw error;

      toast.success('Member removed successfully');
    } catch (err) {
      console.error('Error removing member:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove member';
      toast.error(errorMessage);
      throw err;
    }
  };

  const getApprovedOrganizations = async () => {
    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .eq('approval_status', 'approved')
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (err) {
      console.error('Error fetching approved organizations:', err);
      throw err;
    }
  };

  useEffect(() => {
    fetchUserOrganizations();
  }, [user]);

  return {
    organizations,
    loading,
    error,
    createOrganization,
    updateOrganization,
    addMember,
    updateMemberRole,
    removeMember,
    getApprovedOrganizations,
    refetch: fetchUserOrganizations,
  };
};
