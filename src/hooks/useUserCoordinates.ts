import { useRef, useEffect } from 'react';
import { useUserLocation } from './useUserLocation';

// A simple hook that extracts just the coordinates from useUserLocation
export const useUserCoordinates = () => {
  const userLocation = useUserLocation();
  // Use a ref to store the coordinates
  // This avoids re-renders when the coordinates don't actually change
  const coordinatesRef = useRef<{ latitude: number; longitude: number } | null>(null);

  // Update the ref when userLocation changes
  useEffect(() => {
    // Only log once per userLocation change
    console.log('useUserCoordinates - checking for coordinate updates');

    // Check if we have valid coordinates from the user's location
    if (
      userLocation &&
      userLocation.coordinates &&
      typeof userLocation.coordinates === 'object' &&
      'lat' in userLocation.coordinates &&
      'lng' in userLocation.coordinates &&
      typeof userLocation.coordinates.lat === 'number' &&
      typeof userLocation.coordinates.lng === 'number'
    ) {
      // Check if coordinates have actually changed
      if (
        !coordinatesRef.current ||
        coordinatesRef.current.latitude !== userLocation.coordinates.lat ||
        coordinatesRef.current.longitude !== userLocation.coordinates.lng
      ) {
        console.log('useUserCoordinates - updating to user coordinates:', userLocation.coordinates);
        coordinatesRef.current = {
          latitude: userLocation.coordinates.lat,
          longitude: userLocation.coordinates.lng
        };
      }
    } else {
      // If no valid coordinates, set to null
      if (coordinatesRef.current !== null) {
        console.log('useUserCoordinates - clearing coordinates');
        coordinatesRef.current = null;
      }
    }
  }, [userLocation]);

  return coordinatesRef.current;
};
