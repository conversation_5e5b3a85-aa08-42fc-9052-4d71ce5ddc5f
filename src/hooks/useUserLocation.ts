import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useGeolocation, GeolocationCoordinates } from '@/hooks/useGeolocation';
import {
  geocodeByLatLng,
  geocodeByComponent,
  geocodeByAddress,
  extractLocationDetails,
  saveLocationToStorage,
  clearStoredLocation,
  getStoredLocation,
  isStoredLocationValid
} from '@/utils/geocoding';
import { UserProfile } from '@/context/AuthContext';

// Removed default coordinates - we now require explicit location selection

interface UserLocationState {
  loading: boolean;
  error: string | Error | GeolocationPositionError | null;
  locality: string | null;
  localityShortName: string | null;
  coordinates: { lat: number; lng: number } | null;
  permissionDenied: boolean;
  source: 'initial' | 'storage' | 'live' | 'manual' | 'profile' | null;
  pincodeLocalityOptions: string[] | null;
  pendingPincode: string | null;
  isLoadingOptions: boolean;
  isConfirmingSelection: boolean;
}

export const useUserLocation = () => {
  const { user, userProfile, updateUserProfile } = useAuth();
  const {
    coordinates: geoCoords,
    error: geoError,
    loading: geoLoading,
    fetchCoordinates
  } = useGeolocation();

  const [locationState, setLocationState] = useState<UserLocationState>({
    loading: true,
    error: null,
    locality: null,
    localityShortName: null,
    coordinates: null,
    permissionDenied: false,
    source: 'initial',
    pincodeLocalityOptions: null,
    pendingPincode: null,
    isLoadingOptions: false,
    isConfirmingSelection: false,
  });

  const updateProfileLocation = useCallback(async (coords: { lat: number; lng: number }, localityShortName: string | null) => {
    if (!user || !updateUserProfile) return;
    const updateData: Partial<UserProfile> = {
      location: coords,
      location_updated_at: new Date().toISOString(),
      locality_short_name: localityShortName,
    };
    try {
      console.log('Updating profile with location:', updateData);
      await updateUserProfile(updateData);
      console.log('Profile location updated successfully.');
    } catch (error) {
      console.error('Error updating profile location:', error);
      setLocationState(prev => ({ ...prev, error: `DB Update Failed: ${error instanceof Error ? error.message : String(error)}` }));
    }
  }, [user, updateUserProfile]);

  const processNewCoordinates = useCallback(async (fullCoords: GeolocationCoordinates) => {
    console.log('Processing new coordinates:', fullCoords);

    const { latitude, longitude } = fullCoords;
    const currentCoords = { lat: latitude, lng: longitude };

    // Clear any previous geocoding errors before attempting a new one
    setLocationState(prev => ({ ...prev, error: null, loading: true }));

    try {
      const results: google.maps.GeocoderResult[] = await geocodeByLatLng(latitude, longitude);
      console.log('Reverse geocode results:', results);

      if (results && results.length > 0) {
        const locationDetails = extractLocationDetails(results[0]); // Use the helper
        const { locality, sublocality_level_1 } = locationDetails;
        const newLocalityShort = sublocality_level_1 || locality;

        // Only update state if the location actually changed
        if (
          locality !== locationState.locality ||
          newLocalityShort !== locationState.localityShortName ||
          currentCoords.lat !== locationState.coordinates?.lat ||
          currentCoords.lng !== locationState.coordinates?.lng
        ) {
          setLocationState(prev => ({
            ...prev,
            loading: false,
            error: null, // Clear previous errors on success
            locality: locality,
            localityShortName: newLocalityShort,
            coordinates: currentCoords,
            permissionDenied: false,
          }));
          console.log('Saving new location:', { currentCoords, locality, newLocalityShort });
          saveLocationToStorage(currentCoords, locality, sublocality_level_1);
          if (user) {
            // Only update profile IF the new location is different from the profile
            if (
              !userProfile ||
              !userProfile.location ||
              userProfile.location.lat !== currentCoords.lat ||
              userProfile.location.lng !== currentCoords.lng ||
              userProfile.locality_short_name !== newLocalityShort
            ) {
               console.log("Updating profile with new coordinates/locality from live location.");
               await updateProfileLocation(currentCoords, newLocalityShort); // Await the profile update
            } else {
               console.log("Live location matches profile, skipping profile update.");
            }
          }
        } else {
           console.log('Processed coordinates, but location data is unchanged. Skipping state update.');
        }
      } else {
         console.error('Geocoding failed or returned no results.');
         setLocationState(prev => ({
           ...prev,
           loading: false,
           error: new Error('Could not determine location from coordinates.')
         }));
      }
    } catch (error) { // Catch errors during reverse geocoding or profile update
      setLocationState(prev => ({
        ...prev,
        error: error instanceof Error ? error : new Error(String(error)),
      }));
      console.error("Error processing coordinates or updating profile:", error);
    }
  }, [user, userProfile, updateProfileLocation, locationState.locality, locationState.localityShortName, locationState.coordinates]);

  // Effect 1: Initialize state from storage ONCE on mount
  useEffect(() => {
    console.log('useUserLocation: Initializing from storage (mount only)...');
    const storedLocation = getStoredLocation();
    let initialState: Partial<UserLocationState> = {}; // Start with partial

    if (storedLocation) {
      console.log('Using stored location for initial state:', storedLocation);
      initialState = {
        locality: storedLocation.locality,
        localityShortName: storedLocation.sublocality_level_1,
        coordinates: storedLocation.coords,
        source: 'storage', // Set source to storage when loading from it
        permissionDenied: false,
      };

      // Set initial state with stored location
      setLocationState(prev => ({
          ...prev, // Keep existing fields if any
          ...initialState,
          loading: false, // Mark initial load complete
          error: null,
          permissionDenied: false,
      }));
    } else {
      console.log('No stored location found, user will need to set location.');
      // Set initial state with no coordinates
      setLocationState(prev => ({
          ...prev,
          loading: false, // Mark initial load complete
          error: null,
          coordinates: null, // Explicitly set coordinates to null
          locality: null,
          localityShortName: null,
          source: 'initial',
          permissionDenied: false,
      }));
    }
  }, []); // <-- Empty dependency array: runs only once on mount

  // Effect 2: Update state based on userProfile changes (after initial load)
  useEffect(() => {
    // Only run if userProfile exists and is potentially newer than storage/default
    if (!userProfile) return;

    console.log('useUserLocation: Checking userProfile for updates...', userProfile);

    let profileCoords: { lat: number; lng: number } | null = null;
    let profileLocalityShortName: string | null = userProfile.locality_short_name || null;

    if (userProfile.location &&
        typeof userProfile.location === 'object' &&
        'lat' in userProfile.location &&
        'lng' in userProfile.location) {
       profileCoords = userProfile.location as { lat: number; lng: number };
    } else if (userProfile.location && typeof userProfile.location === 'string') {
        console.warn('Profile location is WKB string, ignoring coordinates part.');
    }

    // Selectively update state only if profile provides data
    setLocationState(prev => {
        // Avoid unnecessary updates if profile data is same as current
        if (prev.source === 'profile' &&
            prev.localityShortName === profileLocalityShortName &&
            JSON.stringify(prev.coordinates) === JSON.stringify(profileCoords)) {
            return prev; // No change needed
        }

        let needsUpdate = false;
        const newState = { ...prev };

        if (profileCoords) {
            newState.coordinates = profileCoords;
            newState.source = 'profile';
            needsUpdate = true;
            console.log('Updating coordinates from profile.');
        }

        if (profileLocalityShortName && profileLocalityShortName !== prev.localityShortName) {
             newState.localityShortName = profileLocalityShortName;
             // Update source only if coords also came from profile or are null
             if (profileCoords || !newState.coordinates) {
                newState.source = 'profile';
             }
             needsUpdate = true;
             console.log('Updating localityShortName from profile.');
        }

        // If we updated from profile, reset locality (full name) as profile doesn't store it
        if (needsUpdate && newState.source === 'profile') {
            newState.locality = null;
        }

        return needsUpdate ? newState : prev;
    });

  }, [userProfile]); // React only to userProfile changes

  const haveCoordsChanged = (newCoords: GeolocationCoordinates, oldCoords: { lat: number; lng: number } | null): boolean => {
    if (!oldCoords) return true; // Always process if no previous coords
    const latDiff = Math.abs(newCoords.latitude - oldCoords.lat);
    const lngDiff = Math.abs(newCoords.longitude - oldCoords.lng);
    // Use a small threshold (e.g., 0.0001 degrees, roughly 11 meters)
    const threshold = 0.0001;
    return latDiff > threshold || lngDiff > threshold;
  };

  useEffect(() => {
    if (geoError) {
      setLocationState(prev => ({
          ...prev,
          loading: false,
          error: geoError,
          permissionDenied: ('code' in geoError && geoError.code === geoError.PERMISSION_DENIED),
      }));
      console.error('Geolocation Error:', geoError.message);
      if ('code' in geoError && geoError.code === geoError.PERMISSION_DENIED) { clearStoredLocation(); }
    } else if (geoCoords && geoCoords.latitude != null && geoCoords.longitude != null) {
      const currentGeoCoords = { latitude: geoCoords.latitude, longitude: geoCoords.longitude, accuracy: geoCoords.accuracy, altitude: geoCoords.altitude, altitudeAccuracy: geoCoords.altitudeAccuracy, heading: geoCoords.heading, speed: geoCoords.speed };
      if (haveCoordsChanged(currentGeoCoords, locationState.coordinates) && locationState.source === 'live') {
        console.log('Received new geo-coordinates while source is live, processing...', geoCoords);
        processNewCoordinates(geoCoords);
      } else {
        // Handle other cases (coords not new, source not live, already processing)
        if (geoCoords && locationState.source !== 'live') {
             console.log(`Ignoring new geo-coordinates because source is ${locationState.source}.`);
        } else if (geoCoords && !haveCoordsChanged(geoCoords, locationState.coordinates)){
             console.log('Ignoring new geo-coordinates because they haven\'t changed significantly.');
        }
        // Ensure loading is false if geo-processing is done
        if (!geoLoading && locationState.loading) {
            setLocationState(prev => ({ ...prev, loading: false }));
        }
      }
    } else if (!geoLoading && locationState.loading) {
        // Handle case where loading finished but no coords/error
         setLocationState(prev => ({ ...prev, loading: false }));
    }
  // Depend on primitive coords, loading/error states, and internal state flags
  }, [
    geoCoords?.latitude, // Depend on primitive value
    geoCoords?.longitude, // Depend on primitive value
    geoError,
    geoLoading,
    locationState.coordinates,
    locationState.loading,
    locationState.source,
    processNewCoordinates
  ]);

  const requestLocation = useCallback(() => {
    console.log('Requesting location update...');
    setLocationState(prev => ({
      ...prev,
      loading: true,
      error: null,
      source: 'live', // Explicitly set source to live on request
      permissionDenied: false, // Reset permission denial on new request
      // Clear pincode state when requesting current location
      pendingPincode: null,
      pincodeLocalityOptions: null,
      isLoadingOptions: false,
      isConfirmingSelection: false,
    }));
    fetchCoordinates();
  }, [fetchCoordinates]);

  const setLocationFromPincode = useCallback(async (pincode: string): Promise<{success: boolean, error?: string, requiresConfirmation?: boolean}> => {
    // Reset state for a new pincode lookup
    setLocationState(prev => ({
      ...prev,
      loading: true,
      error: null,
      pendingPincode: null,
      pincodeLocalityOptions: null,
      isLoadingOptions: true, // Start option loading
      isConfirmingSelection: false,
    }));

    try {
      const results = await geocodeByComponent({ postalCode: pincode, country: 'IN' });
      console.log("Geocode by component results:", results);

      if (results && results.length > 0) {
        const firstResult = results[0];
        const postcodeLocalities = (firstResult as any)?.postcode_localities as string[] | undefined;

        if (postcodeLocalities && postcodeLocalities.length > 1) {
          console.log("Multiple localities found for pincode:", postcodeLocalities);
          setLocationState(prev => ({
            ...prev,
            loading: false,
            isLoadingOptions: false,
            pincodeLocalityOptions: postcodeLocalities,
            pendingPincode: pincode,
          }));
          return { success: true, requiresConfirmation: true };
        } else if (results && results.length > 0) {
          const locationDetails = extractLocationDetails(firstResult);
          if (!locationDetails.coordinates) {
            throw new Error('Could not extract coordinates from pincode geocoding result.');
          }
          const { coordinates, locality, sublocality_level_1 } = locationDetails;

          setLocationState(prev => ({
            ...prev,
            loading: false,
            error: null,
            locality: locality,
            localityShortName: sublocality_level_1 || locality,
            coordinates: coordinates,
            permissionDenied: false,
            source: 'manual',
            pincodeLocalityOptions: null,
            pendingPincode: null,
            isLoadingOptions: false,
            isConfirmingSelection: false,
          }));

          console.log('Saving location from pincode:', { coordinates, locality, sublocality_level_1 });
          saveLocationToStorage(coordinates, locality, sublocality_level_1);
          if (user) {
            updateProfileLocation(coordinates, sublocality_level_1 || locality);
          }

          return { success: true };
        } else {
          throw new Error('Geocoding did not return any results.');
        }
      } else {
        throw new Error('No results found for this pincode.');
      }
    } catch (err: any) {
      console.error("Error setting location from pincode:", err);
      const errorMessage = err instanceof Error ? err.message : String(err);
      setLocationState(prev => ({ ...prev, loading: false, error: err instanceof Error ? err : new Error(errorMessage) }));
      // Reset pincode specific loading states on error too
      setLocationState(prev => ({ ...prev, isLoadingOptions: false, isConfirmingSelection: false }));
      return { success: false, error: errorMessage };
    } finally {
      // Ensure loading flags are reset, primarily handled in try/catch returns now
      setLocationState(prev => ({ ...prev, loading: false, isLoadingOptions: false }));
    }
  }, [user, updateProfileLocation]);

  const confirmPincodeSelection = useCallback(async (selectedLocality: string): Promise<boolean> => {
    if (!locationState.pendingPincode || !selectedLocality) {
      const msg = "Missing pincode or selected locality for confirmation.";
      setLocationState(prev => ({ ...prev, error: new Error(msg) }));
      return false;
    }

    setLocationState(prev => ({
      ...prev,
      isConfirmingSelection: true,
      error: null
    }));

    const addressToGeocode = `${selectedLocality}, ${locationState.pendingPincode}, India`;

    try {
      const results = await geocodeByAddress(addressToGeocode);
      console.log("Confirmation geocode results:", results);

      if (results && results.length > 0) {
        const firstResult = results[0];
        const locationDetails = extractLocationDetails(firstResult);
        if (!locationDetails.coordinates) {
          throw new Error('Could not extract coordinates from the selected locality address.');
        }
        const { coordinates, locality, sublocality_level_1 } = locationDetails;

        setLocationState(prev => ({
          ...prev,
          loading: false,
          error: null,
          locality: locality,
          localityShortName: sublocality_level_1 || locality,
          coordinates: coordinates,
          permissionDenied: false,
          source: 'manual',
          pincodeLocalityOptions: null,
          pendingPincode: null,
          isLoadingOptions: false,
          isConfirmingSelection: false,
        }));

        console.log('Saving location from pincode:', { coordinates, locality, sublocality_level_1 });
        saveLocationToStorage(coordinates, locality, sublocality_level_1);
        if (user) {
          updateProfileLocation(coordinates, sublocality_level_1 || locality);
        }
        return true; // Signal success
      } else {
        throw new Error('No results found for the selected locality and pincode combination.');
      }
    } catch (err: any) {
      console.error("Error confirming locality selection:", err);
      const errorMessage = err instanceof Error ? err.message : String(err);
      setLocationState(prev => ({
        ...prev,
        error: err instanceof Error ? err : new Error(errorMessage),
        isConfirmingSelection: false, // Ensure loading stops
        pincodeLocalityOptions: null,
        pendingPincode: null,
      }));
      return false; // Signal failure
    } finally {
      // Ensure loading flags are reset
      setLocationState(prev => ({ ...prev, loading: false, isConfirmingSelection: false }));
    }

  }, [locationState.pendingPincode, updateProfileLocation]);

  const clearPincodeOptions = useCallback(() => {
    // Clear pincode state and reset error
    setLocationState(prev => ({
      ...prev,
      pendingPincode: null,
      pincodeLocalityOptions: null,
      isLoadingOptions: false,
      isConfirmingSelection: false,
      error: null, // Clear errors when manually clearing pincode state
    }));
  }, []);

  // Only log in development and not on every render
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('useUserLocation state updated:', {
        hasCoordinates: !!locationState.coordinates,
        source: locationState.source,
        loading: locationState.loading
      });
    }
  }, [locationState.coordinates, locationState.source, locationState.loading]);

  return {
    ...locationState,
    requestLocation,
    setLocationFromPincode,
    confirmPincodeSelection,
    clearPincodeOptions
  };
};
