import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from './use-toast';
import { EventVenue } from '@/types/venue';
import { useDebounce } from './useDebounce';
import { useUserCoordinates } from './useUserCoordinates';
import { useUserSearchRadius } from './useUserSearchRadius';

export function useVenues() {
  const [venues, setVenues] = useState<EventVenue[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const userCoordinates = useUserCoordinates();
  const { searchRadius } = useUserSearchRadius();

  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const [searchResults, setSearchResults] = useState<EventVenue[]>([]);
  const [searching, setSearching] = useState(false);

  // Fetch nearby venues when user coordinates change
  useEffect(() => {
    const fetchNearbyVenues = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check if coordinates are available
        if (!userCoordinates) {
          console.log('No coordinates available, fetching venues by priority only');
          // If no coordinates, just fetch venues by priority without distance filtering
          const { data: fallbackData, error: fallbackError } = await supabase
            .from('event_venues')
            .select('*')
            .order('priority', { ascending: false })
            .limit(10);

          if (fallbackError) throw fallbackError;
          setVenues(fallbackData || []);
          return;
        }

        // Use the stored procedure to find venues within user's preferred radius
        // Limit to 10 results for the venue list
        const { data, error } = await supabase
          .rpc('find_venues_within_radius', {
            search_lat: userCoordinates.latitude,
            search_lng: userCoordinates.longitude,
            radius_km: searchRadius,
            result_limit: 10
          });

        if (error) {
          console.error('Error with stored procedure, falling back to regular query:', error);
          // Fallback to regular query if stored procedure fails
          const { data: fallbackData, error: fallbackError } = await supabase
            .from('event_venues')
            .select('*')
            .order('priority', { ascending: false })
            .limit(10);

          if (fallbackError) throw fallbackError;

          setVenues(fallbackData || []);
        } else {
          // Process venues with distance information
          setVenues(data || []);
        }
      } catch (err) {
        console.error('Error fetching venues:', err);
        setError('Failed to load venues');
      } finally {
        setLoading(false);
      }
    };

    fetchNearbyVenues();
  }, [userCoordinates, searchRadius]);

  // Function to search venues by name within radius
  useEffect(() => {
    const searchVenues = async () => {
      if (!debouncedSearchTerm) {
        setSearchResults([]);
        return;
      }

      try {
        setSearching(true);

        // Check if coordinates are available
        if (!userCoordinates) {
          console.log('No coordinates available, searching venues by name only');
          // If no coordinates, just search by name without distance filtering
          const { data, error } = await supabase
            .from('event_venues')
            .select('*')
            .ilike('name', `%${debouncedSearchTerm}%`)
            .order('priority', { ascending: false })
            .limit(10);

          if (error) throw error;
          setSearchResults(data || []);
          return;
        }

        // First get venues within user's preferred radius
        // Limit to 10 results for search
        const { data: nearbyVenues, error: rpcError } = await supabase
          .rpc('find_venues_within_radius', {
            search_lat: userCoordinates.latitude,
            search_lng: userCoordinates.longitude,
            radius_km: searchRadius,
            result_limit: 10
          });

        if (rpcError) {
          console.error('Error with stored procedure during search, falling back:', rpcError);
          // Fallback to regular search if stored procedure fails
          const { data, error } = await supabase
            .from('event_venues')
            .select('*')
            .ilike('name', `%${debouncedSearchTerm}%`)
            .order('priority', { ascending: false })
            .limit(10);

          if (error) throw error;
          setSearchResults(data || []);
        } else {
          // Filter the nearby venues by name
          const filteredResults = (nearbyVenues || []).filter(venue =>
            venue.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
          );

          // No need to sort manually - the stored procedure already sorts by priority DESC, then distance ASC
          setSearchResults(filteredResults);
          console.log(`Found ${filteredResults.length} venues matching "${debouncedSearchTerm}" within ${searchRadius}km`);
        }
      } catch (err) {
        console.error('Error searching venues:', err);
      } finally {
        setSearching(false);
      }
    };

    searchVenues();
  }, [debouncedSearchTerm, userCoordinates, searchRadius]);

  // Function to get a venue by ID
  const getVenueById = useCallback(async (id: string) => {
    try {
      const { data, error } = await supabase
        .from('event_venues')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;

      return data;
    } catch (err) {
      console.error('Error fetching venue:', err);
      return null;
    }
  }, []);

  // Function to create a new venue
  const createVenue = async (venue: Omit<EventVenue, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      // Check if venue already exists
      const { data: existingVenue, error: searchError } = await supabase
        .from('event_venues')
        .select('*')
        .eq('name', venue.name)
        .maybeSingle();

      if (searchError) throw searchError;

      // If venue already exists, return it
      if (existingVenue) {
        return existingVenue;
      }

      // Otherwise create a new venue with lowest priority (0)
      const venueToCreate: any = {
        ...venue,
        priority: 0 // Default lowest priority for user-created venues
      };

      // Convert location object to PostGIS POINT if available
      if (venue.location && venue.location.latitude && venue.location.longitude) {
        venueToCreate.location = `POINT(${venue.location.longitude} ${venue.location.latitude})`;
      }

      const { data, error } = await supabase
        .from('event_venues')
        .insert(venueToCreate)
        .select()
        .single();

      if (error) throw error;

      // Update local state after creating a new venue
      setVenues(prev => [...prev, data]);

      return data;
    } catch (err) {
      console.error('Error creating venue:', err);
      toast({
        title: 'Error',
        description: 'Failed to create venue. Please try again.',
        variant: 'destructive',
      });
      return null;
    }
  };

  // Function to update a venue (admin only)
  const updateVenue = async (id: string, updates: Partial<EventVenue>) => {
    try {
      const { data, error } = await supabase
        .from('event_venues')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      // Update local state after updating a venue
      setVenues(prev => prev.map(v => v.id === id ? data : v));

      toast({
        title: 'Venue updated',
        description: 'Venue has been updated successfully.',
      });

      return data;
    } catch (err) {
      console.error('Error updating venue:', err);
      toast({
        title: 'Error',
        description: 'Failed to update venue. Please try again.',
        variant: 'destructive',
      });
      return null;
    }
  };

  return {
    venues,
    loading,
    error,
    searchTerm,
    setSearchTerm,
    searchResults,
    searching,
    getVenueById,
    createVenue,
    updateVenue
  };
}
