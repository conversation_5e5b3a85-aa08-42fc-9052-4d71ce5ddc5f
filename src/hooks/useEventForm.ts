import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { EventFormValues, eventFormSchema } from "@/types/event-form";
import { supabase } from "@/integrations/supabase/client";
import { useNavigate } from "react-router-dom";
import { useToast } from "./use-toast";
import { useAuth } from "@/context/AuthContext";
import { EventVenue } from "@/types/venue";
import { useVenues } from "./useVenues";

// Form section field mappings for error detection
export const FORM_SECTION_FIELDS = {
  'item-1': ['title', 'description', 'categoryId', 'tags'], // Basic Info
  'item-2': ['startDate', 'startTime', 'isMultiDay', 'endDate', 'endTime', 'maxAttendees', 'registrationDeadline'], // Event Details
  'item-3': ['venueId'], // Location - only venueId is required, other fields are conditional
  'item-4': ['organizerName', 'organizerEmail', 'organizerPhone', 'websiteUrl'], // Organizer
  'item-5': ['isFree', 'generalAdmission', 'vipTicket', 'hasEarlyBird', 'earlyBirdDeadline', 'earlyBirdDiscount', 'groupDiscount', 'minGroupSize', 'groupDiscountPercentage', 'paymentMethods', 'refundPolicy'], // Ticketing
  'item-6': ['organizerTermsEnabled', 'organizerTerms'], // Organizer's Terms
  'item-7': ['termsAgreed', 'privacyPolicyAgreed'], // Platform Terms
} as const;

export const SECTION_NAMES = {
  'item-1': 'Basic Event Information',
  'item-2': 'Event Details',
  'item-3': 'Location & Venue',
  'item-4': 'Organizer Details',
  'item-5': 'Ticketing & Pricing',
  'item-6': "Organizer's Terms and Conditions",
  'item-7': 'Platform Terms & Agreements',
} as const;

export const useEventForm = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  const { createVenue: createVenueFromHook } = useVenues();

  const [coverImage, setCoverImage] = useState<File | null>(null);
  const [coverImageUrl, setCoverImageUrl] = useState<string>("");
  const [additionalImages, setAdditionalImages] = useState<File[]>([]);
  const [additionalImageUrls, setAdditionalImageUrls] = useState<string[]>([]);

  const [formLoading, setFormLoading] = useState(false);

  const defaultValues: Partial<EventFormValues> = {
    isFree: true,
    isMultiDay: false,
    hasEarlyBird: false,
    groupDiscount: false,
    paymentMethods: ["upi"],
    organizerName: user?.user_metadata?.name || user?.user_metadata?.full_name || "Your Name",
    organizerEmail: user?.email || "",
    organizerTermsEnabled: true,
    organizerTerms: "",
  };

  const form = useForm<EventFormValues>({
    resolver: zodResolver(eventFormSchema),
    defaultValues,
  });

  const isFree = form.watch("isFree");
  const isMultiDay = form.watch("isMultiDay");
  const hasEarlyBird = form.watch("hasEarlyBird");

  // Error detection utilities
  const getSectionErrors = (sectionId: keyof typeof FORM_SECTION_FIELDS) => {
    const fields = FORM_SECTION_FIELDS[sectionId];
    const errors = form.formState.errors;
    const sectionErrors: string[] = [];

    fields.forEach(field => {
      if (errors[field as keyof typeof errors]) {
        sectionErrors.push(field);
      }
    });

    return sectionErrors;
  };

  const getSectionErrorCount = (sectionId: keyof typeof FORM_SECTION_FIELDS) => {
    const formErrors = getSectionErrors(sectionId).length;

    // Special handling for item-6 (Organizer's Terms) - include logical validation
    if (sectionId === 'item-6') {
      const formValues = form.getValues();
      const hasLogicalError = formValues.organizerTermsEnabled === true && !formValues.organizerTerms?.trim();
      return formErrors + (hasLogicalError ? 1 : 0);
    }

    return formErrors;
  };

  const isSectionValid = (sectionId: keyof typeof FORM_SECTION_FIELDS) => {
    const formValues = form.getValues();

    switch (sectionId) {
      case 'item-1': // Basic Info
        return !!(formValues.title?.trim() &&
                 formValues.description?.trim() &&
                 formValues.categoryId);

      case 'item-2': // Event Details
        return !!(formValues.startDate && formValues.startTime);

      case 'item-3': // Location
        return !!(formValues.venueId);

      case 'item-4': // Organizer
        return !!(formValues.organizerName?.trim());

      case 'item-5': // Ticketing
        // If free event, it's valid. If paid, need general admission price
        return formValues.isFree === true ||
               (formValues.isFree === false && formValues.generalAdmission && parseFloat(formValues.generalAdmission) > 0);

      case 'item-6': // Organizer's Terms
        // If organizer terms are disabled, section is valid
        // If enabled, need organizer terms text
        return formValues.organizerTermsEnabled === false ||
               (formValues.organizerTermsEnabled === true && formValues.organizerTerms?.trim());

      case 'item-7': // Platform Terms
        return !!(formValues.termsAgreed && formValues.privacyPolicyAgreed);

      default:
        return false;
    }
  };

  const isSectionTouched = (sectionId: keyof typeof FORM_SECTION_FIELDS) => {
    const formValues = form.getValues();
    const touchedFields = form.formState.touchedFields;

    // For progress indicators, we consider a section "touched" if:
    // 1. Any field has been explicitly touched, OR
    // 2. Any required field has a value (for auto-populated fields)

    switch (sectionId) {
      case 'item-1': // Basic Info
        return !!(touchedFields.title || touchedFields.description || touchedFields.categoryId ||
                 formValues.title || formValues.description || formValues.categoryId);

      case 'item-2': // Event Details
        return !!(touchedFields.startDate || touchedFields.startTime ||
                 formValues.startDate || formValues.startTime);

      case 'item-3': // Location
        return !!(touchedFields.venueId || formValues.venueId);

      case 'item-4': // Organizer
        return !!(touchedFields.organizerName || formValues.organizerName);

      case 'item-5': // Ticketing
        return !!(touchedFields.isFree || touchedFields.generalAdmission ||
                 formValues.isFree !== undefined || formValues.generalAdmission);

      case 'item-6': // Organizer's Terms
        return !!(touchedFields.organizerTermsEnabled || touchedFields.organizerTerms ||
                 formValues.organizerTermsEnabled !== undefined || formValues.organizerTerms);

      case 'item-7': // Platform Terms
        return !!(touchedFields.termsAgreed || touchedFields.privacyPolicyAgreed ||
                 formValues.termsAgreed || formValues.privacyPolicyAgreed);

      default:
        const fields = FORM_SECTION_FIELDS[sectionId];
        return fields.some(field => touchedFields[field as keyof typeof touchedFields]);
    }
  };

  const getSectionCompletionStatus = (sectionId: keyof typeof FORM_SECTION_FIELDS) => {
    const hasErrors = getSectionErrorCount(sectionId) > 0;
    const isTouched = isSectionTouched(sectionId);
    const isValid = isSectionValid(sectionId);

    // Special handling for item-6 (Organizer's Terms) - check both form errors AND logical validation
    if (sectionId === 'item-6') {
      const formValues = form.getValues();
      const hasLogicalError = formValues.organizerTermsEnabled === true && !formValues.organizerTerms?.trim();

      // If there are form errors OR logical validation fails, it's in error state
      if (hasErrors || hasLogicalError) return 'error';
    } else {
      // For other sections, if there are validation errors, it's in error state
      if (hasErrors) return 'error';
    }

    // If section is valid (no errors) and has been touched, it's complete
    if (isValid && isTouched) return 'complete';

    // If section has been touched but still has errors or incomplete required fields
    if (isTouched) return 'partial';

    // If section hasn't been touched at all
    return 'untouched';
  };

  const getFirstErrorSection = () => {
    const sectionIds = Object.keys(FORM_SECTION_FIELDS) as Array<keyof typeof FORM_SECTION_FIELDS>;

    for (const sectionId of sectionIds) {
      if (getSectionErrorCount(sectionId) > 0) {
        return sectionId;
      }
    }

    return null;
  };

  const getAllErrorSummary = () => {
    const sectionIds = Object.keys(FORM_SECTION_FIELDS) as Array<keyof typeof FORM_SECTION_FIELDS>;
    const errorSummary: Array<{
      sectionId: keyof typeof FORM_SECTION_FIELDS;
      sectionName: string;
      errors: string[];
      errorCount: number;
    }> = [];

    sectionIds.forEach(sectionId => {
      const errors = getSectionErrors(sectionId);
      let hasLogicalError = false;

      // Special handling for item-6 (Organizer's Terms) - check logical validation
      if (sectionId === 'item-6') {
        const formValues = form.getValues();
        hasLogicalError = formValues.organizerTermsEnabled === true && !formValues.organizerTerms?.trim();
      }

      // Include section if it has form errors OR logical validation errors
      if (errors.length > 0 || hasLogicalError) {
        const totalErrors = errors.length + (hasLogicalError ? 1 : 0);
        const allErrors = [...errors];
        if (hasLogicalError) {
          allErrors.push('organizerTerms'); // Add logical error
        }

        errorSummary.push({
          sectionId,
          sectionName: SECTION_NAMES[sectionId],
          errors: allErrors,
          errorCount: totalErrors,
        });
      }
    });

    return errorSummary;
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setCoverImage(file);
      setCoverImageUrl(URL.createObjectURL(file));
    }
  };

  const handleImageClick = () => {
    document.getElementById('eventImage')?.click();
  };

  const handleAdditionalImagesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);

      const totalImages = additionalImages.length + newFiles.length;
      const filesToAdd = newFiles.slice(0, Math.max(0, 5 - additionalImages.length));

      if (totalImages > 5) {
        toast({
          title: "Too many images",
          description: "You can upload a maximum of 5 additional images.",
          variant: "destructive",
        });
      }

      setAdditionalImages(prev => [...prev, ...filesToAdd]);

      const newUrls = filesToAdd.map(file => URL.createObjectURL(file));
      setAdditionalImageUrls(prev => [...prev, ...newUrls]);
    }
  };

  const handleAdditionalImagesClick = () => {
    document.getElementById('additionalImages')?.click();
  };

  const removeAdditionalImage = (index: number) => {
    setAdditionalImages(prev => prev.filter((_, i) => i !== index));
    setAdditionalImageUrls(prev => prev.filter((_, i) => i !== index));
  };

  const uploadImage = async (file: File) => {
    if (!file) return null;

    const fileExt = file.name.split('.').pop();
    const fileName = `${Math.random().toString(36).substring(2)}.${fileExt}`;
    const filePath = `${fileName}`;

    try {
      const { data, error } = await supabase.storage
        .from('event-images')
        .upload(filePath, file);

      if (error) throw error;

      const { data: urlData } = supabase.storage
        .from('event-images')
        .getPublicUrl(filePath);

      return urlData.publicUrl;
    } catch (error) {
      console.error('Error uploading image:', error);
      return null;
    }
  };

  const uploadAllImages = async () => {
    const uploads = [];
    let mainImageUrl = null;

    if (coverImage) {
      mainImageUrl = await uploadImage(coverImage);
    }

    if (additionalImages.length > 0) {
      for (const image of additionalImages) {
        const url = await uploadImage(image);
        if (url) uploads.push(url);
      }
    }

    return {
      mainImageUrl,
      additionalImageUrls: uploads
    };
  };

  const createVenue = async (venueData: Omit<EventVenue, 'id' | 'created_at' | 'updated_at'>) => {
    return createVenueFromHook(venueData);
  };

  const onSubmit = async (data: EventFormValues) => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "You must be signed in to create an event.",
        variant: "destructive",
      });
      navigate('/auth');
      return;
    }

    // Check for validation errors and show helpful message
    const errorSummary = getAllErrorSummary();
    if (errorSummary.length > 0) {
      const totalErrors = errorSummary.reduce((sum, section) => sum + section.errorCount, 0);
      toast({
        title: "Please fix validation errors",
        description: `Found ${totalErrors} error${totalErrors > 1 ? 's' : ''} in ${errorSummary.length} section${errorSummary.length > 1 ? 's' : ''}. Check the form above for details.`,
        variant: "destructive",
      });
      return;
    }

    try {
      setFormLoading(true);
      const { mainImageUrl, additionalImageUrls } = await uploadAllImages();

      // Ensure we have a venue_id
      let venueId = data.venueId;

      if (!venueId) {
        // If no venue_id is provided, create a new venue
        const venue = await createVenue({
          name: data.venueName,
          address: data.address,
          city: data.city,
          state: data.state,
          zip_code: data.zipCode || null,
          priority: 0
        });

        if (venue) {
          venueId = venue.id;
        } else {
          throw new Error("Failed to create venue");
        }
      }

      const startHour = parseInt(data.startTime.split(':')[0]);
      const startMinute = parseInt(data.startTime.split(':')[1]);

      const startDate = new Date(data.startDate);
      startDate.setHours(startHour, startMinute);

      let endDate = null;
      if (data.isMultiDay && data.endDate) {
        const endHour = data.endTime ? parseInt(data.endTime.split(':')[0]) : 23;
        const endMinute = data.endTime ? parseInt(data.endTime.split(':')[1]) : 59;

        endDate = new Date(data.endDate);
        endDate.setHours(endHour, endMinute);
      } else if (data.endTime) {
        const endHour = parseInt(data.endTime.split(':')[0]);
        const endMinute = parseInt(data.endTime.split(':')[1]);

        endDate = new Date(data.startDate);
        endDate.setHours(endHour, endMinute);
      }

      const eventData = {
        title: data.title,
        description: data.description,
        category_id: data.categoryId,
        tags: data.tags ? data.tags.split(',').map(tag => tag.trim()) : [],
        image_url: mainImageUrl,
        additional_images: additionalImageUrls,
        start_date: startDate.toISOString(),
        end_date: endDate ? endDate.toISOString() : null,
        is_multi_day: data.isMultiDay,
        max_attendees: data.maxAttendees ? parseInt(data.maxAttendees) : null,
        registration_deadline: data.registrationDeadline ? data.registrationDeadline.toISOString() : null,
        venue_id: venueId,
        parking_instructions: data.parkingInstructions,
        organizer_id: user.id,
        organizer_name: data.organizerName,
        organizer_email: data.organizerEmail,
        organizer_phone: data.organizerPhone,
        website_url: data.websiteUrl,
        is_free: data.isFree,
        general_admission_price: !data.isFree && data.generalAdmission ? parseFloat(data.generalAdmission) : null,
        vip_ticket_price: !data.isFree && data.vipTicket ? parseFloat(data.vipTicket) : null,
        has_early_bird: !data.isFree && data.hasEarlyBird,
        early_bird_deadline: !data.isFree && data.hasEarlyBird && data.earlyBirdDeadline
          ? data.earlyBirdDeadline.toISOString()
          : null,
        group_discount: !data.isFree && data.groupDiscount,
        payment_methods: !data.isFree && data.paymentMethods ? data.paymentMethods : null,
        refund_policy: !data.isFree ? data.refundPolicy : null,
        // Organizer Terms
        organizer_terms_enabled: data.organizerTermsEnabled,
        organizer_terms: data.organizerTermsEnabled ? data.organizerTerms : null,
      };

      const { data: event, error } = await supabase
        .from('events')
        .insert(eventData)
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "Event created successfully",
        description: "Your event has been published.",
      });

      setTimeout(() => navigate(`/events/${event.id}`), 1500);
    } catch (error) {
      console.error('Error creating event:', error);
      toast({
        title: "Error",
        description: "There was an error creating your event. Please try again.",
        variant: "destructive",
      });
    } finally {
      setFormLoading(false);
    }
  };

  return {
    form,
    isFree,
    isMultiDay,
    hasEarlyBird,
    coverImageUrl,
    additionalImageUrls,
    handleImageChange,
    handleImageClick,
    handleAdditionalImagesChange,
    handleAdditionalImagesClick,
    removeAdditionalImage,
    createVenue,
    onSubmit,
    // Error detection utilities
    getSectionErrors,
    getSectionErrorCount,
    getFirstErrorSection,
    getAllErrorSummary,
    // Progressive validation utilities
    isSectionValid,
    isSectionTouched,
    getSectionCompletionStatus,
  };
};
