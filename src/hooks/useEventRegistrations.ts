import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from './use-toast';
import { dataCache } from '@/utils/cacheUtils';

import { RegistrationStatus } from '@/types/payment';

export interface EventAttendee {
  id: string;
  user_id: string;
  event_id: string;
  registration_date: string;
  registration_status: RegistrationStatus; // Added
  ticket_type: string | null;
  quantity: number | null;
  unit_price: number | null;
  total_amount: number | null;
  payment_method: string | null;
  user_details: {
    full_name: string | null;
    email: string | null;
    phone_number: string | null;
  };
}

export interface EventRegistrationStats {
  totalAttendees: number;
  totalTickets: number;
  totalRevenue: number;
  maxAttendees: number | null;
}

export function useEventRegistrations(eventId: string | null) {
  const [attendees, setAttendees] = useState<EventAttendee[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<EventRegistrationStats>({
    totalAttendees: 0,
    totalTickets: 0,
    totalRevenue: 0,
    maxAttendees: null
  });
  const [page, setPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [hasMore, setHasMore] = useState(true);
  const pageSize = 10; // Number of attendees per page
  const { toast } = useToast();

  // Fetch event statistics (counts only)
  const fetchEventStats = useCallback(async () => {
    if (!eventId) return;
    
    const cacheKey = `event_stats_${eventId}`;
    const cachedStats = dataCache.get<EventRegistrationStats>(cacheKey);
    
    if (cachedStats) {
      setStats(cachedStats);
      return;
    }
    
    try {
      // Get total count of attendees
      const { count: attendeeCount, error: countError } = await supabase
        .from('registrations')
        .select('id', { count: 'exact' })
        .eq('event_id', eventId);
      
      if (countError) throw countError;
      
      // Get max_attendees from the events table
      const { data: eventData, error: eventError } = await supabase
        .from('events')
        .select('max_attendees')
        .eq('id', eventId)
        .single();
      
      if (eventError) throw eventError;
      
      // Calculate tickets and revenue directly from the registrations table
      const { data: registrationsData, error: registrationsError } = await supabase
        .from('registrations')
        .select('quantity, total_amount')
        .eq('event_id', eventId);
      
      if (registrationsError) throw registrationsError;
      
      // Calculate totals from the data
      let totalTickets = 0;
      let totalRevenue = 0;
      
      if (registrationsData && registrationsData.length > 0) {
        totalTickets = registrationsData.reduce((sum, reg) => sum + (reg.quantity || 0), 0);
        totalRevenue = registrationsData.reduce((sum, reg) => sum + (reg.total_amount || 0), 0);
      }
      
      const newStats = {
        totalAttendees: attendeeCount || 0,
        totalTickets,
        totalRevenue,
        maxAttendees: eventData?.max_attendees || null
      };
      
      dataCache.set(cacheKey, newStats, 5 * 60 * 1000); // Cache for 5 minutes
      setStats(newStats);
    } catch (err) {
      console.error('Error fetching event stats:', err);
    }
  }, [eventId]);

  // Fetch attendees with pagination and search
  const fetchAttendees = useCallback(async (reset = false) => {
    if (!eventId) {
      setAttendees([]);
      setLoading(false);
      return;
    }

    const currentPage = reset ? 1 : page;
    const from = (currentPage - 1) * pageSize;
    const to = from + pageSize - 1;
    
    // Different cache keys for different search queries and pages
    const cacheKey = `event_registrations_${eventId}_${searchQuery}_page${currentPage}`;
    const cachedData = dataCache.get<{
      attendees: EventAttendee[];
      hasMore: boolean;
    }>(cacheKey);
    
    if (cachedData) {
      if (reset) {
        setAttendees(cachedData.attendees);
      } else {
        setAttendees(prev => [...prev, ...cachedData.attendees]);
      }
      setHasMore(cachedData.hasMore);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      
      let data;
      let error;
      
      // If we have a search query, we need to first find matching profiles
      if (searchQuery) {
        // First, find profiles that match the search query
        const { data: matchingProfiles, error: profileError } = await supabase
          .from('profiles')
          .select('id')
          .or(`full_name.ilike.%${searchQuery}%,email.ilike.%${searchQuery}%,phone_number.ilike.%${searchQuery}%`);
        
        if (profileError) throw profileError;
        
        // If we found matching profiles, get their registrations
        if (matchingProfiles && matchingProfiles.length > 0) {
          const userIds = matchingProfiles.map(profile => profile.id);
          
          // Then get registrations for these users for this event
          const response = await supabase
            .from('registrations')
            .select(`
              *,
              user_details:profiles(
                full_name,
                email,
                phone_number
              )
            `)
            .eq('event_id', eventId)
            .in('user_id', userIds)
            .order('registration_date', { ascending: false })
            .range(from, to);
          
          data = response.data;
          error = response.error;
        } else {
          // No matching profiles found
          data = [];
          error = null;
        }
      } else {
        // No search query, just get all registrations for this event
        const response = await supabase
          .from('registrations')
          .select(`
            *,
            user_details:profiles(
              full_name,
              email,
              phone_number
            )
          `)
          .eq('event_id', eventId)
          .order('registration_date', { ascending: false })
          .range(from, to);
        
        data = response.data;
        error = response.error;
      }
      
      if (error) throw error;

      const processedAttendees = data as EventAttendee[];
      
      // Check if there are more results
      const newHasMore = processedAttendees.length === pageSize;
      
      // Cache the results
      dataCache.set(cacheKey, {
        attendees: processedAttendees,
        hasMore: newHasMore
      }, 5 * 60 * 1000); // Cache for 5 minutes
      
      if (reset) {
        setAttendees(processedAttendees);
      } else {
        setAttendees(prev => [...prev, ...processedAttendees]);
      }
      setHasMore(newHasMore);
    } catch (err) {
      console.error('Error fetching event registrations:', err);
      setError('Failed to load event registrations');
      toast({
        title: 'Error',
        description: 'Failed to load registration details. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [eventId, page, pageSize, searchQuery, toast]);

  // Load more attendees
  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      setPage(prevPage => prevPage + 1);
    }
  }, [loading, hasMore]);

  // Search attendees
  const searchAttendees = useCallback((query: string) => {
    setSearchQuery(query);
    setPage(1);
  }, []);

  // Initial data fetch
  useEffect(() => {
    fetchEventStats();
  }, [fetchEventStats]);

  // Fetch attendees when page, search query, or event ID changes
  useEffect(() => {
    fetchAttendees(page === 1);
  }, [fetchAttendees, page]);
  
  // Set up a subscription for real-time updates
  useEffect(() => {
    if (!eventId) return;
    
    const registrationsSubscription = supabase
      .channel('public:registrations')
      .on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'registrations',
        filter: `event_id=eq.${eventId}` 
      }, () => {
        // When data changes, invalidate cache and refresh data
        if (eventId) {
          // Clear all related caches using the getKeys method
          const cacheKeys = dataCache.getKeys();
          cacheKeys.forEach(key => {
            if (key.startsWith(`event_registrations_${eventId}`) || 
                key.startsWith(`event_stats_${eventId}`)) {
              dataCache.remove(key);
            }
          });
          
          // Refresh data
          fetchEventStats();
          fetchAttendees(true);
        }
      })
      .subscribe();

    return () => {
      // Clean up subscription
      supabase.removeChannel(registrationsSubscription);
    };
  }, [eventId, fetchEventStats, fetchAttendees]);

  return { 
    attendees, 
    stats,
    loading, 
    error,
    hasMore,
    searchQuery,
    loadMore,
    searchAttendees
  };
}
