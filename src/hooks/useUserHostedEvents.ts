import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from './use-toast';
import { useAuth } from '@/context/AuthContext';
import { Event } from './useEvents';
import { dataCache } from '@/utils/cacheUtils';

export function useUserHostedEvents() {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    const fetchUserHostedEvents = async () => {
      if (!user) {
        setEvents([]);
        setLoading(false);
        return;
      }

      // Check cache first
      const cacheKey = `user_hosted_events_${user.id}`;
      const cachedData = dataCache.get<Event[]>(cacheKey);
      
      if (cachedData) {
        setEvents(cachedData);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        
        const { data, error } = await supabase
          .from('events')
          .select(`
            *,
            category:event_categories(id, name, color, text_color, icon),
            venue:event_venues(*)
          `)
          .eq('organizer_id', user.id)
          .order('created_at', { ascending: false });
        
        if (error) throw error;

        const processedEvents = data.map(item => ({
          ...item,
          tags: item.tags || [],
          additional_images: Array.isArray(item.additional_images) 
            ? item.additional_images 
            : [],
          location: item.venue 
            ? `${item.venue.name}, ${item.venue.city}, ${item.venue.state}` 
            : `${item.venue_name}, ${item.city}, ${item.state}`
        })) as Event[];
        
        // Cache the data
        dataCache.set(cacheKey, processedEvents);
        
        setEvents(processedEvents);
      } catch (err) {
        console.error('Error fetching user hosted events:', err);
        setError('Failed to load your events');
        toast({
          title: 'Error',
          description: 'Failed to load your hosted events. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchUserHostedEvents();
    
    // Set up a subscription for real-time updates
    const eventsSubscription = supabase
      .channel('public:events')
      .on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'events',
        filter: `organizer_id=eq.${user?.id}` 
      }, () => {
        // When data changes, invalidate cache
        if (user) {
          dataCache.remove(`user_hosted_events_${user.id}`);
          fetchUserHostedEvents();
        }
      })
      .subscribe();

    return () => {
      // Clean up subscription
      supabase.removeChannel(eventsSubscription);
    };
  }, [user, toast]);

  return { events, loading, error };
}
