
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from './use-toast';
import { EventCategory } from '@/types/category';

export function useCategories() {
  const [categories, setCategories] = useState<EventCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        
        const { data, error } = await supabase
          .from('event_categories')
          .select('*')
          .order('priority', { ascending: true });
        
        if (error) throw error;
        
        setCategories(data || []);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories');
        toast({
          title: 'Error',
          description: 'Failed to load categories. Please try again later.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, [toast]);

  // Function to create a new category (admin only)
  const createCategory = async (category: Omit<EventCategory, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const { data, error } = await supabase
        .from('event_categories')
        .insert(category)
        .select()
        .single();
      
      if (error) throw error;
      
      setCategories(prev => [...prev, data]);
      
      toast({
        title: 'Category created',
        description: `${category.name} category has been created successfully.`,
      });
      
      return data;
    } catch (err) {
      console.error('Error creating category:', err);
      toast({
        title: 'Error',
        description: 'Failed to create category. Please try again.',
        variant: 'destructive',
      });
      return null;
    }
  };

  // Function to update a category (admin only)
  const updateCategory = async (id: string, updates: Partial<EventCategory>) => {
    try {
      const { data, error } = await supabase
        .from('event_categories')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      
      setCategories(prev => 
        prev.map(category => category.id === id ? data : category)
      );
      
      toast({
        title: 'Category updated',
        description: `Category has been updated successfully.`,
      });
      
      return data;
    } catch (err) {
      console.error('Error updating category:', err);
      toast({
        title: 'Error',
        description: 'Failed to update category. Please try again.',
        variant: 'destructive',
      });
      return null;
    }
  };

  // Function to delete a category (admin only)
  const deleteCategory = async (id: string) => {
    try {
      const { error } = await supabase
        .from('event_categories')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
      
      setCategories(prev => prev.filter(category => category.id !== id));
      
      toast({
        title: 'Category deleted',
        description: 'Category has been deleted successfully.',
      });
      
      return true;
    } catch (err) {
      console.error('Error deleting category:', err);
      toast({
        title: 'Error',
        description: 'Failed to delete category. Please try again.',
        variant: 'destructive',
      });
      return false;
    }
  };

  // Get active categories only
  const activeCategories = categories.filter(category => category.active);
  
  // Ensure categories are properly sorted by priority
  const sortedCategories = [...categories].sort((a, b) => a.priority - b.priority);
  const sortedActiveCategories = [...activeCategories].sort((a, b) => a.priority - b.priority);
  
  // Get popular categories based on priority (top 5)
  const popularCategories = sortedActiveCategories.slice(0, 5);

  return {
    categories: sortedCategories,
    activeCategories: sortedActiveCategories,
    popularCategories,
    loading,
    error,
    createCategory,
    updateCategory,
    deleteCategory
  };
}
