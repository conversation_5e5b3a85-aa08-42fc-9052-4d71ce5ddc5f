import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface OrganizationWithEvents {
  id: string;
  name: string;
  description?: string;
  logo_url?: string;
  city?: string;
  eventCount: number;
}

export const useOrganizationsWithEvents = (limit: number = 9) => {
  const [organizations, setOrganizations] = useState<OrganizationWithEvents[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrganizationsWithEvents = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get today's date for filtering future events
        const today = new Date().toISOString().split('T')[0];

        // First, get all approved organizations
        const { data: orgData, error: orgError } = await supabase
          .from('organizations')
          .select(`
            id,
            name,
            description,
            logo_url,
            city
          `)
          .eq('approval_status', 'approved');

        if (orgError) {
          console.error('Error fetching organizations:', orgError);
          setError(orgError.message);
          return;
        }

        if (!orgData || orgData.length === 0) {
          setOrganizations([]);
          return;
        }

        // Now get event counts for each organization
        const organizationsWithEventCounts: OrganizationWithEvents[] = [];

        for (const org of orgData) {
          // Count future approved events for this organization
          const { count: eventCount, error: countError } = await supabase
            .from('events')
            .select('id', { count: 'exact' })
            .eq('organization_id', org.id)
            .eq('approval_status', 'approved')
            .gte('start_date', today);

          if (countError) {
            console.error(`Error counting events for organization ${org.name}:`, countError);
            continue;
          }

          // Only include organizations that have upcoming events
          if (eventCount && eventCount > 0) {
            organizationsWithEventCounts.push({
              id: org.id,
              name: org.name,
              description: org.description,
              logo_url: org.logo_url,
              city: org.city,
              eventCount: eventCount
            });
          }
        }

        // Sort by event count (descending) and limit results
        const sortedOrganizations = organizationsWithEventCounts
          .sort((a, b) => b.eventCount - a.eventCount)
          .slice(0, limit);

        setOrganizations(sortedOrganizations);

      } catch (err) {
        console.error('Error in fetchOrganizationsWithEvents:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchOrganizationsWithEvents();
  }, [limit]);

  return { organizations, loading, error };
};
