import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface OrganizationWithEvents {
  id: string;
  name: string;
  description?: string;
  logo_url?: string;
  city?: string;
  eventCount: number;
}

export const useOrganizationsWithEvents = (limit: number = 9) => {
  const [organizations, setOrganizations] = useState<OrganizationWithEvents[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrganizationsWithEvents = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get current date and time
        const now = new Date();

        // First, get all approved organizations
        const { data: orgData, error: orgError } = await supabase
          .from('organizations')
          .select(`
            id,
            name,
            description,
            logo_url,
            city
          `)
          .eq('approval_status', 'approved');

        if (orgError) {
          console.error('Error fetching organizations:', orgError);
          setError(orgError.message);
          return;
        }

        if (!orgData || orgData.length === 0) {
          setOrganizations([]);
          return;
        }

        // Now get event counts for each organization
        const organizationsWithEventCounts: OrganizationWithEvents[] = [];

        for (const org of orgData) {
          // Fetch approved events for this organization
          const { data: eventsData, error: eventsError } = await supabase
            .from('events')
            .select('start_date, end_date')
            .eq('organization_id', org.id)
            .eq('approval_status', 'approved');

          if (eventsError) {
            console.error(`Error fetching events for organization ${org.name}:`, eventsError);
            continue;
          }

          let activeEventCount = 0;
          if (eventsData) {
            for (const event of eventsData) {
              const endDate = event.end_date ? new Date(event.end_date) : null;
              const startDate = event.start_date ? new Date(event.start_date) : null;

              if (endDate) {
                if (endDate > now) {
                  activeEventCount++;
                }
              } else if (startDate) {
                if (startDate > now) {
                  activeEventCount++;
                }
              }
            }
          }

          // Only include organizations that have active events
          if (activeEventCount > 0) {
            organizationsWithEventCounts.push({
              id: org.id,
              name: org.name,
              description: org.description,
              logo_url: org.logo_url,
              city: org.city,
              eventCount: activeEventCount
            });
          }
        }

        // Sort by event count (descending) and limit results
        const sortedOrganizations = organizationsWithEventCounts
          .sort((a, b) => b.eventCount - a.eventCount)
          .slice(0, limit);

        setOrganizations(sortedOrganizations);

      } catch (err) {
        console.error('Error in fetchOrganizationsWithEvents:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchOrganizationsWithEvents();
  }, [limit]);

  return { organizations, loading, error };
};
