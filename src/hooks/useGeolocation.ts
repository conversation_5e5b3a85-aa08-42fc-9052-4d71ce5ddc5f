import { useState, useCallback } from 'react';

// Geolocation interfaces (consider moving to types file if used elsewhere)
export interface GeolocationCoordinates {
  latitude: number;
  longitude: number;
  altitude: number | null;
  accuracy: number;
  altitudeAccuracy: number | null;
  heading: number | null;
  speed: number | null;
}

export interface GeolocationPosition {
  coords: GeolocationCoordinates;
  timestamp: number;
}

export interface GeolocationState {
  loading: boolean;
  error: GeolocationPositionError | Error | null;
  coordinates: GeolocationCoordinates | null; // Store full coordinates object
  timestamp: number | null;
}

export interface GeolocationOptions {
  enableHighAccuracy?: boolean;
  timeout?: number;
  maximumAge?: number;
  // Removed onSuccess/onError callbacks as we return state and a fetch function
}

/**
 * Custom hook to access browser geolocation with a one-time fetch function.
 * @param options Geolocation options
 * @returns Object containing GeolocationState and a function to fetch coordinates.
 */
export const useGeolocation = (options: GeolocationOptions = {}) => {
  const [state, setState] = useState<GeolocationState>({
    loading: false, 
    error: null,
    coordinates: null,
    timestamp: null,
  });

  const fetchCoordinates = useCallback(() => {
    if (!navigator.geolocation) {
      setState({
        loading: false,
        error: new Error('Geolocation is not supported by this browser'),
        coordinates: null,
        timestamp: null,
      });
      return;
    }

    setState(prevState => ({ ...prevState, loading: true, error: null }));

    const geoOptions: PositionOptions = {
      enableHighAccuracy: options.enableHighAccuracy ?? false,
      timeout: options.timeout ?? 10000,
      maximumAge: options.maximumAge ?? 0,
    };

    const successHandler = (position: GeolocationPosition) => {
      setState({
        loading: false,
        error: null,
        coordinates: position.coords, // Store the full coords object
        timestamp: position.timestamp,
      });
    };

    const errorHandler = (error: GeolocationPositionError) => {
      setState({
        loading: false,
        error,
        coordinates: null,
        timestamp: null,
      });
    };

    navigator.geolocation.getCurrentPosition( // Use getCurrentPosition
      successHandler,
      errorHandler,
      geoOptions
    );
    // No need to clear watch ID as we are using getCurrentPosition

  }, [options]); // Dependencies include options

  // Return the state and the function to trigger the fetch
  return { ...state, fetchCoordinates }; 
};
