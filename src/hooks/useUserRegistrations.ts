import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from './use-toast';
import { useAuth } from '@/context/AuthContext';
import { Event } from './useEvents';
import { dataCache } from '@/utils/cacheUtils';

export interface UserRegistration {
  id: string;
  event_id: string;
  registration_date: string;
  payment_status: string | null;
  ticket_type: string | null;
  quantity: number | null;
  unit_price: number | null;
  total_amount: number | null;
  payment_method: string | null;
  event: Event;
}

export function useUserRegistrations() {
  const [registrations, setRegistrations] = useState<UserRegistration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    const fetchUserRegistrations = async () => {
      if (!user) {
        setRegistrations([]);
        setLoading(false);
        return;
      }

      // Check cache first
      const cacheKey = `user_registrations_${user.id}`;
      const cachedData = dataCache.get<UserRegistration[]>(cacheKey);
      
      if (cachedData) {
        setRegistrations(cachedData);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        
        const { data, error } = await supabase
          .from('registrations')
          .select(`
            *,
            event:events(
              *,
              category:event_categories(id, name, color, text_color, icon),
              venue:event_venues(*)
            )
          `)
          .eq('user_id', user.id)
          .order('registration_date', { ascending: false });
        
        if (error) throw error;

        const processedRegistrations = data.map(item => ({
          ...item,
          event: {
            ...item.event,
            tags: item.event.tags || [],
            additional_images: Array.isArray(item.event.additional_images) 
              ? item.event.additional_images 
              : [],
            location: item.event.venue 
              ? `${item.event.venue.name}, ${item.event.venue.city}, ${item.event.venue.state}` 
              : `${item.event.venue_name}, ${item.event.city}, ${item.event.state}`
          }
        })) as UserRegistration[];
        
        // Cache the data
        dataCache.set(cacheKey, processedRegistrations);
        
        setRegistrations(processedRegistrations);
      } catch (err) {
        console.error('Error fetching user registrations:', err);
        setError('Failed to load your registrations');
        toast({
          title: 'Error',
          description: 'Failed to load your registrations. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchUserRegistrations();
    
    // Set up a subscription for real-time updates
    const registrationsSubscription = supabase
      .channel('public:registrations')
      .on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'registrations',
        filter: `user_id=eq.${user?.id}` 
      }, () => {
        // When data changes, invalidate cache
        if (user) {
          dataCache.remove(`user_registrations_${user.id}`);
          fetchUserRegistrations();
        }
      })
      .subscribe();

    return () => {
      // Clean up subscription
      supabase.removeChannel(registrationsSubscription);
    };
  }, [user, toast]);

  return { registrations, loading, error };
}
