import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from './use-toast';
import { useAuth } from '@/context/AuthContext';
import { EventPaymentConfig } from '@/types/payment';

export interface Event extends EventPaymentConfig {
  id: string;
  title: string;
  description: string;
  tags: string[];
  image_url: string | null;
  start_date: string;
  end_date: string | null;
  is_multi_day: boolean;
  max_attendees: number | null;
  registration_deadline: string | null;
  parking_instructions: string | null;
  organizer_id: string;
  organizer_name: string;
  organizer_email: string | null;
  organizer_phone: string | null;
  website_url: string | null;
  organization_id: string | null;
  hosted_by_type: 'individual' | 'organization';
  is_free: boolean;
  general_admission_price: number | null;
  vip_ticket_price: number | null;
  has_early_bird: boolean;
  early_bird_deadline: string | null;
  group_discount: boolean;
  payment_methods: string[] | null;
  refund_policy: string | null;
  created_at: string;
  updated_at: string;
  approval_status: 'pending' | 'approved' | 'rejected';
  approval_notes: string | null;
  additional_images: string[] | null;
  category_id: string | null;
  venue_id: string;
  poster_image_filename: string | null;
  // These fields are kept for backward compatibility
  venue_name?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  // Computed field for display purposes
  location?: string;
  // Added properties for the join with event_categories and event_venues
  category?: {
    id: string;
    name: string;
    color?: string;
    text_color?: string;
    icon?: string | null;
  };
  venue?: {
    id: string;
    name: string;
    address: string;
    city: string;
    state: string;
    zip_code: string;
    locality?: string | null;
    place_id?: string | null;
    created_at?: string;
    updated_at?: string | null;
    priority?: number;
  } | null;
  registrations?: { quantity: number }[];
}

export interface EventsQueryParams {
  search?: string;
  categoryId?: string;
  venueId?: string;
  organizationId?: string;
  dateStart?: string;
  dateEnd?: string;
  page?: number;
  limit?: number;
  featured?: boolean;
  includePastEvents?: boolean;
}

export interface PaginatedEvents {
  data: Event[];
  count: number;
  hasMore: boolean;
}

const PAGE_SIZE = 10;

// Helper function to safely process data with potential type issues
const processEvents = (data: any[]): Event[] => {
  return data.map(event => {
    // Format location using venue data if available
    let location = '';
    if (event.venue && typeof event.venue === 'object' && !('error' in event.venue)) {
      location = `${event.venue.name}, ${event.venue.city}, ${event.venue.state}`;
    } else {
      // Fallback to old fields for backward compatibility
      location = `${event.venue_name || ''}, ${event.city || ''}, ${event.state || ''}`.replace(/^, /, '').replace(/, $/, '');
    }

    return {
      ...event,
      location,
      // Ensure venue is properly typed
      venue: event.venue && typeof event.venue === 'object' && !('error' in event.venue)
        ? event.venue
        : null
    } as Event;
  });
};

export function useEvents(limit?: number, featured?: boolean) {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalPages, setTotalPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true);

        let query = supabase
          .from('events')
          .select(`
            *,
            category: event_categories (*),
            venue: event_venues (*),
            registrations: registrations (quantity)
          `);

        // For featured events on the homepage, we want to show upcoming events first
        const now = new Date();
        const nowISO = now.toISOString();

        if (featured) {
          // Featured events are always approved
          query = query.eq('approval_status', 'approved');
        } else {
          // For non-featured, apply user-specific approval logic
          if (!user?.id) {
            query = query.eq('approval_status', 'approved');
          } else {
            query = query.or(`approval_status.eq.approved,and(approval_status.eq.pending,organizer_id.eq.${user.id})`);
          }
        }

        // Common logic for active events (applied after approval status):
        // 1. Events that started in the past but are still ongoing (end_date > nowISO)
        // 2. Events that start now or in the future (start_date >= nowISO)
        // 3. Events with no end_date that start in the future (end_date IS NULL AND start_date > nowISO)
        // The condition `start_date.gte.${nowISO}` covers future events, including those with null end_date.
        // The condition `and(start_date.lt.${nowISO},end_date.gt.${nowISO})` covers ongoing events.
        // The condition `and(end_date.is.null,start_date.gt.${nowISO})` is for future events with no end date.
        // Simplified and combined:
        query = query.or(
          `and(start_date.lt.${nowISO},end_date.gt.${nowISO}),` + // Ongoing
          `start_date.gte.${nowISO},` + // Starts now or in future (covers null end_date if start_date is future)
          `and(end_date.is.null,start_date.gt.${nowISO})` // Explicitly future with null end_date (might be redundant but safe)
        );

        // Set ordering
        query = query.order('start_date', { ascending: true });

        // Add limit if specified
        if (limit) {
          query = query.limit(limit);
        }

        const { data, error } = await query;

        if (error) throw error;

        // Use the helper function to process events
        const processedEvents = processEvents(data);
        setEvents(processedEvents);
      } catch (err) {
        console.error('Error fetching events:', err);
        setError('Failed to load events');
        toast({
          title: 'Error',
          description: 'Failed to load events. Please try again later.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, [limit, featured, toast, user]);

  // New function for paginated and filtered events
  const fetchPaginatedEvents = useCallback(async (params: EventsQueryParams): Promise<PaginatedEvents> => {
    try {
      let query = supabase
        .from('events')
        .select(`
          *,
          category: event_categories (*),
          venue: event_venues (*),
          registrations: registrations (quantity)
        `);

      // Apply filters
      if (params.search) {
        // Enhanced search to include title, description, tags, and category name
        query = query.or(
          `title.ilike.%${params.search}%,` +
          `description.ilike.%${params.search}%,` +
          `tags.cs.{${params.search}}` // Search in tags array using contains operator
        );
      }

      if (params.categoryId) {
        query = query.eq('category_id', params.categoryId);
      }

      if (params.venueId) {
        query = query.eq('venue_id', params.venueId);
      }

      if (params.organizationId) {
        query = query.eq('organization_id', params.organizationId);
      }

      if (params.dateStart) {
        query = query.gte('start_date', params.dateStart);
      }

      if (params.dateEnd) {
        query = query.lte('start_date', params.dateEnd);
      }

      // Only show approved events to non-creators, or the user's own pending events
      if (!user?.id) {
        // Non-authenticated users only see approved events
        query = query.eq('approval_status', 'approved');
      } else {
        // Authenticated users see approved events and their own pending events
        query = query.or(`approval_status.eq.approved,and(approval_status.eq.pending,organizer_id.eq.${user.id})`);
      }

      // Handle past events differently if requested
      const now = new Date(); // Define now once
      const nowISO = now.toISOString();

      if (params.includePastEvents) {
        // For past events, show events that have an end_date in the past,
        // or have a start_date in the past if no end_date.
        // This means (end_date < nowISO) OR (end_date IS NULL AND start_date < nowISO)
        query = query.or(
          `end_date.lt.${nowISO},` +
          `and(end_date.is.null,start_date.lt.${nowISO})`
        );
        query = query.order('start_date', { ascending: false }); // Most recent past events first
      } else {
        // Logic for "active" events (not past)
        // 1. Ongoing: start_date < nowISO AND end_date > nowISO
        // 2. Future: start_date >= nowISO
        // 3. Future (no end_date): end_date IS NULL AND start_date > nowISO
        query = query.or(
          `and(start_date.lt.${nowISO},end_date.gt.${nowISO}),` + // Ongoing
          `start_date.gte.${nowISO},` + // Starts now or in future
          `and(end_date.is.null,start_date.gt.${nowISO})` // Future with null end_date
        );
        query = query.order('start_date', { ascending: true }); // Soonest upcoming events first
      }

      // Get total count for pagination with the same filters
      let countQuery = supabase
        .from('events')
        .select('id', { count: 'exact', head: true });

      // Apply the same filters to the count query
      if (params.search) {
        // Enhanced search to include title, description, tags, and category name
        countQuery = countQuery.or(
          `title.ilike.%${params.search}%,` +
          `description.ilike.%${params.search}%,` +
          `tags.cs.{${params.search}}` // Search in tags array using contains operator
        );
      }

      if (params.categoryId) {
        countQuery = countQuery.eq('category_id', params.categoryId);
      }

      if (params.venueId) {
        countQuery = countQuery.eq('venue_id', params.venueId);
      }

      if (params.organizationId) {
        countQuery = countQuery.eq('organization_id', params.organizationId);
      }

      if (params.dateStart) {
        countQuery = countQuery.gte('start_date', params.dateStart);
      }

      if (params.dateEnd) {
        countQuery = countQuery.lte('start_date', params.dateEnd);
      }

      // Apply the same date filtering to countQuery as to the main query
      if (params.includePastEvents) {
        countQuery = countQuery.or(
          `end_date.lt.${nowISO},` +
          `and(end_date.is.null,start_date.lt.${nowISO})`
        );
      } else {
        countQuery = countQuery.or(
          `and(start_date.lt.${nowISO},end_date.gt.${nowISO}),` +
          `start_date.gte.${nowISO},` +
          `and(end_date.is.null,start_date.gt.${nowISO})`
        );
      }

      // Apply the same approval status filtering to count query
      if (!user?.id) {
        // Non-authenticated users only see approved events
        countQuery = countQuery.eq('approval_status', 'approved');
      } else {
        // Authenticated users see approved events and their own pending events
        countQuery = countQuery.or(`approval_status.eq.approved,and(approval_status.eq.pending,organizer_id.eq.${user.id})`);
      }

      const { count: totalCount, error: countError } = await countQuery;

      if (countError) {
        throw countError;
      }

      // Apply pagination
      const pageSize = params.limit || PAGE_SIZE;
      const from = ((params.page || 1) - 1) * pageSize;
      const to = from + pageSize - 1;

      query = query.range(from, to);

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      // Use the helper function to process events
      const processedEvents = processEvents(data || []);

      // Calculate if there are more pages
      const hasMore = from + processedEvents.length < (totalCount || 0);

      // Return the paginated events result
      return {
        data: processedEvents,
        count: totalCount || 0,
        hasMore
      };
    } catch (error) {
      console.error('Error fetching paginated events:', error);
      // Return empty result on error
      return {
        data: [],
        count: 0,
        hasMore: false
      };
    }
  }, [user]);

  // Memoize the getEventById function to prevent recreating it on every render
  const getEventById = useCallback(async (id: string) => {
    try {
      setLoading(true);

      const { data, error } = await supabase
        .from('events')
        .select(`
          *,
          category: event_categories (id, name, color, text_color, icon),
          venue: event_venues (id, name, address, city, state, zip_code)
        `)
        .eq('id', id)
        .maybeSingle(); // Use maybeSingle instead of single to avoid errors if no event is found

      if (error) throw error;

      if (!data) {
        // Return null if no event is found
        return null;
      }

      // Use the helper function to process the single event
      const processedEvent = data ? processEvents([data])[0] : null;
      return processedEvent;
    } catch (err) {
      console.error('Error fetching event:', err);
      setError('Failed to load event details');
      toast({
        title: 'Error',
        description: 'Failed to load event details. Please try again later.',
        variant: 'destructive',
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Check if the current user is registered for a specific event
  const checkUserRegistration = useCallback(async (eventId: string) => {
    if (!user) return false;

    try {
      const { data, error } = await supabase
        .from('registrations')
        .select('id')
        .eq('event_id', eventId)
        .eq('user_id', user.id)
        .maybeSingle();

      if (error) {
        console.error('Error checking registration:', error);
        return false;
      }

      // If data exists, user is already registered
      return !!data;
    } catch (err) {
      console.error('Exception checking registration:', err);
      return false;
    }
  }, [user]);

  // Get the registration status for the current user for a specific event
  const getUserRegistrationStatus = useCallback(async (eventId: string): Promise<string | null> => {
    if (!user) return null;

    try {
      const { data, error } = await supabase
        .from('registrations')
        .select('registration_status, events (is_free), payment_proof_url')
        .eq('event_id', eventId)
        .eq('user_id', user.id)
        .maybeSingle();

      if (error) {
        console.error('Error getting registration status:', error);
        return null;
      }

      if (!data) {
        return null; // Not registered
      }

      // Assuming 'data.events' is correctly populated by Supabase relation
      // If 'events' is an array, take the first element. If it's an object, use it directly.
      const eventDetails = Array.isArray(data.events) ? data.events[0] : data.events;
      const dbStatus = data.registration_status;

      if (dbStatus === 'approved') {
        return 'accepted';
      }
      if (dbStatus === 'declined') {
        return 'declined';
      }
      // Explicitly handle 'payment-pending' if it comes directly from DB
      if (dbStatus === 'payment-pending') {
        return 'pending_payment';
      }
      if (dbStatus === 'pending') { // This is the generic 'pending'
        // If event is paid AND no payment proof is uploaded yet, it's 'pending_payment'
        // This case might be redundant if DB reliably sends 'payment-pending' for this state.
        // However, keeping it provides a fallback if 'payment-pending' isn't always set from DB side for this scenario.
        if (eventDetails && !eventDetails.is_free && !data.payment_proof_url) {
          return 'pending_payment';
        }
        // Otherwise, a generic 'pending' (e.g., free event pending admin action, or paid event with proof pending admin review)
        return 'awaiting-confirmation';
      }
      // Add other statuses like 'cancelled' if needed, or map them appropriately
      // If dbStatus is already one of the UI states, it could be returned directly,
      // but the explicit mapping above is safer.
      // Fallback for any other unmapped status from DB:
      console.warn(`Unhandled registration status from DB: ${dbStatus}`);
      return null; // Or map to a default UI state like 'awaiting-confirmation' or null
    } catch (err) {
      console.error('Exception getting registration status:', err);
      return null;
    }
  }, [user]);

  return {
    events,
    loading,
    error,
    getEventById,
    checkUserRegistration,
    getUserRegistrationStatus, // Export the new function
    fetchPaginatedEvents,
    totalPages,
    currentPage,
  };
}
