import { useEffect, useCallback, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import {
  initializeAnalytics,
  trackPageView,
  trackEvent,
  trackAuth,
  trackEventInteraction,
  trackSearch,
  trackForm,
  trackError,
  trackTiming,
  setUserProperties,
  setUserId
} from '@/utils/analytics';
import { EventCategory, EventAction } from '@/config/analytics';

// Hook for initializing and using analytics
export const useAnalytics = () => {
  const location = useLocation();
  const hasTrackedInitialPageView = useRef(false);

  // Initialize analytics on mount
  useEffect(() => {
    initializeAnalytics();
  }, []);

  // Track page views on route changes
  useEffect(() => {
    const pageTitle = document.title;
    const pagePath = location.pathname + location.search;

    // Only track page view if we haven't tracked the initial one yet, or if this is a route change
    if (!hasTrackedInitialPageView.current) {
      // First page view - wait a bit for gtag to be ready
      setTimeout(() => {
        trackPageView(pagePath, pageTitle);
        hasTrackedInitialPageView.current = true;
      }, 500);
    } else {
      // Subsequent page views (route changes)
      trackPageView(pagePath, pageTitle);
    }
  }, [location]);

  // Return analytics functions for use in components
  return {
    trackEvent: useCallback((
      action: EventAction | string,
      category: EventCategory | string,
      label?: string,
      value?: number,
      customParameters?: Record<string, any>
    ) => {
      trackEvent(action, category, label, value, customParameters);
    }, []),

    trackAuth: useCallback((method: 'login' | 'logout' | 'signup', provider?: string) => {
      trackAuth(method, provider);
    }, []),

    trackEventInteraction: useCallback((
      action: 'view' | 'register' | 'share' | 'create',
      eventId?: string,
      eventTitle?: string
    ) => {
      trackEventInteraction(action, eventId, eventTitle);
    }, []),

    trackSearch: useCallback((searchTerm: string, resultCount?: number) => {
      trackSearch(searchTerm, resultCount);
    }, []),

    trackForm: useCallback((
      action: 'start' | 'submit' | 'error',
      formName: string,
      errorMessage?: string
    ) => {
      trackForm(action, formName, errorMessage);
    }, []),

    trackError: useCallback((errorMessage: string, errorLocation?: string) => {
      trackError(errorMessage, errorLocation);
    }, []),

    trackTiming: useCallback((name: string, value: number, category?: string) => {
      trackTiming(name, value, category);
    }, []),

    setUserProperties: useCallback((properties: Record<string, any>) => {
      setUserProperties(properties);
    }, []),

    setUserId: useCallback((userId: string) => {
      setUserId(userId);
    }, [])
  };
};

// Hook for tracking component mount/unmount times
export const useComponentTiming = (componentName: string) => {
  const { trackTiming } = useAnalytics();

  useEffect(() => {
    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      trackTiming(`${componentName}_mount_time`, Math.round(loadTime), 'Component Performance');
    };
  }, [componentName, trackTiming]);
};

// Hook for tracking form interactions
export const useFormAnalytics = (formName: string) => {
  const { trackForm } = useAnalytics();

  const trackFormStart = useCallback(() => {
    trackForm('start', formName);
  }, [formName, trackForm]);

  const trackFormSubmit = useCallback(() => {
    trackForm('submit', formName);
  }, [formName, trackForm]);

  const trackFormError = useCallback((errorMessage: string) => {
    trackForm('error', formName, errorMessage);
  }, [formName, trackForm]);

  return {
    trackFormStart,
    trackFormSubmit,
    trackFormError
  };
};

// Hook for tracking search interactions
export const useSearchAnalytics = () => {
  const { trackSearch } = useAnalytics();

  const trackSearchQuery = useCallback((query: string, resultCount?: number) => {
    if (query.trim()) {
      trackSearch(query.trim(), resultCount);
    }
  }, [trackSearch]);

  return {
    trackSearchQuery
  };
};

// Hook for tracking event page interactions
export const useEventAnalytics = () => {
  const { trackEventInteraction } = useAnalytics();

  const trackEventView = useCallback((eventId: string, eventTitle?: string) => {
    trackEventInteraction('view', eventId, eventTitle);
  }, [trackEventInteraction]);

  const trackEventRegister = useCallback((eventId: string, eventTitle?: string) => {
    trackEventInteraction('register', eventId, eventTitle);
  }, [trackEventInteraction]);

  const trackEventShare = useCallback((eventId: string, eventTitle?: string) => {
    trackEventInteraction('share', eventId, eventTitle);
  }, [trackEventInteraction]);

  const trackEventCreate = useCallback((eventId?: string, eventTitle?: string) => {
    trackEventInteraction('create', eventId, eventTitle);
  }, [trackEventInteraction]);

  return {
    trackEventView,
    trackEventRegister,
    trackEventShare,
    trackEventCreate
  };
};
