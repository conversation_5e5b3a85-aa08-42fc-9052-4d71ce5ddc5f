import { useState, useEffect, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useUserCoordinates } from './useUserCoordinates';
import { useUserSearchRadius } from './useUserSearchRadius';
import { useAuth } from '@/context/AuthContext';
import { Event } from './useEvents';
import { useToast } from './use-toast';

// Helper function to process events (similar to the one in useEvents)
const processEvents = (data: any[]): Event[] => {
  return data.map(event => {
    // Format location using venue data if available
    let location = '';
    if (event.venue_name) {
      // For events from the stored procedure, venue fields are flattened
      location = `${event.venue_name}, ${event.venue_city || ''}, ${event.venue_state || ''}`.replace(/^, /, '').replace(/, $/, '');
    } else if (event.venue && typeof event.venue === 'object' && !('error' in event.venue)) {
      // For events from regular queries, venue is a nested object
      location = `${event.venue.name}, ${event.venue.city || ''}, ${event.venue.state || ''}`.replace(/^, /, '').replace(/, $/, '');
    }

    // Create a properly structured category object
    const category = {
      id: event.category_id,
      name: event.category_name || 'Other',
      color: event.category_color || '#888888',
      text_color: event.category_text_color || '#FFFFFF',
      icon: event.category_icon || null
    };

    // Create a properly structured venue object
    const venue = {
      id: event.venue_id,
      name: event.venue_name || '',
      address: event.venue_address || '',
      city: event.venue_city || '',
      state: event.venue_state || '',
      zip_code: event.venue_zip_code || '',
      locality: event.venue_locality || null,
      place_id: event.venue_place_id || null,
      priority: event.venue_priority || 0
    };

    return {
      ...event,
      location,
      category,
      venue,
      // Ensure registrations is properly formatted
      registrations: Array.isArray(event.registrations)
        ? event.registrations
        : (typeof event.registrations === 'object' && event.registrations !== null)
          ? JSON.parse(JSON.stringify(event.registrations))
          : []
    } as Event;
  });
};

export function useNearbyEvents(limit?: number, includePastEvents = false) {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const userCoordinates = useUserCoordinates();
  const { searchRadius } = useUserSearchRadius();
  const { user } = useAuth();
  const { toast } = useToast();

  // Use a ref to track the current request to prevent race conditions
  const currentRequestRef = useRef<string | null>(null);

  useEffect(() => {
    const fetchNearbyEvents = async () => {
      // Generate a unique ID for this request
      const requestId = Date.now().toString();
      currentRequestRef.current = requestId;

      try {
        setLoading(true);
        setError(null);

        console.log('useNearbyEvents hook called with:', { limit, includePastEvents });
        console.log('User coordinates:', userCoordinates);
        console.log('Search radius:', searchRadius);

        // If no coordinates, fall back to regular query
        if (!userCoordinates || !userCoordinates.latitude || !userCoordinates.longitude) {
          console.log('No coordinates available, fetching events without location filtering');
          // Use the existing query from useEvents
          let query = supabase
            .from('events')
            .select(`
              *,
              category: event_categories (*),
              venue: event_venues (*),
              registrations: registrations (quantity)
            `);

          // For featured events on the homepage, we want to show upcoming events first
          if (!includePastEvents) {
            // Get events that start today or in the future, ordered by start date
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            query = query.gte('start_date', today.toISOString());
          } else {
            // For past events
            const now = new Date().toISOString();
            query = query.lt('start_date', now);
            query = query.order('start_date', { ascending: false }); // Most recent past events first
          }

          // Only show approved events to non-creators, or the user's own pending events
          if (!user?.id) {
            // Non-authenticated users only see approved events
            query = query.eq('approval_status', 'approved');
          } else {
            // Authenticated users see approved events and their own pending events
            query = query.or(`approval_status.eq.approved,and(approval_status.eq.pending,organizer_id.eq.${user.id})`);
          }

          // Default ordering by start date
          if (!includePastEvents) {
            query = query.order('start_date', { ascending: true });
          }

          // Add limit if specified
          if (limit) {
            query = query.limit(limit);
          }

          console.log('Fallback query:', query);
          const { data, error } = await query;

          if (error) throw error;

          // Check if this is still the current request
          if (currentRequestRef.current !== requestId) {
            console.log('Ignoring stale response from fallback query');
            return;
          }

          // Use the helper function to process events
          const processedEvents = processEvents(data || []);
          console.log('Fallback query returned', processedEvents.length, 'events');
          setEvents(processedEvents);
          return;
        }

        console.log(`Fetching events within ${searchRadius}km of user location:`, userCoordinates);

        // Use the stored procedure with user coordinates and search radius
        console.log('Calling find_events_within_radius with params:', {
          search_lat: userCoordinates.latitude,
          search_lng: userCoordinates.longitude,
          radius_km: searchRadius,
          include_past_events: includePastEvents,
          user_id: user?.id || null,
          result_limit: limit || null
        });

        const { data, error } = await supabase.rpc('find_events_within_radius', {
          search_lat: userCoordinates.latitude,
          search_lng: userCoordinates.longitude,
          radius_km: searchRadius,
          include_past_events: includePastEvents,
          user_id: user?.id || null,
          result_limit: limit || null
        });

        if (error) {
          console.error('Error with stored procedure, falling back to regular query:', error);
          throw error;
        }

        // Check if this is still the current request
        if (currentRequestRef.current !== requestId) {
          console.log('Ignoring stale response from stored procedure');
          return;
        }

        // Process the events from the stored procedure
        const processedEvents = processEvents(data || []);
        console.log(`Found ${processedEvents.length} events within ${searchRadius}km radius`);
        console.log('Processed events from stored procedure:', processedEvents);
        setEvents(processedEvents);
      } catch (err) {
        console.error('Error fetching nearby events:', err);
        setError('Failed to load nearby events');
        toast({
          title: 'Error',
          description: 'Failed to load events. Please try again later.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchNearbyEvents();
  }, [userCoordinates, searchRadius, limit, includePastEvents, user, toast]);

  return { events, loading, error };
}
