import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';

// Default search radius in kilometers if not set in profile
const DEFAULT_SEARCH_RADIUS_KM = 2;

/**
 * Hook to get the user's preferred search radius from their profile
 * Falls back to DEFAULT_SEARCH_RADIUS_KM if not set or if user is not logged in
 */
export const useUserSearchRadius = () => {
  const { user } = useAuth();
  const [searchRadius, setSearchRadius] = useState<number>(DEFAULT_SEARCH_RADIUS_KM);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    const fetchSearchRadius = async () => {
      if (!user) {
        setSearchRadius(DEFAULT_SEARCH_RADIUS_KM);
        return;
      }

      try {
        setLoading(true);
        
        const { data, error } = await supabase
          .from('profiles')
          .select('search_radius_km')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error fetching search radius:', error);
          setSearchRadius(DEFAULT_SEARCH_RADIUS_KM);
          return;
        }

        // If search_radius_km is null or not between 1-5, use default
        const radius = data?.search_radius_km;
        if (radius && typeof radius === 'number' && radius >= 1 && radius <= 5) {
          setSearchRadius(radius);
          console.log(`Using user's preferred search radius: ${radius}km`);
        } else {
          setSearchRadius(DEFAULT_SEARCH_RADIUS_KM);
          console.log(`Using default search radius: ${DEFAULT_SEARCH_RADIUS_KM}km`);
        }
      } catch (error) {
        console.error('Error in useUserSearchRadius:', error);
        setSearchRadius(DEFAULT_SEARCH_RADIUS_KM);
      } finally {
        setLoading(false);
      }
    };

    fetchSearchRadius();
  }, [user]);

  return { searchRadius, loading };
};
