import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface VenueWithEvents {
  id: string;
  name: string;
  address?: string;
  city?: string;
  eventCount: number;
}

export const useVenuesWithEvents = (limit: number = 9) => {
  const [venues, setVenues] = useState<VenueWithEvents[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVenuesWithEvents = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get current date and time
        const now = new Date();

        // First, get all venues
        const { data: venueData, error: venueError } = await supabase
          .from('event_venues')
          .select(`
            id,
            name,
            address,
            city
          `);

        if (venueError) {
          console.error('Error fetching venues:', venueError);
          setError(venueError.message);
          return;
        }

        if (!venueData || venueData.length === 0) {
          setVenues([]);
          return;
        }

        // Now get event counts for each venue
        const venuesWithEventCounts: VenueWithEvents[] = [];

        for (const venue of venueData) {
          // Fetch approved events for this venue
          const { data: eventsData, error: eventsError } = await supabase
            .from('events')
            .select('start_date, end_date')
            .eq('venue_id', venue.id)
            .eq('approval_status', 'approved');

          if (eventsError) {
            console.error(`Error fetching events for venue ${venue.name}:`, eventsError);
            continue;
          }

          let activeEventCount = 0;
          if (eventsData) {
            for (const event of eventsData) {
              const endDate = event.end_date ? new Date(event.end_date) : null;
              const startDate = event.start_date ? new Date(event.start_date) : null;

              if (endDate) {
                if (endDate > now) {
                  activeEventCount++;
                }
              } else if (startDate) {
                if (startDate > now) {
                  activeEventCount++;
                }
              }
            }
          }

          // Only include venues that have active events
          if (activeEventCount > 0) {
            venuesWithEventCounts.push({
              id: venue.id,
              name: venue.name,
              address: venue.address,
              city: venue.city,
              eventCount: activeEventCount
            });
          }
        }

        // Sort by event count (descending) and limit results
        const sortedVenues = venuesWithEventCounts
          .sort((a, b) => b.eventCount - a.eventCount)
          .slice(0, limit);

        setVenues(sortedVenues);

      } catch (err) {
        console.error('Error in fetchVenuesWithEvents:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchVenuesWithEvents();
  }, [limit]);

  return { venues, loading, error };
};
