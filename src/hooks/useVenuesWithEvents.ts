import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface VenueWithEvents {
  id: string;
  name: string;
  address?: string;
  city?: string;
  eventCount: number;
}

export const useVenuesWithEvents = (limit: number = 9) => {
  const [venues, setVenues] = useState<VenueWithEvents[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVenuesWithEvents = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get today's date for filtering future events
        const today = new Date().toISOString().split('T')[0];

        // First, get all venues with future approved events
        const { data: venueData, error: venueError } = await supabase
          .from('event_venues')
          .select(`
            id,
            name,
            address,
            city
          `);

        if (venueError) {
          console.error('Error fetching venues:', venueError);
          setError(venueError.message);
          return;
        }

        if (!venueData || venueData.length === 0) {
          setVenues([]);
          return;
        }

        // Now get event counts for each venue
        const venuesWithEventCounts: VenueWithEvents[] = [];

        for (const venue of venueData) {
          // Count future approved events for this venue
          const { count: eventCount, error: countError } = await supabase
            .from('events')
            .select('id', { count: 'exact' })
            .eq('venue_id', venue.id)
            .eq('approval_status', 'approved')
            .gte('start_date', today);

          if (countError) {
            console.error(`Error counting events for venue ${venue.name}:`, countError);
            continue;
          }

          // Only include venues that have events
          if (eventCount && eventCount > 0) {
            venuesWithEventCounts.push({
              id: venue.id,
              name: venue.name,
              address: venue.address,
              city: venue.city,
              eventCount: eventCount
            });
          }
        }

        // Sort by event count (descending) and limit results
        const sortedVenues = venuesWithEventCounts
          .sort((a, b) => b.eventCount - a.eventCount)
          .slice(0, limit);

        setVenues(sortedVenues);

      } catch (err) {
        console.error('Error in fetchVenuesWithEvents:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchVenuesWithEvents();
  }, [limit]);

  return { venues, loading, error };
};
