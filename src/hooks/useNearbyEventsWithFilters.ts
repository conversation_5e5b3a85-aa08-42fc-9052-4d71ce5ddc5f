import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useUserCoordinates } from './useUserCoordinates';
import { useUserSearchRadius } from './useUserSearchRadius';
import { useAuth } from '@/context/AuthContext';
import { Event } from './useEvents';
import { useToast } from './use-toast';

// Helper function to process events
const processEvents = (data: any[]): Event[] => {
  return data.map(event => {
    // Format location using venue data if available
    let location = '';
    if (event.venue_name) {
      // For events from the stored procedure, venue fields are flattened
      location = `${event.venue_name}, ${event.venue_city || ''}, ${event.venue_state || ''}`.replace(/^, /, '').replace(/, $/, '');
    } else if (event.venue && typeof event.venue === 'object' && !('error' in event.venue)) {
      // For events from regular queries, venue is a nested object
      location = `${event.venue.name}, ${event.venue.city || ''}, ${event.venue.state || ''}`.replace(/^, /, '').replace(/, $/, '');
    }

    // Create a properly structured category object
    const category = {
      id: event.category_id,
      name: event.category_name || 'Other',
      color: event.category_color || '#888888',
      text_color: event.category_text_color || '#FFFFFF',
      icon: event.category_icon || null
    };

    // Create a properly structured venue object
    const venue = {
      id: event.venue_id,
      name: event.venue_name || '',
      address: event.venue_address || '',
      city: event.venue_city || '',
      state: event.venue_state || '',
      zip_code: event.venue_zip_code || '',
      locality: event.venue_locality || null,
      place_id: event.venue_place_id || null,
      priority: event.venue_priority || 0
    };

    // Parse registrations if needed
    let registrations = [];
    if (event.registrations) {
      if (typeof event.registrations === 'string') {
        try {
          registrations = JSON.parse(event.registrations);
        } catch (e) {
          console.error('Error parsing registrations:', e);
        }
      } else if (Array.isArray(event.registrations)) {
        registrations = event.registrations;
      }
    }

    return {
      ...event,
      location,
      category,
      venue,
      registrations
    } as Event;
  });
};

export interface EventsQueryParams {
  search?: string;
  categoryId?: string;
  venueId?: string;
  dateStart?: string;
  dateEnd?: string;
  page?: number;
  limit?: number;
  includePastEvents?: boolean;
}

export interface PaginatedEvents {
  data: Event[];
  count: number;
  hasMore: boolean;
}

export function useNearbyEventsWithFilters() {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const userCoordinates = useUserCoordinates();
  const { searchRadius } = useUserSearchRadius();
  const { user } = useAuth();
  const { toast } = useToast();

  // Use a ref to track the current request to prevent race conditions
  const currentRequestRef = useRef<string | null>(null);

  const fetchPaginatedEvents = useCallback(async (params: EventsQueryParams): Promise<PaginatedEvents> => {
    // Generate a unique ID for this request
    const requestId = Date.now().toString();
    currentRequestRef.current = requestId;

    try {
      console.log('useNearbyEventsWithFilters.fetchPaginatedEvents called with params:', params);
      console.log('User coordinates:', userCoordinates);
      console.log('Search radius:', searchRadius);

      // If no coordinates, fall back to regular query
      if (!userCoordinates || !userCoordinates.latitude || !userCoordinates.longitude) {
        console.log('No coordinates available, fetching events without location filtering');

        let query = supabase
          .from('events')
          .select(`
            *,
            category: event_categories (*),
            venue: event_venues (*),
            registrations: registrations (quantity)
          `);

        // Apply filters
        if (params.search) {
          query = query.or(
            `title.ilike.%${params.search}%,` +
            `description.ilike.%${params.search}%,` +
            `tags.cs.{${params.search}}`
          );
        }

        if (params.categoryId) {
          query = query.eq('category_id', params.categoryId);
        }

        if (params.venueId) {
          query = query.eq('venue_id', params.venueId);
        }

        if (params.dateStart) {
          query = query.gte('start_date', params.dateStart);
        }

        if (params.dateEnd) {
          query = query.lte('start_date', params.dateEnd);
        }

        // Only show approved events to non-creators, or the user's own pending events
        if (!user?.id) {
          // Non-authenticated users only see approved events
          query = query.eq('approval_status', 'approved');
        } else {
          // Authenticated users see approved events and their own pending events
          query = query.or(`approval_status.eq.approved,and(approval_status.eq.pending,organizer_id.eq.${user.id})`);
        }

        // Handle past events differently if requested
        if (params.includePastEvents) {
          const now = new Date().toISOString();
          query = query.lt('start_date', now);
          query = query.order('start_date', { ascending: false }); // Most recent past events first
        } else {
          const now = new Date().toISOString();
          query = query.gte('start_date', now);
          query = query.order('start_date', { ascending: true }); // Soonest upcoming events first
        }

        // Get total count first
        const countQuery = supabase
          .from('events')
          .select('id', { count: 'exact', head: true });

        // Apply the same filters to the count query
        if (params.search) {
          countQuery.or(
            `title.ilike.%${params.search}%,` +
            `description.ilike.%${params.search}%,` +
            `tags.cs.{${params.search}}`
          );
        }

        if (params.categoryId) {
          countQuery.eq('category_id', params.categoryId);
        }

        if (params.venueId) {
          countQuery.eq('venue_id', params.venueId);
        }

        if (params.dateStart) {
          countQuery.gte('start_date', params.dateStart);
        }

        if (params.dateEnd) {
          countQuery.lte('start_date', params.dateEnd);
        }

        if (params.includePastEvents) {
          const now = new Date().toISOString();
          countQuery.lt('start_date', now);
        } else {
          const now = new Date().toISOString();
          countQuery.gte('start_date', now);
        }

        if (!user?.id) {
          countQuery.eq('approval_status', 'approved');
        } else {
          countQuery.or(`approval_status.eq.approved,and(approval_status.eq.pending,organizer_id.eq.${user.id})`);
        }

        const { count: totalCount, error: countError } = await countQuery;

        if (countError) {
          throw countError;
        }

        // Apply pagination
        const pageSize = params.limit || 10;
        const from = ((params.page || 1) - 1) * pageSize;
        const to = from + pageSize - 1;

        query = query.range(from, to);

        const { data, error } = await query;

        if (error) {
          throw error;
        }

        // Check if this is still the current request
        if (currentRequestRef.current !== requestId) {
          console.log('Ignoring stale response from fallback query');
          // Return empty result for stale requests
          return {
            data: [],
            count: 0,
            hasMore: false
          };
        }

        // Process the events
        const processedEvents = processEvents(data || []);

        // Calculate if there are more pages
        const hasMore = from + processedEvents.length < (totalCount || 0);

        // Return the paginated events result
        return {
          data: processedEvents,
          count: totalCount || 0,
          hasMore
        };
      }

      // If we have coordinates, use the stored procedure
      console.log(`Fetching events within ${searchRadius}km of user location with filters:`, params);

      // Calculate offset for pagination
      const pageSize = params.limit || 10;
      const offset = ((params.page || 1) - 1) * pageSize;

      // Use the stored procedure with user coordinates and search radius
      const rpcParams = {
        search_lat: userCoordinates.latitude,
        search_lng: userCoordinates.longitude,
        radius_km: searchRadius,
        date_start: params.dateStart || null,
        date_end: params.dateEnd || null,
        filter_category_id: params.categoryId || null,
        search_term: params.search || null,
        include_past_events: params.includePastEvents || false,
        user_id: user?.id || null,
        result_limit: pageSize,
        result_offset: offset
      };

      console.log('Calling find_events_within_radius with params:', rpcParams);

      const { data, error } = await supabase.rpc('find_events_within_radius', rpcParams);

      if (error) {
        console.error('Error with stored procedure, falling back to regular query:', error);
        throw error;
      }

      // Check if this is still the current request
      if (currentRequestRef.current !== requestId) {
        console.log('Ignoring stale response from stored procedure');
        // Return empty result for stale requests
        return {
          data: [],
          count: 0,
          hasMore: false
        };
      }

      // Process the events from the stored procedure
      const processedEvents = processEvents(data || []);

      // For the stored procedure, we need to make a separate call to get the total count
      // This is a limitation of the current implementation
      const countParams = {
        search_lat: userCoordinates.latitude,
        search_lng: userCoordinates.longitude,
        radius_km: searchRadius,
        date_start: params.dateStart || null,
        date_end: params.dateEnd || null,
        filter_category_id: params.categoryId || null,
        search_term: params.search || null,
        include_past_events: params.includePastEvents || false,
        user_id: user?.id || null,
        result_limit: null, // No limit to get total count
        result_offset: 0
      };

      console.log('Calling find_events_within_radius for count with params:', countParams);

      const { data: countData, error: countError } = await supabase.rpc('find_events_within_radius', countParams);

      if (countError) {
        throw countError;
      }

      // Check if this is still the current request
      if (currentRequestRef.current !== requestId) {
        console.log('Ignoring stale response from count query');
        // Return empty result for stale requests
        return {
          data: [],
          count: 0,
          hasMore: false
        };
      }

      const totalCount = countData ? countData.length : 0;
      const hasMore = offset + processedEvents.length < totalCount;

      console.log(`Found ${processedEvents.length} events within ${searchRadius}km radius (total: ${totalCount})`);

      return {
        data: processedEvents,
        count: totalCount,
        hasMore
      };
    } catch (err) {
      console.error('Error fetching paginated events:', err);
      toast({
        title: 'Error',
        description: 'Failed to load events. Please try again later.',
        variant: 'destructive',
      });

      // Return empty result on error
      return {
        data: [],
        count: 0,
        hasMore: false
      };
    }
  }, [userCoordinates, searchRadius, user, toast]);

  return { fetchPaginatedEvents };
}
