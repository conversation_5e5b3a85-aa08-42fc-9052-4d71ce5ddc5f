import { useState, useEffect, useCallback } from 'react';
import { useUserLocation } from './useUserLocation';

export interface LocationGateState {
  /**
   * Whether the location gate is open (blocking content)
   */
  isOpen: boolean;

  /**
   * Whether the location is being set
   */
  isSettingLocation: boolean;

  /**
   * Error message if location setting failed
   */
  error: string | null;
}

/**
 * Hook to manage location gate functionality
 * Blocks access to content until user has a valid location set
 */
export const useLocationGate = () => {
  const {
    coordinates,
    loading: locationLoading,
    error: locationError,
    requestLocation,
    setLocationFromPincode,
    confirmPincodeSelection,
    clearPincodeOptions,
    pincodeLocalityOptions,
    pendingPincode,
    isLoadingOptions,
    isConfirmingSelection,
    permissionDenied
  } = useUserLocation();

  // State to track if the gate should be shown
  const [state, setState] = useState<LocationGateState>({
    isOpen: true, // Start with gate open
    isSettingLocation: false,
    error: null
  });

  // Check if we have valid coordinates
  const hasValidLocation = !!coordinates &&
    typeof coordinates.lat === 'number' &&
    typeof coordinates.lng === 'number';

  // Track if location was just set to trigger page refresh
  const [locationJustSet, setLocationJustSet] = useState(false);

  // Update gate state when location changes
  useEffect(() => {
    // If we're still loading, don't change gate state yet
    if (locationLoading) return;

    // If we have valid coordinates, close the gate
    if (hasValidLocation) {
      // Check if this is a new location being set
      if (locationJustSet) {
        // Refresh the page to ensure all components load with the new location
        // Use replace and add a scroll parameter to ensure page loads at the top
        window.location.replace(window.location.pathname + window.location.search);
        return;
      }

      setState(prev => ({
        ...prev,
        isOpen: false,
        error: null
      }));
    } else {
      // If location failed or was denied, keep gate open with error
      setState(prev => ({
        ...prev,
        isOpen: true,
        error: locationError instanceof Error
          ? locationError.message
          : locationError
            ? String(locationError)
            : null
      }));
    }
  }, [coordinates, locationLoading, locationError, hasValidLocation, locationJustSet]);

  // Function to request browser geolocation
  const handleRequestLocation = useCallback(async () => {
    setState(prev => ({ ...prev, isSettingLocation: true, error: null }));
    try {
      await requestLocation();
      // Mark that location was just set to trigger refresh
      setLocationJustSet(true);
      // The effect will handle closing the gate if successful
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : String(error)
      }));
    } finally {
      setState(prev => ({ ...prev, isSettingLocation: false }));
    }
  }, [requestLocation]);

  // Function to set location from pincode
  const handleSetLocationFromPincode = useCallback(async (pincode: string) => {
    setState(prev => ({ ...prev, isSettingLocation: true, error: null }));
    try {
      const result = await setLocationFromPincode(pincode);

      if (!result.success) {
        setState(prev => ({
          ...prev,
          error: result.error || 'Failed to set location from pincode'
        }));
      } else if (!result.requiresConfirmation) {
        // Mark that location was just set to trigger refresh
        setLocationJustSet(true);
      }
      // The effect will handle closing the gate if successful
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : String(error)
      }));
    } finally {
      setState(prev => ({ ...prev, isSettingLocation: false }));
    }
  }, [setLocationFromPincode]);

  // Function to confirm pincode locality selection
  const handleConfirmPincodeSelection = useCallback(async (locality: string) => {
    setState(prev => ({ ...prev, isSettingLocation: true, error: null }));
    try {
      const success = await confirmPincodeSelection(locality);

      if (!success) {
        setState(prev => ({
          ...prev,
          error: 'Failed to confirm location selection'
        }));
      } else {
        // Mark that location was just set to trigger refresh
        setLocationJustSet(true);
      }
      // The effect will handle closing the gate if successful
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : String(error)
      }));
    } finally {
      setState(prev => ({ ...prev, isSettingLocation: false }));
    }
  }, [confirmPincodeSelection]);

  // Function to manually open the gate (if needed)
  const openGate = useCallback(() => {
    setState(prev => ({ ...prev, isOpen: true }));
  }, []);

  // Function to manually close the gate (use with caution)
  const closeGate = useCallback(() => {
    setState(prev => ({ ...prev, isOpen: false }));
  }, []);

  return {
    // State
    isGateOpen: state.isOpen,
    isSettingLocation: state.isSettingLocation || locationLoading || isLoadingOptions || isConfirmingSelection,
    error: state.error || (locationError instanceof Error ? locationError.message : locationError ? String(locationError) : null),
    permissionDenied,

    // Location data
    coordinates,
    pincodeLocalityOptions,
    pendingPincode,

    // Actions
    requestLocation: handleRequestLocation,
    setLocationFromPincode: handleSetLocationFromPincode,
    confirmPincodeSelection: handleConfirmPincodeSelection,
    clearPincodeOptions,
    openGate,
    closeGate
  };
};
