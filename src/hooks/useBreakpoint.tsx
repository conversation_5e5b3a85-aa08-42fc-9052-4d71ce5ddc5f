import { useState, useEffect } from 'react';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '../../tailwind.config.js'; // Adjust path as needed

const fullConfig = resolveConfig(tailwindConfig);

const getBreakpointValue = (key: string): number => {
  const screens = fullConfig.theme?.screens as Record<string, string> | undefined;
  if (screens && screens[key]) {
    return parseInt(screens[key], 10);
  }
  return 0;
};

export const useBreakpoint = (breakpointKey: string): boolean => {
  const [matches, setMatches] = useState<boolean | undefined>(undefined);

  useEffect(() => {
    const breakpointValue = getBreakpointValue(breakpointKey);
    if (breakpointValue === 0) {
      console.warn(`[useBreakpoint] Breakpoint key "${breakpointKey}" not found or invalid.`);
      setMatches(false); // Default to false if breakpoint is not found
      return;
    }

    const mediaQuery = window.matchMedia(`(min-width: ${breakpointValue}px)`);

    const handleChange = () => {
      setMatches(mediaQuery.matches);
    };

    // Initial check
    handleChange();

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [breakpointKey]);

  return matches === undefined ? false : matches; // Return false during SSR or initial undefined state
};
