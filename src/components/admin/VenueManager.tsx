import React, { useState, useEffect, useCallback } from 'react';
import { Edit, Loader2, Plus, Search, Trash2, ArrowUp, ArrowDown } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { EventVenue } from '@/types/venue';
import { useToast } from '@/hooks/use-toast';
import { useDebounce } from '@/hooks/useDebounce';
import PlacesAutocomplete from '@/components/common/PlacesAutocomplete';
import { PlaceDetails } from '@/utils/googlePlacesLoader';
import { formatDetailedAddress, extractLocality } from '@/utils/addressFormatter';

const VenueManager: React.FC = () => {
  const { toast } = useToast();
  const [venues, setVenues] = useState<EventVenue[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingVenue, setEditingVenue] = useState<EventVenue | null>(null);
  const [venueToDelete, setVenueToDelete] = useState<EventVenue | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm] = useDebounce(searchTerm, 500);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    address: '',
    city: '',
    state: '',
    zip_code: '',
    locality: '',
    place_id: '',
    priority: 0,
    location: {
      latitude: 0,
      longitude: 0
    }
  });

  // Track if a location has been selected from Google Places
  const [locationSelected, setLocationSelected] = useState(false);

  const fetchVenues = useCallback(async () => {
    try {
      setLoading(true);

      let query = supabase
        .from('event_venues')
        .select('*')
        .order('priority', { ascending: false });

      if (debouncedSearchTerm) {
        query = query.ilike('name', `%${debouncedSearchTerm}%`);
      }

      // Limit to 50 for performance
      query = query.limit(50);

      const { data, error } = await query;

      if (error) throw error;

      setVenues(data || []);
    } catch (err) {
      console.error('Error fetching venues:', err);
      toast({
        title: 'Error',
        description: 'Failed to load venues. Please try again later.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [debouncedSearchTerm, toast]);

  useEffect(() => {
    fetchVenues();
  }, [debouncedSearchTerm, fetchVenues]);

  const resetForm = () => {
    setFormData({
      name: '',
      address: '',
      city: '',
      state: '',
      zip_code: '',
      locality: '',
      place_id: '',
      priority: 0,
      location: {
        latitude: 0,
        longitude: 0
      }
    });
    setLocationSelected(false);
    setEditingVenue(null);
  };

  const handleCreateClick = () => {
    resetForm();
    setIsDialogOpen(true);
  };

  const handleEditClick = (venue: EventVenue) => {
    setEditingVenue(venue);
    setFormData({
      name: venue.name,
      address: venue.address,
      city: venue.city,
      state: venue.state,
      zip_code: venue.zip_code || '',
      locality: venue.locality || '',
      place_id: venue.place_id || '',
      priority: venue.priority || 0,
      location: venue.location || {
        latitude: 0,
        longitude: 0
      }
    });
    // If the venue has location data, consider it as having a selected location
    setLocationSelected(venue.location && venue.location.latitude !== 0 && venue.location.longitude !== 0);
    setIsDialogOpen(true);
  };

  const handleDeleteClick = (venue: EventVenue) => {
    setVenueToDelete(venue);
    setIsDeleteDialogOpen(true);
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if location has been selected
    if (!locationSelected) {
      toast({
        title: "Location required",
        description: "Please select a location from the suggestions first",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const venueData = {
        name: formData.name,
        address: formData.address,
        city: formData.city,
        state: formData.state,
        zip_code: formData.zip_code || null,
        locality: formData.locality || null,
        place_id: formData.place_id || null,
        priority: formData.priority || 0,
        location: formData.location.latitude !== 0 && formData.location.longitude !== 0 ?
          `POINT(${formData.location.longitude} ${formData.location.latitude})` : null
      };

      if (editingVenue) {
        const { data, error } = await supabase
          .from('event_venues')
          .update(venueData)
          .eq('id', editingVenue.id)
          .select()
          .single();

        if (error) throw error;

        setVenues(prev =>
          prev.map(venue => venue.id === editingVenue.id ? data : venue)
        );

        toast({
          title: 'Venue updated',
          description: 'The venue has been updated successfully.',
        });
      } else {
        const { data, error } = await supabase
          .from('event_venues')
          .insert(venueData)
          .select()
          .single();

        if (error) throw error;

        setVenues(prev => [...prev, data]);

        toast({
          title: 'Venue created',
          description: 'The new venue has been created successfully.',
        });
      }

      setIsDialogOpen(false);
      resetForm();
    } catch (error) {
      console.error('Error saving venue:', error);
      toast({
        title: 'Error',
        description: 'There was an error saving the venue. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleConfirmDelete = async () => {
    if (!venueToDelete) return;

    setIsSubmitting(true);

    try {
      const { error } = await supabase
        .from('event_venues')
        .delete()
        .eq('id', venueToDelete.id);

      if (error) throw error;

      setVenues(prev => prev.filter(venue => venue.id !== venueToDelete.id));

      toast({
        title: 'Venue deleted',
        description: 'The venue has been deleted successfully.',
      });

      setIsDeleteDialogOpen(false);
      setVenueToDelete(null);
    } catch (error) {
      console.error('Error deleting venue:', error);
      toast({
        title: 'Error',
        description: 'There was an error deleting the venue. It may be in use by existing events.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePriorityChange = async (venue: EventVenue, increase: boolean) => {
    try {
      const newPriority = increase ? venue.priority + 1 : venue.priority - 1;

      if (newPriority < 0) return; // Don't allow negative priorities

      const { data, error } = await supabase
        .from('event_venues')
        .update({ priority: newPriority })
        .eq('id', venue.id)
        .select()
        .single();

      if (error) throw error;

      setVenues(prev =>
        prev.map(v => v.id === venue.id ? data : v)
      );

      // Refresh list to show correct ordering
      fetchVenues();

    } catch (error) {
      console.error('Error updating venue priority:', error);
      toast({
        title: 'Error',
        description: 'There was an error updating the venue priority.',
        variant: 'destructive',
      });
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Event Venues</CardTitle>
          <CardDescription>Manage the venues available for events</CardDescription>
        </div>
        <Button onClick={handleCreateClick} className="gap-1">
          <Plus className="h-4 w-4" />
          Add Venue
        </Button>
      </CardHeader>
      <CardContent>
        <div className="flex items-center mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search venues..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-primary" />
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Address</TableHead>
                  <TableHead>City</TableHead>
                  <TableHead>State</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {venues.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      {searchTerm ? 'No venues match your search' : 'No venues found'}
                    </TableCell>
                  </TableRow>
                ) : (
                  venues.map((venue) => (
                    <TableRow key={venue.id}>
                      <TableCell className="font-medium">{venue.name}</TableCell>
                      <TableCell className="max-w-md truncate">{venue.address}</TableCell>
                      <TableCell>{venue.city}</TableCell>
                      <TableCell>{venue.state}</TableCell>
                      <TableCell>{venue.priority}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handlePriorityChange(venue, true)}
                            className="hover:text-blue-primary"
                            title="Increase priority"
                          >
                            <ArrowUp className="h-4 w-4" />
                            <span className="sr-only">Increase Priority</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handlePriorityChange(venue, false)}
                            className="hover:text-blue-primary"
                            title="Decrease priority"
                            disabled={venue.priority <= 0}
                          >
                            <ArrowDown className="h-4 w-4" />
                            <span className="sr-only">Decrease Priority</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditClick(venue)}
                            className="hover:text-blue-primary"
                          >
                            <Edit className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteClick(venue)}
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>

      {/* Venue Form Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <form onSubmit={handleFormSubmit}>
            <DialogHeader>
              <DialogTitle>{editingVenue ? 'Edit Venue' : 'Create New Venue'}</DialogTitle>
              <DialogDescription>
                {editingVenue
                  ? 'Update the details for this event venue'
                  : 'Fill in the details for the new event venue'}
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label>Search for a venue</Label>
                <div className="text-sm text-muted-foreground mb-2">
                  Start by selecting a location from the suggestions. This is required to ensure accurate positioning.
                </div>
                <PlacesAutocomplete
                  initialValue=""
                  placeholder="Search for a venue"
                  key={isDialogOpen ? 'open' : 'closed'} // Force re-render when dialog opens/closes
                  onPlaceSelect={(placeDetails: PlaceDetails) => {
                    // Set location as selected
                    setLocationSelected(true);
                    // Extract address components
                    let city = '';
                    let state = '';
                    let zipCode = '';

                    placeDetails.addressComponents.forEach(component => {
                      if (component.types.includes('locality')) {
                        city = component.longText;
                      } else if (component.types.includes('administrative_area_level_1')) {
                        state = component.longText;
                      } else if (component.types.includes('postal_code')) {
                        zipCode = component.longText;
                      }
                    });

                    // Get the detailed address using our helper function
                    const detailedAddress = formatDetailedAddress(placeDetails);
                    console.log('Formatted detailed address:', detailedAddress);

                    // Extract the locality (sublocality_level_1)
                    const locality = extractLocality(placeDetails);
                    console.log('Extracted locality:', locality);

                    // Get the place ID
                    const placeId = placeDetails.id;
                    console.log('Place ID:', placeId);

                    // Update form state
                    setFormData({
                      ...formData,
                      name: placeDetails.name,
                      address: detailedAddress,
                      locality: locality,
                      place_id: placeId,
                      city: city,
                      state: state,
                      zip_code: zipCode,
                      location: {
                        latitude: placeDetails.location.latitude,
                        longitude: placeDetails.location.longitude
                      }
                    });

                    // Force update the form fields
                    setTimeout(() => {
                      // Update the name input
                      const nameInput = document.getElementById('name') as HTMLInputElement;
                      if (nameInput) nameInput.value = placeDetails.name;

                      // Update the address input
                      const addressInput = document.getElementById('address') as HTMLInputElement;
                      if (addressInput) addressInput.value = detailedAddress;

                      // Update the locality input
                      const localityInput = document.getElementById('locality') as HTMLInputElement;
                      if (localityInput) localityInput.value = locality || '';

                      // Update the city input
                      const cityInput = document.getElementById('city') as HTMLInputElement;
                      if (cityInput) cityInput.value = city;

                      // Update the state input
                      const stateInput = document.getElementById('state') as HTMLInputElement;
                      if (stateInput) stateInput.value = state;

                      // Update the zip code input
                      const zipInput = document.getElementById('zip_code') as HTMLInputElement;
                      if (zipInput) zipInput.value = zipCode;
                    }, 100);
                  }}
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="name">Venue Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  placeholder="e.g. City Convention Center"
                  disabled={!locationSelected}
                  required
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="address">Address</Label>
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => setFormData({...formData, address: e.target.value})}
                  placeholder="Street address"
                  disabled={!locationSelected}
                  required
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="locality">Locality/Neighborhood</Label>
                <Input
                  id="locality"
                  value={formData.locality}
                  onChange={(e) => setFormData({...formData, locality: e.target.value})}
                  placeholder="Locality or Neighborhood"
                  disabled={!locationSelected}
                />
              </div>

              {/* Hidden field for place_id */}
              <input type="hidden" id="place_id" value={formData.place_id} />

              <div className="grid grid-cols-3 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    value={formData.city}
                    onChange={(e) => setFormData({...formData, city: e.target.value})}
                    placeholder="City"
                    disabled={!locationSelected}
                    required
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="state">State</Label>
                  <Input
                    id="state"
                    value={formData.state}
                    onChange={(e) => setFormData({...formData, state: e.target.value})}
                    placeholder="State"
                    disabled={!locationSelected}
                    required
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="zip_code">Zip Code</Label>
                  <Input
                    id="zip_code"
                    value={formData.zip_code}
                    onChange={(e) => setFormData({...formData, zip_code: e.target.value})}
                    placeholder="Zip/Postal Code"
                    disabled={!locationSelected}
                  />
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="priority">Priority (higher numbers appear first)</Label>
                <Input
                  id="priority"
                  type="number"
                  min="0"
                  value={formData.priority}
                  onChange={(e) => setFormData({...formData, priority: parseInt(e.target.value) || 0})}
                  placeholder="Priority (0 = lowest)"
                />
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting || !locationSelected}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>Save</>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the venue "{venueToDelete?.name}"?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button type="button" variant="destructive" onClick={handleConfirmDelete} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>Delete</>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default VenueManager;
