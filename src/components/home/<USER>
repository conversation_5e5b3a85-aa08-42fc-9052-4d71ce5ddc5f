import React from 'react';
import { Building2, Calendar, MapPin } from 'lucide-react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

interface OrganizationCardProps {
  id: string;
  name: string;
  description?: string;
  logo_url?: string;
  city?: string;
  eventCount: number;
  index: number;
}

const OrganizationCard: React.FC<OrganizationCardProps> = ({
  id,
  name,
  description,
  logo_url,
  city,
  eventCount,
  index
}) => {
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    e.currentTarget.src = '/placeholder-event.jpg';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.05 }}
      className="group w-full h-full"
    >
      <Link
        to={`/events?organization=${id}`}
        className="block w-full h-full rounded-2xl overflow-hidden bg-white shadow-smooth border border-border hover:shadow-lg transition-all duration-300 group-hover:scale-[1.02]"
      >
        {/* Organization Image/Logo */}
        <div className="relative h-48 w-full overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/30 z-10" />
          {logo_url ? (
            <img
              src={logo_url}
              alt={name}
              className="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-105"
              onError={handleImageError}
              style={{ minHeight: '192px', maxHeight: '192px' }}
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center">
              <Building2 size={48} className="text-blue-primary/60" />
            </div>
          )}
          
          {/* Event Count Badge */}
          <div className="absolute top-3 right-3 z-20">
            <div className="bg-blue-primary/90 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-xs font-medium flex items-center gap-1">
              <Calendar size={12} />
              {eventCount} {eventCount === 1 ? 'Event' : 'Events'}
            </div>
          </div>
        </div>

        {/* Organization Info */}
        <div className="p-4 sm:p-5">
          <div className="mb-3">
            <h3 className="text-lg font-semibold text-foreground mb-2 line-clamp-2 group-hover:text-blue-primary transition-colors duration-200">
              {name}
            </h3>
            
            {description && (
              <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                {description}
              </p>
            )}
            
            {city && (
              <div className="flex items-start gap-1.5 text-muted-foreground text-sm">
                <MapPin size={14} className="mt-0.5 flex-shrink-0" />
                <span className="line-clamp-1">
                  {city}
                </span>
              </div>
            )}
          </div>

          {/* Action Text */}
          <div className="text-blue-primary text-sm font-medium group-hover:underline">
            View Events by this Organization →
          </div>
        </div>
      </Link>
    </motion.div>
  );
};

export default OrganizationCard;
