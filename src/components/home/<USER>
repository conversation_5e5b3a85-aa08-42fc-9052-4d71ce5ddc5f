import React, { useState } from 'react';
import { Search, Calendar, MapPin, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useCategories } from '@/hooks/useCategories';
import { useEvents } from '@/hooks/useEvents';

const Hero: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAllCategories, setShowAllCategories] = useState(false);
  const navigate = useNavigate();
  const { popularCategories, activeCategories } = useCategories();
  // Fetch top 2 approved events, prioritizing featured ones
  const { events, loading } = useEvents(2, true);

  // Split categories into featured (first 5) and remaining
  const featuredCategories = activeCategories?.slice(0, 5) || [];
  const remainingCategories = activeCategories?.slice(5) || [];

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/events?search=${encodeURIComponent(searchQuery.trim())}`);
    } else {
      // Navigate to events page even if no search term is provided
      navigate('/events');
    }
  };

  // Helper function to format date as "Today", "Tomorrow", "This Weekend", or the actual date
  const formatEventDate = (dateString: string) => {
    const eventDate = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(today.getDate() + 1);
    
    // Check if it's today
    if (eventDate.toDateString() === today.toDateString()) {
      return "Today";
    }
    
    // Check if it's tomorrow
    if (eventDate.toDateString() === tomorrow.toDateString()) {
      return "Tomorrow";
    }
    
    // Check if it's this weekend (Saturday or Sunday)
    const dayOfWeek = eventDate.getDay(); // 0 = Sunday, 6 = Saturday
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
    const diffDays = Math.floor((eventDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    // If it's this coming weekend (within the next 5 days)
    if (isWeekend && diffDays < 5) {
      return "This Weekend";
    }
    
    // Otherwise, return the formatted date
    return eventDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  return (
    <section className="relative bg-gradient-to-b from-white to-blue-50/30 pt-20 md:pt-28 lg:pt-32 pb-8 md:pb-12 lg:pb-16 overflow-hidden">
      {/* Decorative Elements */}
      <div className="absolute inset-0 z-0 opacity-40 overflow-hidden">
        <div className="absolute top-[10%] right-[5%] w-64 h-64 rounded-full bg-yellow-accent/10 animate-float" style={{ animationDelay: '0s' }}></div>
        <div className="absolute top-[30%] left-[8%] w-48 h-48 rounded-full bg-blue-primary/10 animate-float" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-[15%] right-[15%] w-56 h-56 rounded-full bg-blue-secondary/10 animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="container relative z-10 mx-auto px-4 md:px-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Hero Content */}
          <div className="space-y-8 max-w-xl">
            <div className="space-y-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <span className="inline-block px-4 py-1.5 rounded-full bg-blue-primary/10 text-blue-primary text-sm font-medium mb-4">
                  Discover The Community Near You
                </span>
              </motion.div>
              
              <motion.h1 
                className="text-4xl md:text-5xl lg:text-6xl font-bold font-raleway leading-tight"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
              >
                Find Local <span className="text-blue-primary">Events</span> That <span className="text-yellow-accent">Matter</span>
              </motion.h1>
              
              <motion.p 
                className="text-lg text-muted-foreground mt-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                Connect with families, discover new activities, and build a stronger community in your neighborhood.
              </motion.p>
            </div>

            {/* Search Box */}
            <motion.div 
              className="relative"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <form onSubmit={handleSearch} className="flex flex-col md:flex-row p-2 gap-2 bg-white rounded-2xl shadow-smooth border border-blue-primary/10">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-primary" size={20} />
                  <input
                    type="text"
                    placeholder="Search for events..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 rounded-xl bg-transparent focus:outline-none text-base"
                  />
                </div>
                <Button 
                  type="submit"
                  className="bg-blue-primary hover:bg-blue-secondary text-white rounded-xl transition-all duration-300 transform hover:scale-[1.02] py-3 px-4 md:px-6"
                >
                  Discover Events
                </Button>
              </form>
            </motion.div>

            {/* Featured Categories */}
            <motion.div 
              className="pt-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <h3 className="text-sm text-muted-foreground mb-3">Popular Categories:</h3>
              <div className="flex flex-wrap gap-2">
                {featuredCategories && featuredCategories.length > 0 ? (
                  <>
                    {featuredCategories.map((category, index) => (
                      <motion.button
                        key={category.id}
                        onClick={() => navigate(`/events?category=${category.id}`)}
                        className={`px-4 py-2 rounded-full text-sm border border-blue-primary/10 transition-all duration-300 flex items-center gap-1.5 hover:shadow-md`}
                        style={{
                          backgroundColor: category.color || '#f3f4f6',
                          color: category.text_color || '#1f2937'
                        }}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: 0.5 + (index * 0.1) }}
                      >
                        {category.icon && <span>{category.icon}</span>}
                        {category.name}
                      </motion.button>
                    ))}

                    {/* Show More/Less Button */}
                    {remainingCategories.length > 0 && (
                      <motion.button
                        onClick={() => setShowAllCategories(!showAllCategories)}
                        className="px-4 py-2 rounded-full text-sm bg-white border border-blue-primary/20 text-blue-primary hover:bg-blue-primary hover:text-white transition-all duration-300"
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: 0.5 + (featuredCategories.length * 0.1) }}
                      >
                        {showAllCategories ? 'Show Less' : `+ ${remainingCategories.length} More`}
                      </motion.button>
                    )}
                  </>
                ) : (
                  ['Families', 'Sports', 'Education', 'Culture', 'Music'].map((name, index) => (
                    <motion.button
                      key={name}
                      className="px-4 py-2 rounded-full text-sm bg-white border border-blue-primary/10 text-foreground hover:bg-blue-primary hover:text-white transition-all duration-300"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.5 + (index * 0.1) }}
                    >
                      {name}
                    </motion.button>
                  ))
                )}
              </div>

              {/* Additional Categories - Show when expanded */}
              {showAllCategories && remainingCategories.length > 0 && (
                <motion.div
                  className="flex flex-wrap gap-2 mt-3"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  {remainingCategories.map((category, index) => (
                    <motion.button
                      key={category.id}
                      onClick={() => navigate(`/events?category=${category.id}`)}
                      className={`px-4 py-2 rounded-full text-sm border border-blue-primary/10 transition-all duration-300 flex items-center gap-1.5 hover:shadow-md`}
                      style={{
                        backgroundColor: category.color || '#f3f4f6',
                        color: category.text_color || '#1f2937'
                      }}
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                    >
                      {category.icon && <span>{category.icon}</span>}
                      {category.name}
                    </motion.button>
                  ))}
                </motion.div>
              )}
            </motion.div>
          </div>

          {/* Hero Image */}
          <motion.div 
            className="relative h-full"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.7, delay: 0.3 }}
          >
            <div className="relative w-full h-[450px] rounded-2xl overflow-hidden glass-card border-2 border-white shadow-smooth-lg transform md:rotate-1">
              <img
                src="/hero-image.jpeg"
                alt="Families enjoying community event"
                className="w-full h-full object-cover object-center"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
              
              {/* Floating Event Cards */}
              <div className="absolute left-4 bottom-4 right-4 z-10 flex flex-col space-y-3">
                <motion.div 
                  className="glass-card p-4 rounded-xl w-full max-w-[280px] shadow-smooth transition-all duration-300 cursor-pointer"
                  initial={{ x: -50, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ duration: 0.6, delay: 0.7 }}
                  onClick={() => events.length > 0 && events[0] ? navigate(`/events/${events[0].id}`) : navigate('/events')}
                >
                  <div className="flex justify-between items-start mb-1">
                    <span className="inline-block px-2 py-0.5 bg-blue-primary/10 rounded-lg text-xs text-blue-primary font-medium">
                      {events.length > 0 && events[0] 
                        ? formatEventDate(events[0].start_date)
                        : "This Weekend"}
                    </span>
                    <Calendar size={16} className="text-blue-primary" />
                  </div>
                  <h3 className="font-semibold truncate">
                    {events.length > 0 && events[0] ? events[0].title : "South Delhi Kids Festival"}
                  </h3>
                  <div className="flex items-center mt-1 text-xs text-muted-foreground">
                    <MapPin size={12} className="mr-1" />
                    <span className="truncate">
                      {events.length > 0 && events[0] 
                        ? (events[0].venue?.name || "Location not specified")
                        : "Greater Kailash Park"}
                    </span>
                  </div>
                </motion.div>
                
                <motion.div 
                  className="self-end glass-card p-4 rounded-xl w-full max-w-[280px] shadow-smooth transition-all duration-300 cursor-pointer"
                  initial={{ x: 50, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ duration: 0.6, delay: 0.9 }}
                  onClick={() => events.length > 1 && events[1] ? navigate(`/events/${events[1].id}`) : navigate('/events')}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="inline-block px-2 py-0.5 bg-yellow-accent/10 rounded-lg text-xs text-yellow-800 font-medium">
                      {events.length > 1 && events[1] 
                        ? formatEventDate(events[1].start_date)
                        : "Popular"}
                    </span>
                    <Calendar size={16} className="text-blue-primary" />
                  </div>
                  <h3 className="font-semibold truncate">
                    {events.length > 1 && events[1] ? events[1].title : "Parents Networking Brunch"}
                  </h3>
                  <div className="flex items-center mt-1 text-xs text-muted-foreground">
                    <MapPin size={12} className="mr-1" />
                    <span className="truncate">
                      {events.length > 1 && events[1] 
                        ? (events[1].venue?.name || "Location not specified")
                        : "The Coffee House, Hauz Khas"}
                    </span>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
