import React, { useState, useRef } from 'react';
import { ArrowR<PERSON>, ChevronLeft, ChevronRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import EventCard from './EventCard';
import { useEvents } from '@/hooks/useEvents';
import { useBreakpoint } from '@/hooks/useBreakpoint';

interface FeaturedEventsProps {
  title?: string;
  subtitle?: string;
  searchTerm?: string;
}

const FeaturedEvents: React.FC<FeaturedEventsProps> = ({
  title = 'Discover Upcoming Events',
  subtitle = 'Find the perfect activities for you and your family near you',
  searchTerm = ''
}) => {
  const isLg = useBreakpoint('lg');
  const isMd = useBreakpoint('md');
  const [currentIndex, setCurrentIndex] = useState(0);
  const carouselRef = useRef<HTMLDivElement>(null);

  // Get more events for carousel functionality (increased from 5 to 9)
  const { events, loading } = useEvents(9, true);

  // Filter to ensure we only show approved events in featured section
  const approvedEvents = events.filter(event => event.approval_status === 'approved');

  // Create the events search URL with potential search term
  const eventsUrl = searchTerm
    ? `/events?search=${encodeURIComponent(searchTerm)}`
    : '/events';

  // Calculate how many events to show per view based on screen size
  const eventsPerView = isLg ? 3 : isMd ? 2 : 1;
  const totalItems = approvedEvents.length + (approvedEvents.length > 0 ? 1 : 0); // +1 for "Discover More" card if we have events
  const totalSlides = Math.max(1, Math.ceil(totalItems / eventsPerView));

  // For desktop, we'll show all events but in a grid-like layout
  // Remove the slicing logic that was causing only 1 event to show

  // Navigation functions with immediate response
  const goToNext = (e?: React.MouseEvent) => {
    e?.preventDefault();
    e?.stopPropagation();

    if (currentIndex < totalSlides - 1) {
      const nextIndex = currentIndex + 1;
      setCurrentIndex(nextIndex);
      // Use requestAnimationFrame for immediate visual feedback
      requestAnimationFrame(() => {
        scrollToIndex(nextIndex);
      });
    }
  };

  const goToPrevious = (e?: React.MouseEvent) => {
    e?.preventDefault();
    e?.stopPropagation();

    if (currentIndex > 0) {
      const prevIndex = currentIndex - 1;
      setCurrentIndex(prevIndex);
      // Use requestAnimationFrame for immediate visual feedback
      requestAnimationFrame(() => {
        scrollToIndex(prevIndex);
      });
    }
  };

  const scrollToIndex = (index: number) => {
    if (carouselRef.current) {
      const container = carouselRef.current;
      const containerWidth = container.clientWidth;

      // For mobile, scroll by card width + gap
      if (!isLg && !isMd) {
        const cardWidth = 320; // max-w-[320px]
        const gap = 24; // gap-6 = 1.5rem = 24px
        const scrollAmount = (cardWidth + gap) * index;
        container.scrollTo({
          left: scrollAmount,
          behavior: 'smooth'
        });
      } else {
        // For desktop/tablet, scroll by container width
        const scrollAmount = containerWidth * index;
        container.scrollTo({
          left: scrollAmount,
          behavior: 'smooth'
        });
      }
    }
  };

  return (
    <section className="py-4 md:py-8 lg:py-12">
      <div className="container mx-auto px-4 md:px-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-end mb-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-4 sm:mb-0"
          >
            <h2 className="text-3xl font-bold font-raleway mb-2">{title}</h2>
            <p className="text-muted-foreground">{subtitle}</p>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Button 
              variant="outline" 
              className="group border-blue-primary/80 text-blue-primary hover:bg-blue-primary hover:text-white"
              asChild
            >
              <Link to={eventsUrl}>
                View All Events
                <ArrowRight size={16} className="ml-2 transform group-hover:translate-x-1 transition-transform duration-200" />
              </Link>
            </Button>
          </motion.div>
        </div>
        
        {/* Carousel Container */}
        <div className="relative">
          {/* Navigation Buttons */}
          {totalSlides > 1 && (
            <>
              {/* Previous Button - Hidden when at the beginning */}
              {currentIndex > 0 && (
                <Button
                  variant="outline"
                  size="icon"
                  className="carousel-button absolute left-2 top-1/2 -translate-y-1/2 z-20 bg-white/95 hover:bg-white shadow-xl border-gray-200 transition-all duration-150 hover:scale-105 active:scale-95 cursor-pointer select-none"
                  onClick={goToPrevious}
                  onMouseDown={(e) => e.preventDefault()}
                  style={{
                    minWidth: '44px',
                    minHeight: '44px',
                    touchAction: 'manipulation'
                  }}
                >
                  <ChevronLeft className="h-5 w-5" />
                </Button>
              )}

              {/* Next Button - Hidden when at the end */}
              {currentIndex < totalSlides - 1 && (
                <Button
                  variant="outline"
                  size="icon"
                  className="carousel-button absolute right-2 top-1/2 -translate-y-1/2 z-20 bg-white/95 hover:bg-white shadow-xl border-gray-200 transition-all duration-150 hover:scale-105 active:scale-95 cursor-pointer select-none"
                  onClick={goToNext}
                  onMouseDown={(e) => e.preventDefault()}
                  style={{
                    minWidth: '44px',
                    minHeight: '44px',
                    touchAction: 'manipulation'
                  }}
                >
                  <ChevronRight className="h-5 w-5" />
                </Button>
              )}
            </>
          )}

          {/* Carousel Content */}
          <div
            ref={carouselRef}
            className="overflow-x-auto scrollbar-hide scroll-smooth touch-pan-x"
            style={{
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
              WebkitOverflowScrolling: 'touch' // Smooth scrolling on iOS
            }}
          >
            <div className="flex gap-6">
              {loading ? (
                // Skeleton loaders for loading state
                Array.from({ length: eventsPerView }).map((_, index) => (
                  <div key={index} className={`flex-shrink-0 rounded-2xl overflow-hidden bg-white shadow-smooth border border-border ${
                    isLg ? 'w-[calc((100%-3rem)/3)]' : isMd ? 'w-[calc((100%-1.5rem)/2)]' : 'w-[calc(100vw-3rem)]'
                  } ${isLg ? '' : isMd ? '' : 'max-w-[320px]'}`}>
                    <Skeleton className="h-48 w-full" />
                    <div className="p-4 sm:p-5 space-y-4">
                      <Skeleton className="h-6 w-3/4" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-2/3" />
                        <Skeleton className="h-4 w-1/2" />
                      </div>
                    </div>
                  </div>
                ))
              ) : approvedEvents.length === 0 ? (
                <div className="w-full text-center py-12">
                  <p className="text-muted-foreground">No events found. Check back soon for upcoming events!</p>
                </div>
              ) : (
                <>
                  {approvedEvents.map((event, index) => (
                    <div key={event.id} className={`flex-shrink-0 ${
                      isLg ? 'w-[calc((100%-3rem)/3)]' : isMd ? 'w-[calc((100%-1.5rem)/2)]' : 'w-[calc(100vw-3rem)]'
                    } ${isLg ? '' : isMd ? '' : 'max-w-[320px]'}`}>
                      <EventCard
                        id={event.id}
                        title={event.title}
                        date={new Date(event.start_date)}
                        location={event.venue ? event.venue.name : event.venue_name}
                        category={event.category || { name: 'Other' }}
                        imageUrl={event.image_url || '/placeholder-event.jpg'}
                        approvalStatus={event.approval_status}
                        index={index}
                        isFree={event.is_free}
                        price={event.general_admission_price}
                        isMultiDay={event.is_multi_day}
                        endDate={event.end_date ? new Date(event.end_date) : undefined}
                        registrations={event.registrations}
                        maxAttendees={event.max_attendees}
                      />
                    </div>
                  ))}
                  {/* "Discover More Events" card */}
                  {approvedEvents.length > 0 && (
                      <div className={`flex-shrink-0 ${
                        isLg ? 'w-[calc((100%-3rem)/3)]' : isMd ? 'w-[calc((100%-1.5rem)/2)]' : 'w-[calc(100vw-3rem)]'
                      } ${isLg ? '' : isMd ? '' : 'max-w-[320px]'}`}>
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: approvedEvents.length * 0.05 + 0.1 }}
                        className="group w-full h-full rounded-2xl overflow-hidden bg-gray-50 border border-dashed border-gray-300 hover:shadow-lg transition-all duration-300 flex flex-col items-center justify-center text-center p-4 sm:p-5 min-h-[300px]"
                      >
                        <div className="p-4">
                          <h3 className="text-xl font-semibold mb-2 text-gray-700">Discover More Events</h3>
                          <p className="text-muted-foreground mb-4">
                            There are many more events happening in your area.
                          </p>
                          <Button
                            variant="outline"
                            className="group border-blue-primary/80 text-blue-primary hover:bg-blue-primary hover:text-white"
                            asChild
                          >
                            <Link to="/events">
                              Click here to explore
                              <ArrowRight size={16} className="ml-2 transform group-hover:translate-x-1 transition-transform duration-200" />
                            </Link>
                          </Button>
                        </div>
                      </motion.div>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>

      </div>
    </section>
  );
};

export default FeaturedEvents;
