import React from 'react';
import { Link } from 'react-router-dom';
import { PlusCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';

interface HostEventCTAProps {
  className?: string;
}

const HostEventCTA: React.FC<HostEventCTAProps> = ({ className = '' }) => {
  const { user } = useAuth();

  return (
    <motion.section 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`py-16 ${className}`}
    >
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center p-8 md:p-12 bg-blue-50/50 rounded-2xl border border-blue-100 shadow-smooth">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold font-raleway mb-4">
              Want to host your own event?
            </h2>
            <p className="text-muted-foreground text-lg mb-8 max-w-2xl mx-auto">
              Share your event with the community and reach more people. Create memorable experiences that bring neighbors together.
            </p>

            {user ? (
              <Button
                className="bg-blue-primary hover:bg-blue-secondary text-white shadow-lg hover:shadow-xl transition-all duration-300"
                size="lg"
                asChild
              >
                <Link to="/create">
                  <PlusCircle className="mr-2" size={20} />
                  Host an Event
                </Link>
              </Button>
            ) : (
              <div className="space-y-4">
                <Button
                  variant="outline"
                  className="border-blue-primary text-blue-primary hover:bg-blue-primary hover:text-white shadow-lg hover:shadow-xl transition-all duration-300"
                  size="lg"
                  asChild
                >
                  <Link to="/auth?redirect=/create">
                    Sign in to Create Events
                  </Link>
                </Button>
                <p className="text-sm text-muted-foreground">
                  Join our community to start hosting events
                </p>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
};

export default HostEventCTA;
