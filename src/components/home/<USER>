import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Clock, Building2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

import LocationBadge from '../ui/LocationBadge';
import CategoryBadge from '../ui/CategoryBadge';
import { Badge } from '@/components/ui/badge';
import { useEventAnalytics } from '@/hooks/useAnalytics';

export interface EventCardProps {
  id: string;
  title: string;
  date: Date;
  location: string;
  category: {
    name: string;
    color?: string;
    text_color?: string;
    icon?: string | null;
  };
  imageUrl: string;
  approvalStatus?: 'pending' | 'approved' | 'rejected';
  isFeatured?: boolean;
  className?: string;
  index?: number;
  isFree?: boolean;
  price?: number;
  isMultiDay?: boolean;
  endDate?: Date;
  registrations?: { quantity: number }[];
  maxAttendees?: number | null;
  hostedByType?: 'individual' | 'organization';
  organization?: {
    id: string;
    name: string;
    logo_url?: string;
  };
}

const EventCard: React.FC<EventCardProps> = ({
  id,
  title,
  date,
  location,
  category,
  imageUrl,
  approvalStatus = 'approved',
  className,
  index = 0,
  isFree = false,
  price,
  isMultiDay = false,
  endDate,
  hostedByType = 'individual',
  organization,
  registrations = [],
  maxAttendees = null
}) => {
  const { trackEventView } = useEventAnalytics();
  // Helper function to format date as "Today", "Tomorrow", "This Weekend", or the actual date
  const formatSmartDate = (date: Date) => {
    const eventDate = new Date(date);
    const today = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(today.getDate() + 1);
    
    // Check if it's today
    if (eventDate.toDateString() === today.toDateString()) {
      return "Today";
    }
    
    // Check if it's tomorrow
    if (eventDate.toDateString() === tomorrow.toDateString()) {
      return "Tomorrow";
    }
    
    // Check if it's this weekend (Saturday or Sunday)
    const dayOfWeek = eventDate.getDay(); // 0 = Sunday, 6 = Saturday
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
    const diffDays = Math.floor((eventDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    // If it's this coming weekend (within the next 5 days)
    if (isWeekend && diffDays < 5) {
      return "This Weekend";
    }
    
    // Otherwise, return the formatted date
    return format(date, 'EEE, MMM d, yyyy');
  };

  const formattedDate = formatSmartDate(date);
  const formattedTime = format(date, 'h:mm a');
  const delayBase = 0.1;
  const delayIncrement = 0.05;
  const delay = delayBase + (index * delayIncrement);

  // Use a fallback image if the provided URL is invalid or not showing
  const fallbackImageUrl = "https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80";
  
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    e.currentTarget.src = fallbackImageUrl;
  };

  const isPending = approvalStatus === 'pending';
  const isRejected = approvalStatus === 'rejected';

  // Handle event click tracking
  const handleEventClick = () => {
    // Debug: Log the values being passed to tracking
    console.log('🔍 EventCard tracking data:', {
      id: id,
      title: title,
      titleType: typeof title,
      titleLength: title?.length,
      titleTrimmed: title?.trim(),
      allProps: { id, title, date, location, category }
    });

    // Ensure we have a valid title
    const eventTitle = title?.trim() || `Event ${id}` || 'Untitled Event';

    console.log('🏷️ Final tracking values:', {
      eventId: id,
      eventTitle: eventTitle
    });

    // Track event view with combined title and ID
    trackEventView(id, eventTitle);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      whileHover={{ y: -5 }}
      className={cn(
        "group w-full rounded-2xl overflow-hidden bg-white shadow-smooth border border-border hover:shadow-smooth-lg transition-all duration-300",
        isRejected && "opacity-60",
        className
      )}
    >
      <Link to={`/events/${id}`} className="block h-full" onClick={handleEventClick}>
        <div className="relative h-48 w-full overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/30 z-10" />
          <img
            src={imageUrl}
            alt={title}
            className="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-105"
            onError={handleImageError}
            style={{ minHeight: '192px', maxHeight: '192px' }}
          />
          
          {/* Category */}
          <div className="absolute top-4 left-4 z-20">
            <CategoryBadge 
              category={category} 
              className="!bg-opacity-90 shadow-md" 
            />
            {process.env.NODE_ENV === 'development' && (
              <div className="hidden">
                {/* This will be in the DOM but not visible, for debugging */}
                <pre>{JSON.stringify(category, null, 2)}</pre>
              </div>
            )}
          </div>
          
          {/* Approval Status - Updated styling for better visibility */}
          {isPending && (
            <div className="absolute top-4 right-14 z-20">
              <Badge variant="outline" className="bg-yellow-500/90 text-white border-yellow-600 font-semibold shadow-sm">
                Pending
              </Badge>
            </div>
          )}
          
          {isRejected && (
            <div className="absolute top-4 right-14 z-20">
              <Badge variant="destructive" className="font-semibold shadow-sm">
                Rejected
              </Badge>
            </div>
          )}
          
          {/* Registration Count Pill */}
          {(() => {
            // Calculate total tickets booked
            const totalTicketsBooked = registrations?.reduce((sum, reg) => sum + (reg.quantity || 0), 0) || 0;
            
            // Case 1: Limited seats event (max_attendees defined)
            if (maxAttendees && maxAttendees > 0) {
              const seatsLeft = maxAttendees - totalTicketsBooked;
              return (
                <div className="absolute top-4 right-4 z-20">
                  <Badge variant="outline" className="bg-blue-primary/90 text-white border-blue-primary font-medium shadow-sm">
                    {seatsLeft} seats left
                  </Badge>
                </div>
              );
            }
            
            // Case 2: No max_attendees but has registrations
            if (totalTicketsBooked > 0) {
              return (
                <div className="absolute top-4 right-4 z-20">
                  <Badge variant="outline" className="bg-green-600/90 text-white border-green-700 font-medium shadow-sm">
                    {totalTicketsBooked} registered
                  </Badge>
                </div>
              );
            }
            
            // Case 3: No max_attendees and no registrations - don't show anything
            return null;
          })()}
          
          {/* Location Badge */}
          <div className="absolute bottom-4 left-4 z-20">
            <LocationBadge location={location} />
          </div>
        </div>
        
        <div className="p-4 sm:p-5">
          <h3 className="font-raleway font-semibold text-lg mb-2 line-clamp-2 group-hover:text-blue-primary transition-colors">
            {title}
          </h3>
          
          <div className="flex flex-col space-y-2 text-sm text-muted-foreground">
            <div className="flex items-center">
              <Calendar size={14} className="mr-2 text-blue-primary" />
              <span>{formattedDate}</span>
            </div>
            <div className="flex items-center">
              <Clock size={14} className="mr-2 text-blue-primary" />
              <span>{formattedTime}</span>
            </div>

            {/* Organization branding */}
            {hostedByType === 'organization' && organization && (
              <div className="flex items-center">
                {organization.logo_url ? (
                  <img
                    src={organization.logo_url}
                    alt={organization.name}
                    className="w-3.5 h-3.5 mr-2 rounded object-cover"
                  />
                ) : (
                  <Building2 size={14} className="mr-2 text-blue-primary" />
                )}
                <span className="text-xs">by {organization.name}</span>
              </div>
            )}
          </div>
        </div>
      </Link>
    </motion.div>
  );
};

export default EventCard;
