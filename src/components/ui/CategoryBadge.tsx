import React from 'react';
import { cn } from '@/lib/utils';

interface CategoryBadgeProps {
  category: {
    name: string;
    color?: string;
    text_color?: string;
    icon?: string | null;
    id?: string;
  };
  className?: string;
}

const CategoryBadge: React.FC<CategoryBadgeProps> = ({ category, className }) => {
  // Extract category data handling different structures
  let name = '';
  let colorClass = '';
  let textColorClass = '';
  let icon = null;

  if (!category) {
    name = 'Unknown';
    colorClass = 'bg-gray-100';
    textColorClass = 'text-gray-800';
  } else if (Array.isArray(category)) {
    const firstItem = category[0] || {};
    name = firstItem.name || 'Unknown';
    colorClass = firstItem.color || 'bg-gray-100';
    textColorClass = firstItem.text_color || 'text-gray-800';
    icon = firstItem.icon;
  } else if (typeof category === 'object') {
    name = category.name || 'Unknown';
    colorClass = category.color || 'bg-gray-100';
    textColorClass = category.text_color || 'text-gray-800';
    icon = category.icon;
  }

  // Combine all classes, including the Tailwind color classes
  const badgeClasses = cn(
    "inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium transition-all gap-1.5 shadow-sm",
    colorClass, // Apply background color class
    textColorClass, // Apply text color class
    className
  );

  return (
    <span className={badgeClasses}>
      {icon && <span className="text-sm">{icon}</span>}
      {name}
    </span>
  );
};

export default CategoryBadge;
