import React from 'react';
import PlacesAutocomplete from '@/components/common/PlacesAutocomplete';
import { PlaceDetails } from '@/utils/googlePlacesLoader';
import { cn } from '@/lib/utils';

interface GooglePlacesAutocompleteProps {
  onPlaceSelect: (placeDetails: PlaceDetails) => void;
  placeholder?: string;
  className?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export const GooglePlacesAutocomplete: React.FC<GooglePlacesAutocompleteProps> = ({
  onPlaceSelect,
  placeholder = 'Search for a place',
  className,
  value = '',
  onChange
}) => {
  return (
    <PlacesAutocomplete
      onPlaceSelect={onPlaceSelect}
      placeholder={placeholder}
      className={cn(
        "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      initialValue={value}
    />
  );
};
