import React from 'react';
import { cn } from '@/lib/utils';

interface RequiredIndicatorProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const RequiredIndicator: React.FC<RequiredIndicatorProps> = ({ 
  className,
  size = 'md'
}) => {
  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm', 
    lg: 'text-base'
  };

  return (
    <span 
      className={cn(
        'text-destructive font-medium ml-1',
        sizeClasses[size],
        className
      )}
      aria-label="Required field"
    >
      *
    </span>
  );
};

export default RequiredIndicator;
