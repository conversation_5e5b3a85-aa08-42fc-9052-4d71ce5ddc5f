import React, { useState, useEffect } from 'react';
import { Search, X } from 'lucide-react';
import { Input } from './input';
import { useSearchAnalytics } from '@/hooks/useAnalytics';

interface SearchBarProps {
  onSearch: (term: string) => void;
  placeholder?: string;
  initialValue?: string;
  className?: string;
  resultCount?: number; // Optional: for tracking search result counts
}

const SearchBar: React.FC<SearchBarProps> = ({
  onSearch,
  placeholder = "Search events...",
  initialValue = "",
  className,
  resultCount
}) => {
  const [searchTerm, setSearchTerm] = useState(initialValue);
  const { trackSearchQuery } = useSearchAnalytics();

  // Update searchTerm if initialValue changes (e.g. from URL params)
  useEffect(() => {
    setSearchTerm(initialValue);
  }, [initialValue]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Track search query with analytics
    if (searchTerm.trim()) {
      trackSearchQuery(searchTerm.trim(), resultCount);
    }

    onSearch(searchTerm);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleClear = () => {
    setSearchTerm('');
    onSearch('');
  };

  return (
    <form onSubmit={handleSubmit} className={`relative w-full md:max-w-md ${className}`}>
      <div className="relative">
        <Input
          type="text"
          placeholder={placeholder}
          value={searchTerm}
          onChange={handleChange}
          className="pr-24 rounded-lg pl-10 border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
        />
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search size={18} className="text-gray-400" />
        </div>
        
        {searchTerm && (
          <button 
            type="button"
            onClick={handleClear}
            className="absolute inset-y-0 right-20 flex items-center text-gray-400 hover:text-gray-600"
            aria-label="Clear search"
          >
            <X size={16} />
          </button>
        )}
        
        <button 
          type="submit" 
          className="absolute inset-y-0 right-0 px-3 flex items-center text-blue-500 hover:text-blue-700"
          aria-label="Search"
        >
          Search
        </button>
      </div>
    </form>
  );
};

export default SearchBar;
