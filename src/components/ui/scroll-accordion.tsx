import * as React from "react";
import * as AccordionPrimitive from "@radix-ui/react-accordion";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";

const Accordion = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Root> & {
    scrollOffset?: number;
  }
>(({ scrollOffset = 100, ...props }, ref) => {
  return (
    <AccordionPrimitive.Root
      ref={ref}
      {...props}
    />
  );
});
Accordion.displayName = "Accordion";

const AccordionItem = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>
>(({ className, ...props }, ref) => (
  <AccordionPrimitive.Item
    ref={ref}
    className={cn("border-b", className)}
    {...props}
  />
));
AccordionItem.displayName = "AccordionItem";

const AccordionTrigger = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>
>(({ className, children, ...props }, ref) => {
  const triggerRef = React.useRef<HTMLButtonElement>(null);
  
  return (
    <AccordionPrimitive.Header className="flex">
      <AccordionPrimitive.Trigger
        ref={(node) => {
          // Handle both the forwarded ref and our internal ref
          if (typeof ref === 'function') ref(node);
          else if (ref) ref.current = node;
          triggerRef.current = node;
        }}
        className={cn(
          "flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",
          className
        )}
        {...props}
        onClick={(e) => {
          // Call the original onClick if it exists
          if (props.onClick) props.onClick(e);
          
          // Check if this click will open the accordion (when it's currently closed)
          const isCurrentlyClosed = triggerRef.current?.getAttribute('data-state') === 'closed';
          
          if (isCurrentlyClosed) {
            // We need to wait for the animation to complete and DOM to update
            // Radix UI accordion animation takes about 300ms
            setTimeout(() => {
              if (triggerRef.current) {
                // Get the element's position relative to the document
                const rect = triggerRef.current.getBoundingClientRect();
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const absoluteTop = rect.top + scrollTop;
                
                // Scroll directly to the position with offset
                window.scrollTo({
                  top: absoluteTop - 100, // Adjust this offset as needed for header
                  behavior: 'smooth'
                });
              }
            }, 350); // Wait a bit longer than the animation time
          }
        }}
      >
        {children}
        <ChevronDown className="h-4 w-4 shrink-0 transition-transform duration-200" />
      </AccordionPrimitive.Trigger>
    </AccordionPrimitive.Header>
  );
});
AccordionTrigger.displayName = "AccordionTrigger";

const AccordionContent = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Content
    ref={ref}
    className="overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
    {...props}
  >
    <div className={cn("pb-4 pt-0", className)}>{children}</div>
  </AccordionPrimitive.Content>
));
AccordionContent.displayName = "AccordionContent";

export { Accordion, AccordionItem, AccordionTrigger, AccordionContent };
