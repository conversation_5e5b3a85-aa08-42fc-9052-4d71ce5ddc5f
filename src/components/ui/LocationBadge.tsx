
import React from 'react';
import { MapPin } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LocationBadgeProps {
  location: string;
  className?: string;
}

const LocationBadge: React.FC<LocationBadgeProps> = ({ location, className }) => {
  return (
    <div className={cn(
      "inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-white/80 backdrop-blur-md border border-blue-primary/10 text-blue-secondary/90",
      className
    )}>
      <MapPin size={12} className="mr-1" />
      <span className="truncate max-w-[180px]">{location}</span>
    </div>
  );
};

export default LocationBadge;
