
import React from 'react';
import { Link } from 'react-router-dom';
import { Facebook, Twitter, Instagram, Linkedin, Mail, MapPin, Phone } from 'lucide-react';
import { Button } from '@/components/ui/button';

const Footer: React.FC = () => {
  return (
    <footer className="bg-gradient-to-b from-white to-muted pt-16 pb-8">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 mb-12">
          <div className="space-y-4">
            <Link to="/" className="inline-block">
              <h2 className="text-2xl font-bold font-raleway text-blue-primary">
                The<span className="text-yellow-accent mx-1 font-[Mukta] align-middle relative -top-[1px]">लोकल</span><span className="text-blue-primary">Adda</span>
              </h2>
            </Link>
            <p className="text-muted-foreground text-sm">
              Connecting local communities through engaging events and memorable experiences.
            </p>
            <div className="flex space-x-3">
              <SocialIcon icon={<Facebook size={18} />} />
              <SocialIcon icon={<Twitter size={18} />} />
              <SocialIcon icon={<Instagram size={18} />} />
              <SocialIcon icon={<Linkedin size={18} />} />
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-5 text-lg font-raleway">Discover</h3>
            <ul className="space-y-3">
              <FooterLink to="/events" label="All Events" />
              <FooterLink to="/events?category=families" label="Family Events" />
              <FooterLink to="/events?category=education" label="Education" />
              <FooterLink to="/events?category=culture" label="Arts & Culture" />
            </ul>
          </div>

          <div>
            <h3 className="font-semibold mb-5 text-lg font-raleway">Organize</h3>
            <ul className="space-y-3">
              <FooterLink to="/create" label="Create Event" />
              <FooterLink to="/dashboard" label="My Events" />
              <FooterLink to="/guidelines" label="Community Guidelines" />
              <FooterLink to="/help" label="Help Center" />
            </ul>
          </div>

          <div>
            <h3 className="font-semibold mb-5 text-lg font-raleway">Contact</h3>
            <ul className="space-y-4">
              <li className="flex items-start">
                <MapPin size={18} className="mr-2 mt-1 text-blue-primary" />
                <span className="text-sm">C.R.Park, New Delhi, India 110019</span>
              </li>
              <li className="flex items-center">
                <Mail size={18} className="mr-2 text-blue-primary" />
                <span className="text-sm"><EMAIL></span>
              </li>
              <li className="flex items-center">
                <Phone size={18} className="mr-2 text-blue-primary" />
                <span className="text-sm">+91 98765 43210</span>
              </li>
            </ul>
          </div>
        </div>

        <div className="flex flex-col md:flex-row justify-between items-center pt-8 border-t border-blue-primary/10">
          <p className="text-sm text-muted-foreground mb-4 md:mb-0">
            &copy; {new Date().getFullYear()} TheLocalAdda. All rights reserved.
          </p>
          <div className="flex flex-wrap justify-center space-x-4 text-sm">
            <Link to="/about" className="text-muted-foreground hover:text-blue-primary transition-colors duration-200">
              About
            </Link>
            <Link to="/privacy" className="text-muted-foreground hover:text-blue-primary transition-colors duration-200">
              Privacy Policy
            </Link>
            <Link to="/terms" className="text-muted-foreground hover:text-blue-primary transition-colors duration-200">
              Terms of Service
            </Link>
            <Link to="/cookies" className="text-muted-foreground hover:text-blue-primary transition-colors duration-200">
              Cookie Policy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

interface FooterLinkProps {
  to: string;
  label: string;
}

const FooterLink: React.FC<FooterLinkProps> = ({ to, label }) => {
  return (
    <li>
      <Link
        to={to}
        className="text-sm text-muted-foreground hover:text-blue-primary transition-colors duration-200"
      >
        {label}
      </Link>
    </li>
  );
};

interface SocialIconProps {
  icon: React.ReactNode;
}

const SocialIcon: React.FC<SocialIconProps> = ({ icon }) => {
  return (
    <Button
      size="icon"
      variant="ghost"
      className="h-9 w-9 rounded-full bg-blue-primary/10 hover:bg-blue-primary hover:text-white text-blue-primary transition-all duration-300"
    >
      {icon}
    </Button>
  );
};

export default Footer;
