import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { MapPin, Menu, X, User, LogOut, LogIn, Plus, Settings, ShieldCheck, PlusCircle, Search, Map, Loader2, LocateFixed, Calendar, Shield, AlertCircle, Home, Building2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { useMobile } from '@/hooks/use-mobile';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useUserLocation } from '@/hooks/useUserLocation';
import { Skeleton } from '@/components/ui/skeleton';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

const Header: React.FC = () => {
  const { isMobile } = useMobile();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const location = useLocation();
  const { user, userProfile, signOut } = useAuth();
  const avatarUrlRef = useRef<string | null>(null);
  const { 
    localityShortName, 
    loading: locationLoading, 
    requestLocation,
    permissionDenied,
    setLocationFromPincode,
    error: locationError, 
    pincodeLocalityOptions,
    pendingPincode,
    isLoadingOptions,      // Loading state for fetching locality options
    isConfirmingSelection, // Loading state for confirming selection
    confirmPincodeSelection,
    clearPincodeOptions,   // Clear pending pincode, options, and error from hook
  } = useUserLocation();

  // State for the pincode popover
  const [pincode, setPincode] = useState("");
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [popoverError, setPopoverError] = useState<string | null>(null);
  const [selectedLocality, setSelectedLocality] = useState<string | null>(null); // For locality selection

  // Store avatar URL in a ref to prevent excessive re-renders
  useEffect(() => {
    if (user?.user_metadata?.avatar_url) {
      avatarUrlRef.current = user.user_metadata.avatar_url;
    }
  }, [user]);

  // Links for the navbar - "Host Event" is handled manually in rendering
  const navLinks = [
    { title: "Home", href: "/" },
    { title: "Discover Events", href: "/events" },
    // { title: "Host Event", href: user ? "/create" : "/auth?redirect=/create" }, // Handled below
    // About link moved to footer
    // Add more links as needed
  ];

  // Authenticated user links
  const authLinks = [
    { title: "Organizations", href: "/organizations" },
  ];

  // Check if user is admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (user) {
        try {
          const { data, error } = await supabase
            .from('profiles')
            .select('is_admin')
            .eq('id', user.id)
            .single();
            
          if (error) throw error;
          setIsAdmin(data?.is_admin || false);
        } catch (error) {
          console.error('Error checking admin status:', error);
          setIsAdmin(false);
        }
      } else {
        setIsAdmin(false);
      }
    };
    
    checkAdminStatus();
  }, [user]);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
  }, [location.pathname]);

  // Add scroll event listener
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Toggle mobile menu
  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  // Determine if the current route is home
  const isHome = location.pathname === '/';

  // Get the user's initials for avatar fallback
  const getUserInitials = () => {
    if (!user || !user.user_metadata) return '?';
    
    const name = user.user_metadata.name || user.user_metadata.full_name;
    if (!name) return user.email?.substring(0, 1).toUpperCase() || '?';
    
    return name.split(' ')
      .map(part => part.charAt(0).toUpperCase())
      .join('')
      .substring(0, 2);
  };

  const handlePincodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, ''); // Allow only digits
    setPincode(value);
    if (popoverError) setPopoverError(null); // Clear error on input change
  };

  // Handle pincode form submission (Step 1)
  const handlePincodeSubmit = useCallback(async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!pincode || pincode.length !== 6) {
      setPopoverError("Please enter a valid 6-digit pincode.");
      return;
    }
    // Reset previous state
    setPopoverError(null);
    setSelectedLocality(null); // Reset selection on new submit
     
    try {
      // Call the hook function which might trigger option loading
      const result = await setLocationFromPincode(pincode);
      
      if (result.success && !result.requiresConfirmation) {
        setIsPopoverOpen(false); // Close only if confirmation is NOT needed
        setPincode("");
      } else if (!result.success) {
        // Error occurred during initial lookup
        setPopoverError(result.error || "Failed to fetch location for this pincode.");
      }
      // If result.success && result.requiresConfirmation, the popover remains open
      // and the conditional rendering will show the locality options.
    } catch (error: any) { // Catch errors from the hook call itself
      setPopoverError(error?.message || "An unexpected error occurred during pincode lookup.");
    } finally {
      // Loading is now handled by isLoadingOptions from the hook
    }
  }, [pincode, setLocationFromPincode]);

  // Handle locality selection confirmation (Step 2)
  const handleConfirmSelection = useCallback(async () => {
    if (!selectedLocality || !pendingPincode) {
      setPopoverError("Please select a locality.");
      return;
    }
    setPopoverError(null);

    try {
      const success = await confirmPincodeSelection(selectedLocality);
      if (success) {
        setIsPopoverOpen(false);
        setPincode(""); // Clear original input
        setSelectedLocality(null); // Reset selection
      } else {
        // Error should be reflected in locationError from the hook
        setPopoverError(locationError instanceof Error ? locationError.message : String(locationError) || "Failed to confirm selected locality.");
      }
    } catch (error: any) {
      setPopoverError(error?.message || "An unexpected error occurred during confirmation.");
    }
  }, [selectedLocality, pendingPincode, confirmPincodeSelection, locationError]);

  const handleUseCurrentLocation = useCallback(async () => {
    setPopoverError(null); // Clear pincode errors
    setSelectedLocality(null);
    // Maybe clear pending pincode state? Add clearPincodeOptions to hook if needed.
    // clearPincodeOptions?.(); // Assuming clearPincodeOptions exists and clears relevant state
    try {
      await requestLocation();
      setIsPopoverOpen(false); // Close popover after requesting current location
    } catch (error: any) {
      setPopoverError(error?.message || "Failed to request current location.");
    }
  }, [requestLocation]);

  const handlePopoverOpenChange = (isOpen: boolean) => {
    // Only clear state if the popover is being closed by the user action
    if (!isOpen) {
      clearPincodeOptions(); // Clear pending pincode, options, and error from hook
      setPopoverError(null); // Clear local popover error
      setSelectedLocality(null); // Reset local selection state
    }
    setIsPopoverOpen(isOpen);
  };

  const handleSetPincode = async () => {
    if (pincode.length !== 6) {
      setPopoverError("Pincode must be 6 digits."); // Revert error message for consistency
      return;
    }
    setPopoverError(null);
    setSelectedLocality(null); // Reset local selection before potentially getting new options
    try {
      // The hook now updates its internal state (pincodeLocalityOptions, locationError)
      await setLocationFromPincode(pincode);
      // No need to check options.length here, component reacts to pincodeLocalityOptions state
    } catch (error: any) {
      console.error("Error setting location from pincode:", error);
      // Error should be set in locationError state by the hook.
      // Set a local popover error as a fallback if needed.
      setPopoverError("An error occurred while fetching localities."); // Generic fallback
    } finally {
      // Loading is now handled by isLoadingOptions from the hook
    }
  };

  // Handler for the new 'Back' button in locality selection
  const handleBackToPincode = () => {
    clearPincodeOptions(); // Clear hook state (pending pincode, options, error)
    setPincode(""); // Clear local pincode input
    setPopoverError(null); // Clear local error
    setSelectedLocality(null); // Clear local selection
  };

  // Reset pincode input when popover closes without confirming
  useEffect(() => {
    if (!isPopoverOpen) {
      setPincode("");
      setSelectedLocality(null);
    }
  }, [isPopoverOpen]);

  return (
    <header 
      className={cn(
        "fixed w-full z-50 transition-all duration-300",
        isScrolled || !isHome || isMenuOpen ? "bg-background/80 backdrop-blur-md border-b shadow-sm" : "bg-transparent"
      )}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-1">
            <motion.span
              className="text-2xl md:text-3xl font-bold font-raleway leading-none"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, ease: 'easeOut' }}
            >
              <span className="text-blue-primary">The</span>
              <span className="text-yellow-accent mx-1 font-[Mukta] align-middle relative -top-[1px]">लोकल</span>
              <span className="text-blue-primary">Adda</span>
            </motion.span>
          </Link>

          {/* Desktop Navigation */}
          {!isMobile && (
            <div className="flex items-center space-x-6 ml-auto mr-6">
              {/* Location Badge */}
              <Popover open={isPopoverOpen} onOpenChange={handlePopoverOpenChange}>
                <TooltipProvider delayDuration={100}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <PopoverTrigger asChild aria-label="Change location">
                        <button
                          className={cn(
                            "flex items-center text-sm transition-colors flex-shrink-0 cursor-pointer",
                            isHome && !isScrolled ? "text-white/80 hover:text-white" : "text-muted-foreground hover:text-foreground",
                            locationLoading && "cursor-wait opacity-70",
                            permissionDenied && "text-destructive hover:text-destructive/80"
                          )}
                          disabled={locationLoading}
                          aria-label="Change location"
                        >
                          <MapPin className="h-4 w-4 mr-1 text-blue-primary" />
                          {locationLoading && !localityShortName ? (
                            <Skeleton className="h-4 w-20" />
                          ) : (
                            <span className={cn(
                              "inline-block",
                              isHome && !isScrolled && "text-foreground"
                            )}>
                              {localityShortName || 'Set Location'}
                            </span>
                          )}
                        </button>
                      </PopoverTrigger>
                    </TooltipTrigger>
                    <TooltipContent>
                      {locationLoading
                        ? "Fetching location..."
                        : permissionDenied 
                        ? "Location denied. Click to change."
                        : locationError
                        ? `Error: ${locationError}. Click to change.`
                        : `Current location: ${localityShortName || 'Unknown'}. Click to change.`
                      } 
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <PopoverContent className="w-80" align="end">
                  <div className="grid gap-4 p-4">
                    {/* Section 1: Use Current Location */} 
                    <div className="space-y-2">
                      <h4 className="font-medium leading-none text-sm text-muted-foreground">Detect Automatically</h4>
                      <Button 
                        variant="outline" 
                        className="w-full justify-start" 
                        onClick={handleUseCurrentLocation}
                        disabled={locationLoading} // Disable while loading
                      >
                        {locationLoading && !pendingPincode && !isLoadingOptions ? (
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        ) : (
                          <LocateFixed className="mr-2 h-4 w-4" />
                        )}
                        Use Current Location
                      </Button>
                      {permissionDenied && (
                        <Alert variant="destructive" className="mt-2">
                          <AlertCircle className="h-4 w-4" />
                          <AlertTitle className="text-xs">Permission Denied</AlertTitle>
                          <AlertDescription className="text-xs">
                            Location permission denied. Please enable it in browser settings.
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>

                    <Separator />

                    {/* Section 2: Pincode Entry / Locality Selection */} 
                    <div className="space-y-2">
                      <h4 className="font-medium leading-none text-sm text-muted-foreground">Or Enter Pincode</h4>

                      {/* Show Locality Selection if options available */} 
                      {pendingPincode && pincodeLocalityOptions && pincodeLocalityOptions.length > 0 ? (
                        <div className="space-y-3">
                           <p className="text-sm text-muted-foreground">Select your locality for pincode {pendingPincode}:</p>
                          <RadioGroup 
                            value={selectedLocality ?? ''} 
                            onValueChange={setSelectedLocality}
                            className="space-y-2"
                           >
                            {pincodeLocalityOptions.map((locality) => (
                              <div key={locality} className="flex items-center space-x-2">
                                <RadioGroupItem value={locality} id={`locality-${locality}`} />
                                <Label htmlFor={`locality-${locality}`} className="font-normal">{locality}</Label>
                              </div>
                            ))}
                          </RadioGroup>

                           {/* Display hook error during selection phase */} 
                          {locationError && (
                             <Alert variant="destructive" className="mt-2">
                                <AlertCircle className="h-4 w-4" />
                                <AlertTitle className="text-xs">Error</AlertTitle>
                                <AlertDescription className="text-xs">
                                  {locationError instanceof Error ? locationError.message : String(locationError)}
                                </AlertDescription>
                             </Alert>
                          )}

                          <div className="flex justify-between gap-2">
                            <Button 
                               variant="outline" 
                               size="sm" 
                               onClick={handleBackToPincode}
                               disabled={isConfirmingSelection} // Disable while confirming
                             >
                              Back
                            </Button>
                            <Button 
                              size="sm" 
                              onClick={handleConfirmSelection}
                              disabled={!selectedLocality || isConfirmingSelection || locationLoading}
                            >
                              {isConfirmingSelection ? (
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                               ) : null}
                              Confirm Selection
                            </Button>
                          </div>
                        </div>
                       ) : ( 
                        /* Show Pincode Input */ 
                        <div className="space-y-2">
                          <div className="relative">
                            <MapPin className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                            <Input
                              type="text"
                              placeholder="Enter 6-digit Pincode"
                              value={pincode}
                              onChange={(e) => {
                                const val = e.target.value.replace(/[^0-9]/g, '').slice(0, 6);
                                setPincode(val);
                                setPopoverError(null); // Clear error on input change
                              }}
                              maxLength={6}
                              disabled={isLoadingOptions || locationLoading}
                              className="pl-9" // Add padding for the icon
                            />
                          </div>
                          <Button 
                             className="w-full mt-2" 
                             disabled={pincode.length !== 6 || isLoadingOptions || locationLoading}
                             onClick={handleSetPincode} // Call the hook function
                           >
                            {isLoadingOptions ? (
                              <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Fetching...</>
                             ) : null}
                            Set Location
                          </Button>
                           {/* Display hook error or popover error during pincode entry */} 
                          {(popoverError || (locationError && !pendingPincode)) && (
                             <Alert variant="destructive" className="mt-2">
                                <AlertCircle className="h-4 w-4" />
                                <AlertTitle className="text-xs">Error</AlertTitle>
                                <AlertDescription className="text-xs">
                                  {popoverError || (locationError instanceof Error ? locationError.message : String(locationError))}
                                </AlertDescription>
                             </Alert>
                          )}
                         </div>
                       )}
                     </div>
                    </div>
                  </PopoverContent>
                </Popover>
                <nav className="flex items-center space-x-6">
                  {/* Home Link */}
                  <Link
                    key={navLinks[0].href}
                    to={navLinks[0].href}
                    className={cn(
                      "text-sm font-medium transition-colors hover:text-primary relative group py-2",
                      location.pathname === navLinks[0].href ? "text-primary" : "text-muted-foreground"
                    )}
                  >
                    {navLinks[0].title}
                    <span className="absolute inset-x-0 bottom-0 h-0.5 bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left" />
                  </Link>

                  {/* Discover Events Link */}
                  <Link
                    key={navLinks[1].href}
                    to={navLinks[1].href}
                    className={cn(
                      "text-sm font-medium transition-colors hover:text-primary relative group py-2",
                      location.pathname === navLinks[1].href ? "text-primary" : "text-muted-foreground"
                    )}
                  >
                    {navLinks[1].title}
                    <span className="absolute inset-x-0 bottom-0 h-0.5 bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left" />
                  </Link>

                  {/* My Events Link (conditional) */}
                  {user && (
                    <Link
                      to="/my-events"
                      className={cn(
                        "text-sm font-medium transition-colors hover:text-primary relative group py-2",
                        location.pathname === "/my-events" ? "text-primary" : "text-muted-foreground"
                      )}
                    >
                      My Events
                      <span className="absolute inset-x-0 bottom-0 h-0.5 bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left" />
                    </Link>
                  )}

                  {/* Organizations Link (conditional) */}
                  {user && (
                    <Link
                      to="/organizations"
                      className={cn(
                        "text-sm font-medium transition-colors hover:text-primary relative group py-2",
                        location.pathname === "/organizations" ? "text-primary" : "text-muted-foreground"
                      )}
                    >
                      Organizations
                      <span className="absolute inset-x-0 bottom-0 h-0.5 bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left" />
                    </Link>
                  )}

                  {/* Host Event Link */}
                  <Link
                    to={user ? "/create" : "/auth?redirect=/create"}
                    className={cn(
                      "text-sm font-medium transition-colors hover:text-primary relative group py-2",
                      (location.pathname === "/create" || (location.pathname === "/auth" && location.search.includes("redirect=/create"))) ? "text-primary" : "text-muted-foreground"
                    )}
                  >
                    Host Event
                    <span className="absolute inset-x-0 bottom-0 h-0.5 bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left" />
                  </Link>


                </nav>
              </div> // Close Desktop Nav container
            )}

            {/* Actions */}
            <div className="flex items-center space-x-3">
              {!isMobile && (
                <>
                  {user ? (
                    <DropdownMenu>
                      <DropdownMenuTrigger className="focus:outline-none">
                        <Avatar className="h-8 w-8 border-2 border-blue-primary/30">
                          {/* Only render AvatarImage if we have a non-Googleusercontent URL or none at all */}
                          {!avatarUrlRef.current || !avatarUrlRef.current.includes('googleusercontent.com') ? (
                            <AvatarImage 
                              src={avatarUrlRef.current || undefined} 
                              alt={user.user_metadata?.name || "User"} 
                            />
                          ) : null}
                          <AvatarFallback>{getUserInitials()}</AvatarFallback>
                        </Avatar>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild className="cursor-pointer">
                          <Link to="/profile">
                            <User className="mr-2 h-4 w-4" />
                            <span>My Profile</span>
                          </Link>
                        </DropdownMenuItem>
                        {isAdmin && (
                          <DropdownMenuItem asChild className="cursor-pointer">
                            <Link to="/admin">
                              <Shield className="mr-2 h-4 w-4" />
                              <span>Admin Dashboard</span>
                            </Link>
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem onClick={() => signOut()} className="cursor-pointer">
                          <LogOut className="mr-2 h-4 w-4" />
                          <span>Sign out</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  ) : (
                    <Button asChild variant="ghost" className="text-muted-foreground hover:text-primary">
                      <Link to="/auth">
                        <LogIn className="mr-2 h-4 w-4" />
                        Sign In
                      </Link>
                    </Button>
                  )}
                </>
              )}

              {/* Location for Mobile */}
              {isMobile && (
                <Popover open={isPopoverOpen} onOpenChange={handlePopoverOpenChange}>
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <PopoverTrigger asChild aria-label="Change location">
                          <button
                            className={cn(
                              "flex items-center text-sm transition-colors flex-shrink-0 cursor-pointer mr-2",
                              "text-foreground hover:text-primary",
                              locationLoading && "cursor-wait opacity-70",
                              permissionDenied && "text-destructive hover:text-destructive/80"
                            )}
                            disabled={locationLoading}
                          >
                            <MapPin className="h-4 w-4 mr-1 text-primary" /> 
                            {locationLoading && !localityShortName ? (
                              <Skeleton className="h-4 w-16" /> 
                            ) : (
                              <span className="inline-block"> 
                                {localityShortName || 'Set Location'}
                              </span>
                            )}
                          </button>
                        </PopoverTrigger>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">
                        {locationLoading
                          ? "Fetching location..."
                          : permissionDenied 
                          ? "Location denied. Click to change."
                          : locationError
                          ? `Error: ${locationError}. Click to change.`
                          : `Current location: ${localityShortName || 'Unknown'}. Click to change.`
                        } 
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <PopoverContent className="w-80 mt-2" align="end">
                    <div className="grid gap-4 p-4">
                      {/* Section 1: Use Current Location */}
                      <div className="space-y-2">
                        <h4 className="font-medium leading-none text-sm text-muted-foreground">Detect Automatically</h4>
                        <Button 
                          variant="outline" 
                          className="w-full justify-start" 
                          onClick={handleUseCurrentLocation}
                          disabled={locationLoading}
                        >
                          {locationLoading && !pendingPincode && !isLoadingOptions ? (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          ) : (
                            <LocateFixed className="mr-2 h-4 w-4" />
                          )}
                          Use Current Location
                        </Button>
                        {permissionDenied && (
                          <Alert variant="destructive" className="mt-2">
                            <AlertCircle className="h-4 w-4" />
                            <AlertTitle className="text-xs">Permission Denied</AlertTitle>
                            <AlertDescription className="text-xs">
                              Location permission denied. Please enable it in browser settings.
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>

                      <Separator />

                      {/* Section 2: Pincode Entry / Locality Selection */}
                      <div className="space-y-2">
                        <h4 className="font-medium leading-none text-sm text-muted-foreground">Or Enter Pincode</h4>
                        {pendingPincode && pincodeLocalityOptions && pincodeLocalityOptions.length > 0 ? (
                          <div className="space-y-3">
                            <p className="text-sm text-muted-foreground">Select your locality for pincode {pendingPincode}:</p>
                            <RadioGroup 
                              value={selectedLocality ?? ''} 
                              onValueChange={setSelectedLocality}
                              className="space-y-2"
                            >
                              {pincodeLocalityOptions.map((locality) => (
                                <div key={locality} className="flex items-center space-x-2">
                                  <RadioGroupItem value={locality} id={`mobile-locality-${locality}`} />
                                  <Label htmlFor={`mobile-locality-${locality}`} className="font-normal">{locality}</Label>
                                </div>
                              ))}
                            </RadioGroup>

                            {locationError && (
                              <Alert variant="destructive" className="mt-2">
                                <AlertCircle className="h-4 w-4" />
                                <AlertTitle className="text-xs">Error</AlertTitle>
                                <AlertDescription className="text-xs">
                                  {locationError instanceof Error ? locationError.message : String(locationError)}
                                </AlertDescription>
                              </Alert>
                            )}

                            <div className="flex justify-between gap-2">
                              <Button 
                                variant="outline" 
                                size="sm" 
                                onClick={handleBackToPincode}
                                disabled={isConfirmingSelection}
                              >
                                Back
                              </Button>
                              <Button 
                                size="sm" 
                                onClick={handleConfirmSelection}
                                disabled={!selectedLocality || isConfirmingSelection || locationLoading}
                              >
                                {isConfirmingSelection ? (
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                ) : null}
                                Confirm Selection
                              </Button>
                            </div>
                          </div>
                        ) : ( 
                          <div className="space-y-2">
                            <div className="relative">
                              <MapPin className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                              <Input
                                type="text"
                                placeholder="Enter 6-digit Pincode"
                                value={pincode}
                                onChange={(e) => {
                                  const val = e.target.value.replace(/[^0-9]/g, '').slice(0, 6);
                                  setPincode(val);
                                  setPopoverError(null); 
                                }}
                                maxLength={6}
                                disabled={isLoadingOptions || locationLoading}
                                className="pl-9"
                              />
                            </div>
                            <Button 
                              className="w-full mt-2" 
                              disabled={pincode.length !== 6 || isLoadingOptions || locationLoading}
                              onClick={handleSetPincode}
                            >
                              {isLoadingOptions ? (
                                <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Fetching...</>
                              ) : "Set Location"}
                            </Button>
                            {(popoverError || (locationError && !pendingPincode)) && (
                              <Alert variant="destructive" className="mt-2">
                                <AlertCircle className="h-4 w-4" />
                                <AlertTitle className="text-xs">Error</AlertTitle>
                                <AlertDescription className="text-xs">
                                  {popoverError || (locationError instanceof Error ? locationError.message : String(locationError))}
                                </AlertDescription>
                              </Alert>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              )}

              {/* Mobile Menu Toggle */}
              {isMobile && (
                <button
                  onClick={toggleMenu}
                  className={cn(
                    "p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 transition-colors duration-150 ease-in-out",
                    isMenuOpen && "bg-accent" // Add accent background when menu is open
                  )}
                  aria-label="Toggle menu"
                >
                  {isMenuOpen ? (
                    <X className="h-6 w-6" />
                  ) : (
                    <Menu className="h-6 w-6" />
                  )}
                </button>
              )}
            </div>
          </div> {/* End of main container div */}

         {/* Mobile Menu Overlay (should be outside justify-between) */}
         <AnimatePresence>
           {isMobile && isMenuOpen && (
             <motion.div
               initial={{ opacity: 0, height: 0 }}
               animate={{ opacity: 1, height: 'auto' }}
               exit={{ opacity: 0, height: 0 }}
               transition={{ duration: 0.2 }}
               className="container mx-auto px-4 pb-4 border-t md:hidden" 
             >
               <div className="py-4 flex flex-col space-y-4">
                 {/* Navigation links within the mobile menu */}
                 <nav className="flex flex-col space-y-2 py-4">
                   {/* Home Link */}
                   <Link
                     key={navLinks[0].href}
                     to={navLinks[0].href}
                     className={cn(
                       "flex items-center text-base font-medium py-2 transition-colors",
                       location.pathname === navLinks[0].href
                         ? "text-blue-primary"
                         : "text-foreground hover:text-primary"
                     )}
                     onClick={() => setIsMenuOpen(false)}
                   >
                     <Home className="h-4 w-4 mr-2" />
                     <span>{navLinks[0].title}</span>
                   </Link>

                   {/* Discover Events Link */}
                   <Link
                     key={navLinks[1].href}
                     to={navLinks[1].href}
                     className={cn(
                       "flex items-center text-base font-medium py-2 transition-colors", // Ensure 'flex items-center'
                       location.pathname === navLinks[1].href
                         ? "text-blue-primary"
                         : "text-foreground hover:text-primary"
                     )}
                     onClick={() => setIsMenuOpen(false)}
                   >
                     <Search className="h-4 w-4 mr-2" />
                     <span>{navLinks[1].title}</span>
                   </Link>
                   
                   {/* My Events Link (conditional, with icon) */}
                   {user && (
                     <Link
                       to="/my-events"
                       className={cn(
                         "flex items-center text-base font-medium py-2 transition-colors",
                         location.pathname === "/my-events"
                           ? "text-blue-primary"
                           : "text-foreground hover:text-primary"
                       )}
                       onClick={() => setIsMenuOpen(false)}
                     >
                       <Calendar className="h-4 w-4 mr-2" />
                       <span>My Events</span>
                     </Link>
                   )}

                   {/* Organizations Link (conditional, with icon) */}
                   {user && (
                     <Link
                       to="/organizations"
                       className={cn(
                         "flex items-center text-base font-medium py-2 transition-colors",
                         location.pathname === "/organizations"
                           ? "text-blue-primary"
                           : "text-foreground hover:text-primary"
                       )}
                       onClick={() => setIsMenuOpen(false)}
                     >
                       <Building2 className="h-4 w-4 mr-2" />
                       <span>Organizations</span>
                     </Link>
                   )}

                   {/* Host Event Link */}
                   <Link
                     to={user ? "/create" : "/auth?redirect=/create"}
                     className={cn(
                       "flex items-center text-base font-medium py-2 transition-colors", // Added flex items-center
                       (location.pathname === "/create" || (location.pathname === "/auth" && location.search.includes("redirect=/create")))
                         ? "text-blue-primary"
                         : "text-foreground hover:text-primary"
                     )}
                     onClick={() => setIsMenuOpen(false)}
                   >
                     <PlusCircle className="h-4 w-4 mr-2" /> {/* Added Icon */}
                     <span>Host Event</span>
                   </Link>


                   
                   {/* Profile link for mobile */}
                   {user && (
                     <Link
                       to="/profile"
                       className={cn(
                         "flex items-center text-base font-medium py-2 transition-colors",
                         location.pathname === "/profile" 
                           ? "text-blue-primary" 
                           : "text-foreground hover:text-primary"
                       )}
                       onClick={() => setIsMenuOpen(false)}
                     >
                       <User className="h-4 w-4 mr-2" />
                       <span>My Profile</span>
                     </Link>
                   )}
                   
                   {/* Admin link for mobile */}
                   {user && isAdmin && (
                     <Link
                       to="/admin"
                       className={cn(
                         "flex items-center text-base font-medium py-2 transition-colors",
                         location.pathname === "/admin" 
                           ? "text-blue-primary" 
                           : "text-foreground hover:text-primary"
                       )}
                       onClick={() => setIsMenuOpen(false)}
                     >
                       <Shield className="h-4 w-4 mr-2" />
                       <span>Admin Dashboard</span>
                     </Link>
                   )}
                   
                   {/* Sign In / User Info & Sign Out */} 
                   {user ? (
                     <>
                       <div className="flex items-center space-x-3 py-2">
                         <Avatar className="h-8 w-8">
                           {/* Render AvatarImage only if avatarUrlRef has a value */}
                           {avatarUrlRef.current && (
                             <AvatarImage 
                               src={avatarUrlRef.current}
                               alt={user.user_metadata?.name || "User"} 
                             />
                           )}
                           <AvatarFallback>{getUserInitials()}</AvatarFallback>
                         </Avatar>
                         <div className="text-sm font-medium truncate">
                           {user.user_metadata?.name || user.user_metadata?.full_name || user.email}
                         </div>
                       </div>
                       
                       <button 
                         onClick={() => { signOut(); setIsMenuOpen(false); }} // Also close menu
                         className="flex items-center text-base font-medium py-2 text-foreground hover:text-primary transition-colors"
                       >
                         <LogOut className="h-4 w-4 mr-2" />
                         <span>Sign out</span>
                       </button>
                     </>
                   ) : (
                     <Link 
                       to="/auth" 
                       className={cn(
                         "flex items-center text-base font-medium py-2 transition-colors",
                         location.pathname === "/auth" 
                           ? "text-blue-primary" 
                           : "text-foreground hover:text-primary"
                       )}
                       onClick={() => setIsMenuOpen(false)} // Close menu on click
                     >
                       <LogIn className="h-4 w-4 mr-2" />
                       <span>Sign In</span>
                     </Link>
                   )}
                 </nav>
               </div>
             </motion.div>
           )}
         </AnimatePresence>
      </div> {/* End container div */}
     </header>
   );
 };

 export default Header;
