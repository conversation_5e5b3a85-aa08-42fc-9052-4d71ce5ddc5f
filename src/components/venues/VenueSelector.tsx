import React, { useState, useEffect } from 'react';
import { Check, ChevronsUpDown, Loader2, Plus, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useVenues } from '@/hooks/useVenues';
import { cn } from '@/lib/utils';
import { EventVenue } from '@/types/venue';
import { useUserSearchRadius } from '@/hooks/useUserSearchRadius';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';

const venueSchema = z.object({
  name: z.string().min(2, { message: 'Venue name must be at least 2 characters' }),
  address: z.string().min(5, { message: 'Address must be at least 5 characters' }),
  city: z.string().min(2, { message: 'City is required' }),
  state: z.string().min(2, { message: 'State is required' }),
  zip_code: z.string().optional(),
});

type VenueFormValues = z.infer<typeof venueSchema>;

interface VenueSelectorProps {
  selectedVenue?: EventVenue | null;
  onVenueChange?: (venue: EventVenue | null) => void;
  className?: string;
  value?: string;
  onChange?: (value: string, venue: EventVenue | null) => void;
  onCreateVenue?: () => void;
}

const VenueSelector: React.FC<VenueSelectorProps> = ({
  selectedVenue,
  onVenueChange,
  className,
  value,
  onChange,
  onCreateVenue
}) => {
  const [open, setOpen] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const { venues, searchTerm, setSearchTerm, searchResults, searching, createVenue } = useVenues();
  const { searchRadius } = useUserSearchRadius();
  const [displayValue, setDisplayValue] = useState('');

  // Reset search results when popover closes
  useEffect(() => {
    if (!open) {
      // Small delay to prevent UI flicker
      setTimeout(() => {
        setSearchTerm('');
      }, 300);
    }
  }, [open, setSearchTerm]);

  useEffect(() => {
    if (selectedVenue) {
      setDisplayValue(selectedVenue.name);
    } else if (value) {
      // If using as a form field with value prop
      const venue = venues.find(v => v.id === value);
      if (venue) {
        setDisplayValue(venue.name);
      }
    } else {
      setDisplayValue('');
    }
  }, [selectedVenue, value, venues]);

  const handleVenueSelect = (venue: EventVenue) => {
    // Support both usage patterns
    if (onChange) {
      onChange(venue.id, venue);
    }
    if (onVenueChange) {
      onVenueChange(venue);
    }
    setOpen(false);
  };

  const handleCreateVenue = async (data: VenueFormValues) => {
    // Ensure all required fields are non-optional
    const venueData = {
      name: data.name,
      address: data.address,
      city: data.city,
      state: data.state,
      zip_code: data.zip_code || null,
      priority: 0
    };

    const newVenue = await createVenue(venueData);

    if (newVenue) {
      if (onChange) {
        onChange(newVenue.id, newVenue);
      }
      if (onVenueChange) {
        onVenueChange(newVenue);
      }
      setDialogOpen(false);
      form.reset();
    }
  };

  const form = useForm<VenueFormValues>({
    resolver: zodResolver(venueSchema),
    defaultValues: {
      name: '',
      address: '',
      city: '',
      state: '',
      zip_code: '',
    },
  });

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            {displayValue || "Select venue..."}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[300px] p-0">
          <div className="p-2">
            <Input
              placeholder="Search venues..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="mb-2"
            />

            {searching && (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                <span>Searching...</span>
              </div>
            )}

            {!searching && searchTerm && searchResults.length === 0 && (
              <div className="py-4 text-center text-sm">
                <p>No venues found within {searchRadius}km of your location.</p>
                <Button
                  variant="link"
                  className="mt-2 text-blue-500"
                  onClick={() => {
                    setDialogOpen(true);
                    if (onCreateVenue) onCreateVenue();
                  }}
                >
                  <Plus className="mr-1 h-4 w-4" />
                  Add a new venue
                </Button>
              </div>
            )}

            {!searchTerm && venues.length > 0 && (
              <div className="py-2">
                <div className="text-sm font-medium px-2 py-1.5 text-muted-foreground">Nearby Venues (within {searchRadius}km)</div>
                <div className="max-h-[300px] overflow-auto">
                  {venues.slice(0, 5).map((venue) => (
                    <div
                      key={venue.id}
                      className={cn(
                        "flex items-center justify-between px-2 py-1.5 cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-sm",
                        (selectedVenue?.id === venue.id || value === venue.id) && "bg-accent text-accent-foreground"
                      )}
                      onClick={() => handleVenueSelect(venue)}
                    >
                      <div className="flex items-center">
                        <MapPin className="mr-2 h-4 w-4" />
                        <div className="flex flex-col">
                          <span>{venue.name}</span>
                          {venue.distance_km !== undefined && (
                            <span className="text-xs text-muted-foreground">
                              {venue.distance_km.toFixed(1)}km away
                            </span>
                          )}
                        </div>
                      </div>
                      {(selectedVenue?.id === venue.id || value === venue.id) && (
                        <Check className="h-4 w-4" />
                      )}
                    </div>
                  ))}
                  {venues.length > 5 && (
                    <div className="text-xs text-center text-muted-foreground py-1">
                      Search to see more venues
                    </div>
                  )}
                </div>
              </div>
            )}

            {searchResults.length > 0 && (
              <div className="py-2">
                <div className="text-sm font-medium px-2 py-1.5 text-muted-foreground">Nearby Search Results (within {searchRadius}km)</div>
                <div className="max-h-[300px] overflow-auto">
                  {searchResults.map((venue) => (
                    <div
                      key={venue.id}
                      className={cn(
                        "flex items-center justify-between px-2 py-1.5 cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-sm",
                        (selectedVenue?.id === venue.id || value === venue.id) && "bg-accent text-accent-foreground"
                      )}
                      onClick={() => handleVenueSelect(venue)}
                    >
                      <div className="flex items-center">
                        <MapPin className="mr-2 h-4 w-4" />
                        <div className="flex flex-col">
                          <span>{venue.name}</span>
                          {venue.distance_km !== undefined && (
                            <span className="text-xs text-muted-foreground">
                              {venue.distance_km.toFixed(1)}km away
                            </span>
                          )}
                        </div>
                      </div>
                      {(selectedVenue?.id === venue.id || value === venue.id) && (
                        <Check className="h-4 w-4" />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="border-t mt-2 pt-2">
              <Button
                variant="outline"
                className="w-full justify-center"
                onClick={() => {
                  setDialogOpen(true);
                  if (onCreateVenue) onCreateVenue();
                }}
              >
                <Plus className="mr-2 h-4 w-4" />
                Add a new venue
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Venue</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleCreateVenue)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Venue Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter venue name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter address" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>City</FormLabel>
                      <FormControl>
                        <Input placeholder="City" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>State</FormLabel>
                      <FormControl>
                        <Input placeholder="State" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="zip_code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>ZIP Code (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="ZIP Code" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex justify-end gap-2 pt-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Add Venue</Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default VenueSelector;