import React, { useEffect } from 'react';
import { useAnalytics } from '@/hooks/useAnalytics';
import { useAuth } from '@/context/AuthContext';
import { setAnonymousUser } from '@/utils/analytics';

interface AnalyticsProviderProps {
  children: React.ReactNode;
}

/**
 * Analytics Provider Component
 * This component initializes analytics and provides tracking capabilities
 * to all child components through the useAnalytics hook.
 */
const AnalyticsProvider: React.FC<AnalyticsProviderProps> = ({ children }) => {
  // Initialize analytics - this will handle page view tracking automatically
  useAnalytics();

  const { user } = useAuth();

  // Set up user identification for analytics
  useEffect(() => {
    // If user is not logged in, set up anonymous tracking
    if (!user) {
      setAnonymousUser();
    }
    // If user is logged in, the AuthContext will handle setting the user ID
  }, [user]);

  // This component doesn't render anything visible, it just initializes analytics
  return <>{children}</>;
};

export default AnalyticsProvider;
