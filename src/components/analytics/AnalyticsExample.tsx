import React, { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  useAnalytics, 
  useEventAnalytics, 
  useSearchAnalytics, 
  useFormAnalytics,
  useComponentTiming 
} from '@/hooks/useAnalytics';

/**
 * Example component demonstrating various analytics tracking features
 * This is for demonstration purposes and can be removed in production
 */
const AnalyticsExample: React.FC = () => {
  // Basic analytics hook
  const { trackEvent, trackError } = useAnalytics();
  
  // Specialized analytics hooks
  const { trackEventView, trackEventRegister, trackEventShare } = useEventAnalytics();
  const { trackSearchQuery } = useSearchAnalytics();
  const { trackFormStart, trackFormSubmit, trackFormError } = useFormAnalytics('example-form');
  
  // Track component performance
  useComponentTiming('AnalyticsExample');

  // Example: Track component mount
  useEffect(() => {
    trackEvent('component_mounted', 'User Engagement', 'Analytics Example');
  }, [trackEvent]);

  // Example handlers for different types of tracking
  const handleBasicEvent = () => {
    trackEvent('button_click', 'User Engagement', 'Basic Event Button');
  };

  const handleEventView = () => {
    trackEventView('example-event-123', 'Sample Event Title');
  };

  const handleEventRegister = () => {
    trackEventRegister('example-event-123', 'Sample Event Title');
  };

  const handleEventShare = () => {
    trackEventShare('example-event-123', 'Sample Event Title');
  };

  const handleSearch = () => {
    trackSearchQuery('sample search query', 5);
  };

  const handleFormStart = () => {
    trackFormStart();
  };

  const handleFormSubmit = () => {
    trackFormSubmit();
  };

  const handleFormError = () => {
    trackFormError('Sample form validation error');
  };

  const handleError = () => {
    trackError('Sample error message', 'AnalyticsExample component');
  };

  const handleCustomEvent = () => {
    trackEvent('custom_action', 'Custom Category', 'Custom Label', 42, {
      custom_parameter: 'custom_value',
      user_action: 'demo_interaction'
    });
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Analytics Tracking Examples</CardTitle>
        <CardDescription>
          This component demonstrates various analytics tracking features. 
          Check your browser console (with VITE_GA_DEBUG=true) or Google Analytics dashboard to see the events.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <Button onClick={handleBasicEvent} variant="outline">
            Track Basic Event
          </Button>
          
          <Button onClick={handleEventView} variant="outline">
            Track Event View
          </Button>
          
          <Button onClick={handleEventRegister} variant="outline">
            Track Event Register
          </Button>
          
          <Button onClick={handleEventShare} variant="outline">
            Track Event Share
          </Button>
          
          <Button onClick={handleSearch} variant="outline">
            Track Search Query
          </Button>
          
          <Button onClick={handleFormStart} variant="outline">
            Track Form Start
          </Button>
          
          <Button onClick={handleFormSubmit} variant="outline">
            Track Form Submit
          </Button>
          
          <Button onClick={handleFormError} variant="outline">
            Track Form Error
          </Button>
          
          <Button onClick={handleError} variant="outline">
            Track Error
          </Button>
          
          <Button onClick={handleCustomEvent} variant="outline">
            Track Custom Event
          </Button>
        </div>
        
        <div className="mt-6 p-4 bg-muted rounded-lg">
          <h4 className="font-semibold mb-2">Analytics Configuration</h4>
          <div className="text-sm space-y-1">
            <p><strong>Tracking ID:</strong> {import.meta.env.VITE_GA_TRACKING_ID || 'Not configured'}</p>
            <p><strong>Debug Mode:</strong> {import.meta.env.VITE_GA_DEBUG === 'true' ? 'Enabled' : 'Disabled'}</p>
            <p><strong>Analytics Enabled:</strong> {
              import.meta.env.VITE_GA_TRACKING_ID && 
              (import.meta.env.PROD || import.meta.env.VITE_ENABLE_ANALYTICS === 'true') 
                ? 'Yes' : 'No'
            }</p>
          </div>
        </div>
        
        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold mb-2 text-blue-800">Setup Instructions</h4>
          <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
            <li>Set up Google Analytics 4 account</li>
            <li>Add VITE_GA_TRACKING_ID to your .env file</li>
            <li>Set VITE_GA_DEBUG=true for development testing</li>
            <li>Build and deploy your application</li>
            <li>Check Google Analytics dashboard for data</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
};

export default AnalyticsExample;
