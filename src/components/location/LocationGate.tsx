import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MapPin, LocateFixed, Loader2, AlertCircle, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useLocationGate } from '@/hooks/useLocationGate';
import { useMobile } from '@/hooks/use-mobile';

interface LocationGateProps {
  children: React.ReactNode;
}

const LocationGate: React.FC<LocationGateProps> = ({ children }) => {
  const {
    isGateOpen,
    isSettingLocation,
    error,
    permissionDenied,
    pincodeLocalityOptions,
    pendingPincode,
    requestLocation,
    setLocationFromPincode,
    confirmPincodeSelection,
    clearPincodeOptions
  } = useLocationGate();

  const { isMobile } = useMobile();
  const [pincode, setPincode] = useState('');
  const [selectedLocality, setSelectedLocality] = useState<string | null>(null);

  // If the gate is closed, render children
  if (!isGateOpen) {
    return <>{children}</>;
  }

  // Variants for animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.3 } }
  };

  // Use the same animation for both mobile and desktop - a centered modal
  const contentVariants = {
    hidden: { scale: 0.95, opacity: 0 },
    visible: { scale: 1, opacity: 1, transition: { type: 'spring', damping: 25, stiffness: 300 } }
  };

  const handlePincodeSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (pincode.length === 6) {
      setLocationFromPincode(pincode);
    }
  };

  const handleConfirmSelection = (locality: string) => {
    confirmPincodeSelection(locality);
  };

  const handleBackToPincode = () => {
    clearPincodeOptions();
    setPincode('');
    setSelectedLocality(null);
  };

  return (
    <AnimatePresence mode="wait">
      {isGateOpen && (
        <motion.div
          className="fixed inset-0 z-50 bg-black/70 backdrop-blur-md flex items-center justify-center overflow-hidden"
          initial="hidden"
          animate="visible"
          exit="hidden"
          variants={containerVariants}
        >
        <motion.div
          className="bg-white relative w-[95%] max-w-md rounded-3xl shadow-xl max-h-[90vh] overflow-y-auto"
          variants={contentVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Background image (very subtle) */}
          <div
            className="absolute inset-0 opacity-[0.12] pointer-events-none rounded-t-3xl md:rounded-3xl overflow-hidden"
            style={{
              backgroundImage: 'url(/delhi_bg.png)',
              backgroundSize: 'cover',
              backgroundPosition: 'center'
            }}
          />

          <div className="relative z-10 p-6 md:p-8 pb-8">
            {/* Header */}
            <div className="text-center mb-6">
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.3 }}
                className="mb-4"
              >
                <h2 className="text-2xl md:text-3xl font-bold font-raleway leading-none">
                  <span className="text-blue-primary">The</span>
                  <span className="text-yellow-accent mx-1 font-[Mukta] align-middle relative -top-[1px]">लोकल</span>
                  <span className="text-blue-primary">Adda</span>
                </h2>
              </motion.div>
              <motion.h2
                initial={{ y: 10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.3 }}
                className="text-xl md:text-2xl font-bold mb-3"
              >
                Set Your Location
              </motion.h2>
              <motion.p
                initial={{ y: 10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4, duration: 0.3 }}
                className="text-muted-foreground text-base md:text-lg"
              >
                We need your location to show you relevant events nearby
              </motion.p>
            </div>

            {/* Location Selection Options */}
            <motion.div
              initial={{ y: 10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.3 }}
              className="space-y-6"
            >
              {/* Current Location Button */}
              <div className="space-y-3">
                <h4 className="font-medium text-base text-muted-foreground">Use Device Location</h4>
                <Button
                  variant="outline"
                  size="lg"
                  className="w-full justify-start group hover:border-blue-primary/50 hover:bg-blue-50/50 transition-all text-base py-6"
                  onClick={requestLocation}
                  disabled={isSettingLocation}
                >
                  {isSettingLocation && !pendingPincode ? (
                    <Loader2 className="mr-3 h-5 w-5 animate-spin" />
                  ) : (
                    <LocateFixed className="mr-3 h-5 w-5 text-blue-primary group-hover:scale-110 transition-transform" />
                  )}
                  Use Current Location
                </Button>

                {permissionDenied && (
                  <Alert variant="destructive" className="mt-2">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle className="text-xs">Permission Denied</AlertTitle>
                    <AlertDescription className="text-xs">
                      Location permission denied. Please enable it in browser settings or use pincode instead.
                    </AlertDescription>
                  </Alert>
                )}
              </div>

              {/* Divider with text */}
              <div className="relative py-2">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-muted"></div>
                </div>
                <div className="relative flex justify-center text-sm uppercase">
                  <span className="bg-white px-3 text-muted-foreground font-medium">OR</span>
                </div>
              </div>

              {/* Pincode Entry / Locality Selection */}
              <div className="space-y-3">
                <h4 className="font-medium text-base text-muted-foreground">Enter Pincode</h4>

                {pendingPincode && pincodeLocalityOptions && pincodeLocalityOptions.length > 0 ? (
                  <div className="space-y-4">
                    <p className="text-base font-medium">Select your locality for pincode {pendingPincode}:</p>
                    <div className="max-h-[180px] overflow-y-auto pr-1 -mr-1 locality-scroll-container">
                      <div className="space-y-2">
                        {pincodeLocalityOptions.map((locality, index) => (
                          <motion.div
                            key={locality}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.3, delay: 0.1 + (index * 0.05) }}
                          >
                            <Button
                              variant="outline"
                              size="lg"
                              className={`w-full justify-between text-base py-5 px-4 transition-all ${selectedLocality === locality ? 'bg-blue-50 border-blue-primary text-blue-primary' : 'hover:bg-muted/30'}`}
                              onClick={() => {
                                setSelectedLocality(locality);
                                handleConfirmSelection(locality);
                              }}
                              disabled={isSettingLocation}
                            >
                              <span>{locality}</span>
                              {selectedLocality === locality && (
                                <Check className="h-5 w-5 text-blue-primary" />
                              )}
                            </Button>
                          </motion.div>
                        ))}
                      </div>
                    </div>

                    <Button
                      variant="outline"
                      size="lg"
                      onClick={handleBackToPincode}
                      disabled={isSettingLocation}
                      className="w-full text-base mt-4"
                    >
                      Back to Pincode Entry
                    </Button>
                  </div>
                ) : (
                  <form onSubmit={handlePincodeSubmit} className="space-y-4">
                    <div className="relative">
                      <MapPin className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        type="text"
                        placeholder="Enter 6-digit Pincode"
                        value={pincode}
                        onChange={(e) => {
                          const val = e.target.value.replace(/[^0-9]/g, '').slice(0, 6);
                          setPincode(val);
                        }}
                        maxLength={6}
                        disabled={isSettingLocation}
                        className="pl-12 py-6 text-lg rounded-xl"
                      />
                    </div>
                    <Button
                      type="submit"
                      size="lg"
                      className="w-full bg-blue-primary hover:bg-blue-primary/90 text-base py-6"
                      disabled={pincode.length !== 6 || isSettingLocation}
                    >
                      {isSettingLocation && pendingPincode === null ? (
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      ) : null}
                      Set Location
                    </Button>
                  </form>
                )}
              </div>

              {/* Error message */}
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* Privacy note */}
              <p className="text-sm text-center text-muted-foreground pt-4">
                Your location is only used to show you relevant events nearby.
                <br />
                We don't track or share your precise location.
              </p>
            </motion.div>
          </div>
        </motion.div>
      </motion.div>
      )}
    </AnimatePresence>
  );
};

export default LocationGate;
