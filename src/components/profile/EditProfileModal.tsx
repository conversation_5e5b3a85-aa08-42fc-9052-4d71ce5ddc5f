import React, { useState } from 'react'; // Import useState
import { Phone } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
// Removed Label as FormLabel will be used
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

// Zod schema for validation
const profileFormSchema = z.object({
  phone_number: z.string().optional().or(z.literal(''))
    .refine((value) => !value || /^[0-9]{10}$/.test(value), {
      message: "Please enter a 10-digit phone number.",
    }),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

interface EditProfileModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const EditProfileModal: React.FC<EditProfileModalProps> = ({ open, onOpenChange }) => {
  const { userProfile, updateUserProfile } = useAuth();
  const { toast } = useToast();
  const [isUpdatingProfile, setIsUpdatingProfile] = useState(false); // New state for async loading

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      phone_number: userProfile?.phone_number || '',
    },
    mode: 'onTouched',
  });

  const { register, handleSubmit, formState: { errors, isSubmitting: rhfIsSubmitting } } = form;

  const onValidSubmit = async (data: ProfileFormValues) => {
    setIsUpdatingProfile(true); // Set loading true
    try {
      const { error } = await updateUserProfile({
        phone_number: data.phone_number?.trim() || null,
      });

      if (error) throw error;

      toast({
        title: 'Profile updated',
        description: 'Your profile has been successfully updated.',
        variant: 'default',
      });
      onOpenChange(false);
      form.reset({ phone_number: data.phone_number || '' });
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: 'Update failed',
        description: (error as Error)?.message || 'There was a problem updating your profile. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsUpdatingProfile(false); // Set loading false
    }
  };

  const handleModalClose = () => {
    onOpenChange(false);
    form.reset({ phone_number: userProfile?.phone_number || '' });
  };

  return (
    <Dialog open={open} onOpenChange={handleModalClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Profile</DialogTitle>
          <DialogDescription>
            Update your profile information below.
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onValidSubmit)} className="space-y-6 py-4">
            <FormField
              control={form.control}
              name="phone_number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone Number</FormLabel>
                  <FormControl>
                    <div className="flex items-center space-x-2">
                      <div className="bg-blue-primary/10 p-2 rounded">
                        <Phone size={18} className="text-blue-primary" />
                      </div>
                      <Input
                        type="tel"
                        placeholder="Enter your phone number"
                        className="flex-1" // Removed conditional error class
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <p className="text-xs text-muted-foreground mt-1">
              Your name and email are managed by your Google account.
            </p>
            {/* Note: The above <p> tag is outside the FormField structure for phone_number.
                If it's specific to phone_number, it could be FormDescription within FormItem.
                Keeping as is based on current structure. */}

            <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleModalClose}
              disabled={rhfIsSubmitting || isUpdatingProfile}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={rhfIsSubmitting || isUpdatingProfile}>
              { (rhfIsSubmitting || isUpdatingProfile) ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </form>
      </Form>
      </DialogContent>
    </Dialog>
  );
};

export default EditProfileModal;
