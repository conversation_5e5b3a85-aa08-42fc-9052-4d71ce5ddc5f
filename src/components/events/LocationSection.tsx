import React, { useState, useEffect } from 'react';
import { Control } from "react-hook-form";
import { MapPin } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from "@/components/ui/enhanced-accordion";
import { EventFormValues } from '@/types/event-form';
import VenueSelector from '@/components/venues/VenueSelector';
import { EventVenue } from '@/types/venue';
import { useVenues } from '@/hooks/useVenues';
import { Button } from '@/components/ui/button';
import PlacesAutocomplete from '@/components/common/PlacesAutocomplete';
import { PlaceDetails } from '@/utils/googlePlacesLoader';
import { formatDetailedAddress, extractLocality } from '@/utils/addressFormatter';

interface LocationSectionProps {
  control: Control<EventFormValues>;
  errorCount?: number;
  isValid?: boolean;
  showValidation?: boolean;
  completionStatus?: 'untouched' | 'partial' | 'complete' | 'error';
  showProgressIndicators?: boolean;
}

const LocationSection: React.FC<LocationSectionProps> = ({
  control,
  errorCount = 0,
  isValid = false,
  showValidation = false,
  completionStatus = 'untouched',
  showProgressIndicators = false
}) => {
  const { createVenue } = useVenues();
  const [venueMode, setVenueMode] = useState<'select' | 'create'>('select');
  const [isCreatingVenue, setIsCreatingVenue] = useState(false);
  const [locationSelected, setLocationSelected] = useState(false);

  return (
    <AccordionItem value="item-3" data-value="item-3">
      <AccordionTrigger
        className="text-lg font-medium"
        errorCount={errorCount}
        isValid={isValid}
        showValidation={showValidation}
        completionStatus={completionStatus}
        showProgressIndicators={showProgressIndicators}
      >
        <div className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Location & Venue
        </div>
      </AccordionTrigger>
      <AccordionContent className="pt-4">
        <div className="space-y-4">
          {venueMode === 'select' ? (
            <>
              <FormField
                control={control}
                name="venueId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel required>Select a Venue</FormLabel>
                    <FormControl>
                      <VenueSelector
                        value={field.value}
                        onChange={(value, venue) => {
                          field.onChange(value);

                          if (venue) {
                            // Update other form fields with venue data using proper form methods
                            // This ensures validation is triggered correctly
                            const form = control._formState;
                            if (form) {
                              // Use setValue to properly update form fields and trigger validation
                              control._formValues.venueName = venue.name;
                              control._formValues.address = venue.address;
                              control._formValues.city = venue.city;
                              control._formValues.state = venue.state;
                              control._formValues.zipCode = venue.zip_code || '';

                              // Trigger validation update
                              control._subjects.state.next({
                                ...control._formState,
                                isDirty: true,
                              });
                            }
                          }
                        }}
                        onCreateVenue={() => setVenueMode('create')}
                      />
                    </FormControl>
                    <div className="mt-2 text-xs text-muted-foreground">
                      <button
                        type="button"
                        className="text-blue-primary hover:underline focus:outline-none"
                        onClick={() => setVenueMode('create')}
                      >
                        Can't find your venue? Enter a new one
                      </button>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          ) : (
            <>
              <div className="mb-2 text-xs text-muted-foreground">
                <button
                  type="button"
                  className="text-blue-primary hover:underline focus:outline-none"
                  onClick={() => setVenueMode('select')}
                >
                  ← Back to venue selection
                </button>
              </div>

              <FormItem>
                <FormLabel>Search for a venue</FormLabel>
                <div className="text-sm text-muted-foreground mb-2">
                  Start by selecting a location from the suggestions. If you can't find the exact venue, search for a nearby landmark.
                </div>
                <PlacesAutocomplete
                  placeholder="Search for a venue or nearby landmark"
                  initialValue=""
                  key={venueMode} // Force re-render when venue mode changes
                  onPlaceSelect={(placeDetails: PlaceDetails) => {
                    // Set location as selected
                    setLocationSelected(true);
                    // Extract address components
                    let city = '';
                    let state = '';
                    let zipCode = '';

                    placeDetails.addressComponents.forEach(component => {
                      if (component.types.includes('locality')) {
                        city = component.longText;
                      } else if (component.types.includes('administrative_area_level_1')) {
                        state = component.longText;
                      } else if (component.types.includes('postal_code')) {
                        zipCode = component.longText;
                      }
                    });

                    // Get the detailed address using our helper function
                    const detailedAddress = formatDetailedAddress(placeDetails);
                    console.log('Formatted detailed address:', detailedAddress);

                    // Extract the locality (sublocality_level_1)
                    const locality = extractLocality(placeDetails);
                    console.log('Extracted locality:', locality);

                    // Get the place ID
                    const placeId = placeDetails.id;
                    console.log('Place ID:', placeId);

                    // Update form values directly
                    const venueName = control._formValues.venueName = placeDetails.name;
                    const address = control._formValues.address = detailedAddress;
                    // Store the locality and place ID in hidden fields for later use when creating the venue
                    control._formValues.venueLocality = locality;
                    control._formValues.venuePlaceId = placeId;
                    control._formValues.city = city;
                    control._formValues.state = state;
                    control._formValues.zipCode = zipCode;
                    control._formValues.venueLocation = {
                      latitude: placeDetails.location.latitude,
                      longitude: placeDetails.location.longitude
                    };

                    // Force re-render to update the form fields
                    const tempVenueMode = venueMode;
                    setVenueMode('select');
                    setTimeout(() => {
                      setVenueMode(tempVenueMode);

                      // After re-render, update the form fields
                      setTimeout(() => {
                        // Force update the form fields
                        const event = new Event('input', { bubbles: true });
                        const nameInput = document.getElementById('venueName');
                        const addressInput = document.getElementById('venueAddress');
                        const cityInput = document.getElementById('venueCity');
                        const stateInput = document.getElementById('venueState');
                        const zipInput = document.getElementById('venueZip');

                        if (nameInput) {
                          (nameInput as HTMLInputElement).value = venueName;
                          nameInput.dispatchEvent(event);
                        }

                        if (addressInput) {
                          (addressInput as HTMLInputElement).value = address;
                          addressInput.dispatchEvent(event);
                        }

                        if (cityInput) {
                          (cityInput as HTMLInputElement).value = city;
                          cityInput.dispatchEvent(event);
                        }

                        if (stateInput) {
                          (stateInput as HTMLInputElement).value = state;
                          stateInput.dispatchEvent(event);
                        }

                        if (zipInput) {
                          (zipInput as HTMLInputElement).value = zipCode;
                          zipInput.dispatchEvent(event);
                        }
                      }, 100);
                    }, 10);
                  }}
                />
              </FormItem>

              <FormField
                control={control}
                name="venueName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Venue Name</FormLabel>
                    <FormControl>
                      <Input
                        id="venueName"
                        placeholder="e.g. City Convention Center"
                        disabled={!locationSelected}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <Textarea
                        id="venueAddress"
                        placeholder="Street address"
                        disabled={!locationSelected}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="venueLocality"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Locality/Neighborhood</FormLabel>
                    <FormControl>
                      <Input
                        id="venueLocality"
                        placeholder="Locality or Neighborhood"
                        disabled={!locationSelected}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>City</FormLabel>
                      <FormControl>
                        <Input
                          id="venueCity"
                          placeholder="City"
                          disabled={!locationSelected}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>State</FormLabel>
                      <FormControl>
                        <Input
                          id="venueState"
                          placeholder="State"
                          disabled={!locationSelected}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="zipCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>ZIP Code</FormLabel>
                      <FormControl>
                        <Input
                          id="venueZip"
                          placeholder="ZIP Code"
                          disabled={!locationSelected}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex justify-end">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  disabled={isCreatingVenue || !locationSelected}
                  onClick={async () => {
                    // Check if location has been selected
                    if (!locationSelected) {
                      alert('Please select a location from the suggestions first');
                      return;
                    }
                    try {
                      setIsCreatingVenue(true);

                      // Get venue data from form
                      const venueName = control._formValues.venueName;
                      const address = control._formValues.address;
                      const city = control._formValues.city;
                      const state = control._formValues.state;
                      const zipCode = control._formValues.zipCode;

                      // Validate venue data
                      if (!venueName || !address || !city || !state) {
                        return;
                      }

                      // Get venue location if available
                      const venueLocation = control._formValues.venueLocation || null;

                      // Get venue locality and place ID if available
                      const venueLocality = control._formValues.venueLocality || null;
                      const venuePlaceId = control._formValues.venuePlaceId || null;

                      // Create venue
                      const venue = await createVenue({
                        name: venueName,
                        address,
                        city,
                        state,
                        zip_code: zipCode || null,
                        locality: venueLocality,
                        place_id: venuePlaceId,
                        priority: 0,
                        location: venueLocation
                      });

                      if (venue) {
                        // Update venueId field
                        control._formValues.venueId = venue.id;

                        // Switch back to select mode
                        setVenueMode('select');
                      }
                    } catch (error) {
                      console.error('Error creating venue:', error);
                    } finally {
                      setIsCreatingVenue(false);
                    }
                  }}
                >
                  {isCreatingVenue ? (
                    <>Creating venue...</>
                  ) : (
                    <>Save and use this venue</>
                  )}
                </Button>
              </div>
            </>
          )}

          <FormField
            control={control}
            name="parkingInstructions"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Parking Instructions</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Optional: Provide parking information for attendees"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};

export default LocationSection;