import React from 'react';
import { AlertTriangle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface FormErrorSummaryProps {
  errorSummary: Array<{
    sectionId: string;
    sectionName: string;
    errors: string[];
    errorCount: number;
  }>;
  onSectionClick: (sectionId: string) => void;
  className?: string;
}

const FormErrorSummary: React.FC<FormErrorSummaryProps> = ({
  errorSummary,
  onSectionClick,
  className
}) => {
  const totalErrors = errorSummary.reduce((sum, section) => sum + section.errorCount, 0);

  if (errorSummary.length === 0) {
    return null;
  }

  return (
    <Alert variant="destructive" className={className}>
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle className="flex items-center justify-between">
        <span>Please fix the following errors to continue</span>
        <Badge variant="destructive" className="ml-2">
          {totalErrors} error{totalErrors > 1 ? 's' : ''}
        </Badge>
      </AlertTitle>

      <AlertDescription className="mt-3">
        <div className="space-y-2">
          {errorSummary.map((section) => (
            <div key={section.sectionId} className="flex items-center justify-between">
              <Button
                variant="ghost"
                size="sm"
                className="p-0 h-auto text-left justify-start text-destructive hover:text-destructive/80 hover:underline"
                onClick={() => onSectionClick(section.sectionId)}
              >
                <span className="font-medium">{section.sectionName}</span>
                <Badge variant="outline" className="ml-2 border-destructive text-destructive">
                  {section.errorCount}
                </Badge>
              </Button>
            </div>
          ))}
        </div>
      </AlertDescription>
    </Alert>
  );
};

export default FormErrorSummary;
