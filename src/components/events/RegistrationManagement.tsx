import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
  Users, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Eye, 
  FileImage,
  User,
  Mail,
  Phone,
  Calendar,
  CreditCard,
  MessageSquare
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  RegistrationManagement as RegistrationManagementType,
  RegistrationReview,
  RegistrationStatus, // Added
  REGISTRATION_STATUSES, // Added
  REGISTRATION_STATUS_COLORS
} from '@/types/payment';
import { useRegistrations } from '@/hooks/useRegistrations';

interface RegistrationManagementProps {
  eventId: string;
  eventTitle: string;
  isEventFree: boolean;
}

export const RegistrationManagement: React.FC<RegistrationManagementProps> = ({
  eventId,
  eventTitle,
  isEventFree
}) => {
  const { managementRegistrations, loading, reviewRegistration, fetchManagementRegistrations } = useRegistrations();
  const [selectedRegistration, setSelectedRegistration] = useState<RegistrationManagementType | null>(null);
  const [reviewNotes, setReviewNotes] = useState('');
  const [reviewLoading, setReviewLoading] = useState(false);

  useEffect(() => {
    fetchManagementRegistrations(eventId);
  }, [eventId, fetchManagementRegistrations]);

  const handleReview = async (registrationId: string, status: 'accepted' | 'declined') => {
    setReviewLoading(true);

    const review: RegistrationReview = {
      registrationId,
      status, // This now correctly aligns with RegistrationReview type expecting 'accepted' | 'declined'
      notes: reviewNotes.trim() || undefined
    };

    const success = await reviewRegistration(registrationId, review);

    if (success) {
      setSelectedRegistration(null);
      setReviewNotes('');
    }

    setReviewLoading(false);
  };

  // Handle direct accept action
  const handleAccept = async (registrationId: string) => {
    await handleReview(registrationId, 'accepted');
  };

  // Handle direct decline action
  const handleDecline = async (registrationId: string) => {
    await handleReview(registrationId, 'declined');
  };

  const getStatusBadge = (status: RegistrationStatus) => {
    const colorClass = REGISTRATION_STATUS_COLORS[status] || 'bg-gray-100 text-gray-800';
    const statusText = REGISTRATION_STATUSES[status] || status.charAt(0).toUpperCase() + status.slice(1);
    
    return (
      <Badge variant="secondary" className={colorClass}>
        {statusText}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const awaitingConfirmationRegistrations = managementRegistrations.filter(
    reg => reg.registration_status === 'awaiting-confirmation'
  );
  const acceptedRegistrations = managementRegistrations.filter(
    reg => reg.registration_status === 'accepted'
  );
  const declinedRegistrations = managementRegistrations.filter(
    reg => reg.registration_status === 'declined'
  );

  // Also want to display payment-pending registrations, perhaps in their own section or grouped with awaiting-confirmation
  // For now, let's create a separate list for them.
  const paymentPendingRegistrations = managementRegistrations.filter(
    reg => reg.registration_status === 'payment-pending'
  );


  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
            <p className="text-muted-foreground">Loading registrations...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Registration Management - {eventTitle}
          </CardTitle>
          <div className="flex flex-wrap gap-4 text-sm">
            <div className="flex items-center gap-2">
              <CreditCard className="h-4 w-4 text-blue-600" /> {/* Icon for payment-pending */}
              <span>Payment Pending: {paymentPendingRegistrations.length}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-yellow-600" />
              <span>Awaiting Confirmation: {awaitingConfirmationRegistrations.length}</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span>Accepted: {acceptedRegistrations.length}</span>
            </div>
            <div className="flex items-center gap-2">
              <XCircle className="h-4 w-4 text-red-600" />
              <span>Declined: {declinedRegistrations.length}</span>
            </div>
          </div>
        </CardHeader>
      </Card>

      {managementRegistrations.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Users className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-lg font-medium mb-2">No Registrations Yet</p>
            <p className="text-muted-foreground">
              Registrations for this event will appear here once people start signing up.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {/* Payment Pending Registrations */}
          {paymentPendingRegistrations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <CreditCard className="h-5 w-5 text-blue-600" />
                  Payment Pending ({paymentPendingRegistrations.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {paymentPendingRegistrations.map((registration) => (
                    <RegistrationCard
                      key={registration.id}
                      registration={registration}
                      isEventFree={isEventFree}
                      onReview={setSelectedRegistration}
                      showActions={false} // No actions for payment-pending by host typically
                    />
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Awaiting Confirmation Registrations */}
          {awaitingConfirmationRegistrations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Clock className="h-5 w-5 text-yellow-600" />
                  Awaiting Confirmation ({awaitingConfirmationRegistrations.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {awaitingConfirmationRegistrations.map((registration) => (
                    <RegistrationCard
                      key={registration.id}
                      registration={registration}
                      isEventFree={isEventFree}
                      onReview={setSelectedRegistration}
                      onAccept={handleAccept}
                      onDecline={handleDecline}
                      // showActions is true by default, which is correct for this section
                    />
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Accepted Registrations */}
          {acceptedRegistrations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  Accepted ({acceptedRegistrations.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {acceptedRegistrations.map((registration) => (
                    <RegistrationCard
                      key={registration.id}
                      registration={registration}
                      isEventFree={isEventFree}
                      onReview={setSelectedRegistration}
                      showActions={false}
                    />
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Declined Registrations */}
          {declinedRegistrations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <XCircle className="h-5 w-5 text-red-600" />
                  Declined ({declinedRegistrations.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {declinedRegistrations.map((registration) => (
                    <RegistrationCard
                      key={registration.id}
                      registration={registration}
                      isEventFree={isEventFree}
                      onReview={setSelectedRegistration}
                      showActions={false}
                    />
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Review Dialog */}
      {selectedRegistration && (
        <Dialog open={!!selectedRegistration} onOpenChange={() => setSelectedRegistration(null)}>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Review Registration</DialogTitle>
            </DialogHeader>
            
            <div className="space-y-6">
              {/* User Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span className="font-medium">{selectedRegistration.user_profile?.full_name || 'Unknown User'}</span>
                  </div>
                  {selectedRegistration.user_profile?.email && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Mail className="h-4 w-4" />
                      <span>{selectedRegistration.user_profile.email}</span>
                    </div>
                  )}
                  {selectedRegistration.user_profile?.phone_number && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Phone className="h-4 w-4" />
                      <span>{selectedRegistration.user_profile.phone_number}</span>
                    </div>
                  )}
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span className="text-sm">Registered: {formatDate(selectedRegistration.registration_date)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    <span className="text-sm">
                      Amount: ₹{selectedRegistration.total_amount?.toFixed(2) || '0.00'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Payment Proof */}
              {!isEventFree && selectedRegistration.payment_proof_url && (
                <div className="space-y-2">
                  <Label>Payment Proof</Label>
                  <div className="border rounded-lg p-4">
                    <img 
                      src={selectedRegistration.payment_proof_url} 
                      alt="Payment proof" 
                      className="max-w-full h-auto max-h-96 mx-auto rounded border"
                    />
                    {selectedRegistration.payment_transaction_ref && (
                      <p className="text-sm text-muted-foreground mt-2">
                        <strong>Transaction Ref:</strong> {selectedRegistration.payment_transaction_ref}
                      </p>
                    )}
                    {selectedRegistration.payment_proof_uploaded_at && (
                      <p className="text-sm text-muted-foreground">
                        <strong>Uploaded:</strong> {formatDate(selectedRegistration.payment_proof_uploaded_at)}
                      </p>
                    )}
                  </div>
                </div>
              )}

              {/* Review Notes */}
              <div className="space-y-2">
                <Label htmlFor="review-notes">Review Notes (Optional)</Label>
                <Textarea
                  id="review-notes"
                  placeholder="Add any notes about this registration review..."
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                  rows={3}
                />
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setSelectedRegistration(null)}
                  disabled={reviewLoading}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => handleReview(selectedRegistration.id, 'declined')}
                  disabled={reviewLoading}
                  className="flex-1"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Decline
                </Button>
                <Button
                  onClick={() => handleReview(selectedRegistration.id, 'approved')}
                  disabled={reviewLoading}
                  className="flex-1"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

// Registration Card Component
interface RegistrationCardProps {
  registration: RegistrationManagementType;
  isEventFree: boolean;
  onReview: (registration: RegistrationManagementType) => void;
  onAccept?: (registrationId: string) => void;
  onDecline?: (registrationId: string) => void;
  showActions?: boolean;
}

const RegistrationCard: React.FC<RegistrationCardProps> = ({
  registration,
  isEventFree,
  onReview,
  onAccept,
  onDecline,
  showActions = true
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Updated getStatusBadge for RegistrationCard to use new types
  const getStatusBadge = (status: RegistrationStatus) => {
    const colorClass = REGISTRATION_STATUS_COLORS[status] || 'bg-gray-100 text-gray-800';
    const statusText = REGISTRATION_STATUSES[status] || status.charAt(0).toUpperCase() + status.slice(1);
    
    return (
      <Badge variant="secondary" className={colorClass}>
        {statusText}
      </Badge>
    );
  };

  return (
    <div className="border rounded-lg p-4 space-y-3">
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <User className="h-4 w-4" />
            <span className="font-medium">{registration.user_profile?.full_name || 'Unknown User'}</span>
          </div>
          {registration.user_profile?.email && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Mail className="h-4 w-4" />
              <span>{registration.user_profile.email}</span>
            </div>
          )}
        </div>
        
        <div className="flex flex-col gap-2 items-end">
          {getStatusBadge(registration.registration_status as RegistrationStatus)}
          {/* payment_status badge removed */}
        </div>
      </div>

      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            <span>{formatDate(registration.registration_date)}</span>
          </div>
          {!isEventFree && (
            <div className="flex items-center gap-1">
              <CreditCard className="h-4 w-4" />
              <span>₹{registration.total_amount?.toFixed(2) || '0.00'}</span>
            </div>
          )}
        </div>

        {showActions && registration.registration_status === 'awaiting-confirmation' && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onAccept?.(registration.id)}
              className="text-green-600 border-green-600 hover:bg-green-50"
            >
              <CheckCircle className="h-4 w-4 mr-1" />
              Accept
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onDecline?.(registration.id)}
              className="text-red-600 border-red-600 hover:bg-red-50"
            >
              <XCircle className="h-4 w-4 mr-1" />
              Decline
            </Button>
          </div>
        )}
      </div>

      {registration.review_notes && (
        <div className="flex items-start gap-2 text-sm bg-muted/50 p-2 rounded">
          <MessageSquare className="h-4 w-4 mt-0.5" />
          <span>{registration.review_notes}</span>
        </div>
      )}
    </div>
  );
};
