import React from 'react';
import { Control } from "react-hook-form";
import { IndianRupee, CalendarIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Calendar } from '@/components/ui/calendar';
import { Checkbox } from '@/components/ui/checkbox';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { format } from "date-fns";
import { cn } from '@/lib/utils';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from "@/components/ui/enhanced-accordion";
import { EventFormValues } from '@/types/event-form';
import { UseFormGetValues } from 'react-hook-form';

interface TicketingSectionProps {
  control: Control<EventFormValues>;
  isFree: boolean;
  hasEarlyBird: boolean;
  getValues: UseFormGetValues<EventFormValues>;
  errorCount?: number;
  isValid?: boolean;
  showValidation?: boolean;
  completionStatus?: 'untouched' | 'partial' | 'complete' | 'error';
  showProgressIndicators?: boolean;
}

const TicketingSection: React.FC<TicketingSectionProps> = ({
  control,
  isFree,
  hasEarlyBird,
  getValues,
  errorCount = 0,
  isValid = false,
  showValidation = false,
  completionStatus = 'untouched',
  showProgressIndicators = false
}) => {
  const paymentOptions = [
    { id: "upi", label: "UPI" },
    { id: "card", label: "Credit/Debit Card" },
    { id: "paypal", label: "PayPal" },
    { id: "netbanking", label: "Net Banking" },
  ];

  return (
    <AccordionItem value="item-5" data-value="item-5">
      <AccordionTrigger
        className="text-lg font-medium"
        errorCount={errorCount}
        isValid={isValid}
        showValidation={showValidation}
        completionStatus={completionStatus}
        showProgressIndicators={showProgressIndicators}
      >
        <div className="flex items-center gap-2">
          <IndianRupee className="h-5 w-5" />
          Ticketing & Pricing
        </div>
      </AccordionTrigger>
      <AccordionContent className="pt-4">
        <div className="space-y-4">
          <FormField
            control={control}
            name="isFree"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">
                    This is a free event
                  </FormLabel>
                  <FormDescription>
                    Attendees won't need to pay to register
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {!isFree && (
            <div className="space-y-4 pt-2">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={control}
                  name="generalAdmission"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel required>General Admission Price</FormLabel>
                      <div className="flex items-center">
                        <span className="mr-2 h-4 w-4 text-muted-foreground">₹</span>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="0.00"
                            {...field}
                          />
                        </FormControl>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="vipTicket"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>VIP Ticket Price (Optional)</FormLabel>
                      <div className="flex items-center">
                        <span className="mr-2 h-4 w-4 text-muted-foreground">₹</span>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="0.00"
                            {...field}
                          />
                        </FormControl>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-4">
                <FormField
                  control={control}
                  name="hasEarlyBird"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Offer Early Bird Discount</FormLabel>
                        <FormDescription>
                          Lower price for early registrations
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                {hasEarlyBird && (
                  <FormField
                    control={control}
                    name="earlyBirdDeadline"
                    render={({ field }) => (
                      <FormItem className="flex flex-col ml-7">
                        <FormLabel>Early Bird Deadline</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Select date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value || undefined}
                              onSelect={field.onChange}
                              disabled={(date) => {
                                const startDate = getValues("startDate");
                                return date > startDate || date < new Date();
                              }}
                              initialFocus
                              className={cn("p-3 pointer-events-auto")}
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>

              <FormField
                control={control}
                name="groupDiscount"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Offer Group Discounts</FormLabel>
                      <FormDescription>
                        Special pricing for group bookings
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="paymentMethods"
                render={() => (
                  <FormItem>
                    <div className="mb-4">
                      <FormLabel className="text-base">Payment Methods</FormLabel>
                      <FormDescription>
                        Select the payment methods you'll accept
                      </FormDescription>
                    </div>
                    {paymentOptions.map((option) => (
                      <FormField
                        key={option.id}
                        control={control}
                        name="paymentMethods"
                        render={({ field }) => {
                          return (
                            <FormItem
                              key={option.id}
                              className="flex flex-row items-start space-x-3 space-y-0"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(option.id)}
                                  onCheckedChange={(checked) => {
                                    return checked
                                      ? field.onChange([...(field.value || []), option.id])
                                      : field.onChange(
                                          field.value?.filter(
                                            (value) => value !== option.id
                                          )
                                        )
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="font-normal">
                                {option.label}
                              </FormLabel>
                            </FormItem>
                          )
                        }}
                      />
                    ))}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="refundPolicy"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Refund Policy</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your refund policy for cancellations"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          )}
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};

export default TicketingSection;
