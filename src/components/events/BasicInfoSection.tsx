import React from 'react';
import { Control } from "react-hook-form";
import { Info, Upload, X } from 'lucide-react';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from "@/components/ui/enhanced-accordion";
import { EventFormValues } from '@/types/event-form';
import { useCategories } from '@/hooks/useCategories';
import CategoryBadge from '@/components/ui/CategoryBadge';

interface BasicInfoSectionProps {
  control: Control<EventFormValues>;
  coverImageUrl: string;
  additionalImageUrls?: string[];
  onImageSelect: () => void;
  onAdditionalImagesSelect?: () => void;
  onRemoveAdditionalImage?: (index: number) => void;
  errorCount?: number;
  isValid?: boolean;
  showValidation?: boolean;
  completionStatus?: 'untouched' | 'partial' | 'complete' | 'error';
  showProgressIndicators?: boolean;
}

const BasicInfoSection: React.FC<BasicInfoSectionProps> = ({
  control,
  coverImageUrl,
  additionalImageUrls = [],
  onImageSelect,
  onAdditionalImagesSelect,
  onRemoveAdditionalImage,
  errorCount = 0,
  isValid = false,
  showValidation = false,
  completionStatus = 'untouched',
  showProgressIndicators = false
}) => {
  const { activeCategories, loading: loadingCategories } = useCategories();

  return (
    <AccordionItem value="item-1" data-value="item-1">
      <AccordionTrigger
        className="text-lg font-medium"
        errorCount={errorCount}
        isValid={isValid}
        showValidation={showValidation}
        completionStatus={completionStatus}
        showProgressIndicators={showProgressIndicators}
      >
        <div className="flex items-center gap-2">
          <Info className="h-5 w-5" />
          Basic Event Information
        </div>
      </AccordionTrigger>
      <AccordionContent className="pt-4">
        <div className="space-y-4">
          <FormField
            control={control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel required>Event Name</FormLabel>
                <FormControl>
                  <Input placeholder="Give your event a catchy title" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel required>Event Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe your event in detail..."
                    className="min-h-32"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Provide a detailed description of what attendees can expect.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="categoryId"
            render={({ field }) => (
              <FormItem>
                <FormLabel required>Category</FormLabel>
                <Select onValueChange={field.onChange} value={field.value || ''}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {loadingCategories ? (
                      <div className="flex items-center justify-center p-2">
                        <p className="text-sm text-muted-foreground">Loading categories...</p>
                      </div>
                    ) : (
                      activeCategories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          <div className="flex items-center gap-2">
                            <CategoryBadge category={category} className="py-0.5" />
                          </div>
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="tags"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Event Tags</FormLabel>
                <FormControl>
                  <Input
                    placeholder="e.g. music, live, indie, jazz"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Comma-separated tags to help with discoverability
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div>
            <FormLabel className="block mb-2">Event Cover Image</FormLabel>
            <div
              className={cn(
                "border border-dashed border-border rounded-md p-8 text-center cursor-pointer hover:bg-muted/50 transition-colors",
                coverImageUrl && "border-primary"
              )}
              onClick={onImageSelect}
            >
              {coverImageUrl ? (
                <div className="space-y-4">
                  <img
                    src={coverImageUrl}
                    alt="Event cover preview"
                    className="mx-auto max-h-48 object-contain rounded-md"
                  />
                  <p className="text-sm text-muted-foreground">
                    Click to change image
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <Upload className="mx-auto h-12 w-12 text-muted-foreground" />
                  <p className="text-muted-foreground">
                    Drag and drop an image here, or click to select
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Recommended size: 1200 x 630 pixels (16:9 ratio)
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Additional Images Section */}
          <div>
            <FormLabel className="block mb-2">Additional Images (up to 5)</FormLabel>
            <div className="space-y-4">
              {/* Image gallery */}
              {additionalImageUrls.length > 0 && (
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-4 mb-4">
                  {additionalImageUrls.map((url, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={url}
                        alt={`Event image ${index + 1}`}
                        className="h-32 w-full object-cover rounded-md"
                      />
                      {onRemoveAdditionalImage && (
                        <Button
                          type="button"
                          variant="destructive"
                          size="icon"
                          className="absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={(e) => {
                            e.stopPropagation();
                            onRemoveAdditionalImage(index);
                          }}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {/* Add more images button */}
              {additionalImageUrls.length < 5 && onAdditionalImagesSelect && (
                <div
                  className="border border-dashed border-border rounded-md p-4 text-center cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={onAdditionalImagesSelect}
                >
                  <Upload className="mx-auto h-6 w-6 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">
                    Add more images ({5 - additionalImageUrls.length} remaining)
                  </p>
                </div>
              )}
            </div>
            <FormDescription className="mt-2">
              Add up to 5 additional images to showcase more aspects of your event
            </FormDescription>
          </div>
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};

export default BasicInfoSection;
