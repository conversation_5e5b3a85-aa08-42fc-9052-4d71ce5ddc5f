import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { CalendarPlus } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  generateGoogleCalendarUrl,
  generateOutlookCalendarUrl,
  generateYahooCalendarUrl,
  generateICalFileUrl,
} from '@/utils/calendarUtils';

interface CalendarEvent {
  title: string;
  description: string;
  location: string;
  startDate: Date;
  endDate?: Date | null;
  url: string;
}

interface CalendarSelectorProps {
  event: CalendarEvent;
}

const CalendarSelector: React.FC<CalendarSelectorProps> = ({ event }) => {
  const isMobile = useIsMobile();

  const calendarOptions = [
    {
      name: 'Google Calendar',
      icon: '/icons/google-calendar.svg',
      onClick: () => {
        const googleUrl = generateGoogleCalendarUrl(event);
        window.open(googleUrl, '_blank');
      },
    },
    {
      name: 'Outlook Calendar',
      icon: '/icons/outlook-calendar.svg',
      onClick: () => {
        const outlookUrl = generateOutlookCalendarUrl(event);
        window.open(outlookUrl, '_blank');
      },
    },
    {
      name: 'Yahoo Calendar',
      icon: '/icons/yahoo-calendar.svg',
      onClick: () => {
        const yahooUrl = generateYahooCalendarUrl(event);
        window.open(yahooUrl, '_blank');
      },
    },
    {
      name: 'Apple Calendar / iCal',
      icon: '/icons/apple-calendar.svg',
      onClick: () => {
        const icalUrl = generateICalFileUrl(event);

        // Create a temporary link element to trigger the download
        const link = document.createElement('a');
        link.href = icalUrl;
        link.download = `${event.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_event.ics`;
        document.body.appendChild(link);
        link.click();

        // Clean up
        document.body.removeChild(link);
        URL.revokeObjectURL(icalUrl);
      },
    },
  ];

  // Render a calendar option with icon and text
  const renderCalendarOption = (option: typeof calendarOptions[0], className?: string) => (
    <div
      className={`flex items-center ${className || ''}`}
      onClick={option.onClick}
    >
      <img
        src={option.icon}
        alt={option.name}
        className="h-5 w-5 mr-3"
        onError={(e) => {
          e.currentTarget.src = '/icons/calendar-icon.svg';
        }}
      />
      <span>{option.name}</span>
    </div>
  );

  // Mobile version - Bottom Sheet
  if (isMobile) {
    return (
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="ghost" className="w-full text-muted-foreground">
            <CalendarPlus className="h-4 w-4 mr-2" />
            Add to Calendar
          </Button>
        </SheetTrigger>
        <SheetContent side="bottom" className="px-0">
          <SheetHeader className="px-6 pb-2">
            <SheetTitle>Add to Calendar</SheetTitle>
          </SheetHeader>
          <div className="mt-4">
            {calendarOptions.map((option, index) => (
              <Button
                key={option.name}
                variant="ghost"
                className="w-full justify-start px-6 py-4 h-auto text-base font-normal"
                onClick={option.onClick}
              >
                {renderCalendarOption(option)}
              </Button>
            ))}
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  // Desktop version - Dropdown
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="w-full text-muted-foreground">
          <CalendarPlus className="h-4 w-4 mr-2" />
          Add to Calendar
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        {calendarOptions.slice(0, 3).map((option) => (
          <DropdownMenuItem key={option.name} onClick={option.onClick}>
            {renderCalendarOption(option)}
          </DropdownMenuItem>
        ))}
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={calendarOptions[3].onClick}>
          {renderCalendarOption(calendarOptions[3])}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default CalendarSelector;
