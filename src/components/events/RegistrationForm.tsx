import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Users, Check } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Event } from '@/hooks/useEvents';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';

const formSchema = z.object({
  name: z.string(),
  email: z.string().email(),
  phone: z.string().min(10, {
    message: 'Phone number must be at least 10 digits',
  }),
  numGuests: z.string().min(1),
  ticketType: z.string(),
  acceptTerms: z.boolean().refine((value) => value === true, {
    message: "You must accept the terms and conditions.",
  }),
});

type FormValues = z.infer<typeof formSchema>;

interface RegistrationFormProps {
  event: Event;
  isOpen: boolean;
  onClose: () => void;
  onRegistrationComplete?: () => void; // New callback prop
}

const RegistrationForm: React.FC<RegistrationFormProps> = ({
  event,
  isOpen,
  onClose,
  onRegistrationComplete
}) => {
  const { user, userProfile } = useAuth();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [registrationSuccess, setRegistrationSuccess] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: userProfile?.full_name || '',
      email: user?.email || '',
      phone: userProfile?.phone_number || '',
      numGuests: '1',
      ticketType: 'general',
      acceptTerms: false,
    },
  });

  const { watch, setValue, handleSubmit } = form;
  const numGuests = watch('numGuests');
  const ticketType = watch('ticketType');

  // Fetch user profile data including phone number
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!user) return;

      const { data, error } = await supabase
        .from('profiles')
        .select('full_name, email, phone_number')
        .eq('id', user.id)
        .maybeSingle();

      if (error) {
        console.error('Error fetching user profile:', error);
        return;
      }

      if (data) {
        setValue('name', data.full_name || user.user_metadata?.full_name || '');
        setValue('email', user.email || '');
        setValue('phone', data.phone_number || '');
      }
    };

    fetchUserProfile();
  }, [user, setValue]);

  const registerForEvent = async (
    eventId: string,
    registrationData: {
      ticket_type: string;
      quantity: number;
      unit_price: number;
      total_amount: number;
      payment_method: string;
    }
  ) => {
    if (!user) return null;

    try {
      // First update the user's phone number if changed
      if (userProfile?.phone_number !== form.getValues().phone) {
        await supabase
          .from('profiles')
          .update({ phone_number: form.getValues().phone })
          .eq('id', user.id);
      }

      // Then create the registration
      const { data, error } = await supabase
        .from('registrations')
        .insert({
          event_id: eventId,
          user_id: user.id,
          ticket_type: registrationData.ticket_type,
          quantity: registrationData.quantity, 
          unit_price: registrationData.unit_price,
          total_amount: registrationData.total_amount,
          payment_method: registrationData.payment_method,
          registration_date: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error registering for event:', error);
      throw error;
    }
  };

  const onSubmit = async (data: FormValues) => {
    if (!user) {
      toast({
        title: 'Authentication required',
        description: 'Please sign in to register for this event',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Parse numGuests as number to ensure proper calculation
      const guests = parseInt(data.numGuests, 10) || 1;
      const unitPrice = ticketType === 'vip'
        ? (event.vip_ticket_price || 0)
        : (event.general_admission_price || 0);
      const totalPrice = guests * unitPrice;

      await registerForEvent(event.id, {
        ticket_type: data.ticketType,
        quantity: guests,
        unit_price: unitPrice,
        total_amount: totalPrice,
        payment_method: event.is_free ? 'free' : 'pending',
      });

      setRegistrationSuccess(true);
      
      // Show success toast
      toast({
        title: 'Registration successful!',
        description: 'You have successfully registered for this event.',
      });

      // Call the completion callback if provided
      if (onRegistrationComplete) {
        onRegistrationComplete();
      }

      // Close dialog after short delay
      setTimeout(() => {
        setRegistrationSuccess(false);
        onClose();
      }, 3000);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to register for the event';
      toast({
        title: 'Registration failed',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!user) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md md:max-w-lg w-[calc(100%-2rem)] p-0 overflow-hidden">
        {registrationSuccess ? (
          <div className="p-6 flex flex-col items-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <Check className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Registration Successful!</h3>
            <p className="text-center text-muted-foreground mb-6">
              Thank you for registering for {event.title}. We look forward to seeing you!
            </p>
            <Button onClick={onClose} className="w-full">
              Close
            </Button>
          </div>
        ) : (
          <>
            <DialogHeader className="border-b p-6">
              <DialogTitle className="text-xl font-raleway">Register for Event</DialogTitle>
              <DialogDescription>
                Complete your registration for {event.title}
              </DialogDescription>
            </DialogHeader>

            <Form {...form}>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 px-6 py-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input {...field} disabled className="bg-gray-50" />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input {...field} disabled className="bg-gray-50" />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter your phone number" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="numGuests"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Number of Guests</FormLabel>
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <Select
                          value={field.value.toString()}
                          onValueChange={field.onChange}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select number of guests" />
                          </SelectTrigger>
                          <SelectContent>
                            {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                              <SelectItem key={num} value={num.toString()}>
                                {num} {num === 1 ? 'Guest' : 'Guests'}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {!event.is_free && (
                  <FormField
                    control={form.control}
                    name="ticketType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Ticket Type</FormLabel>
                        <Select value={field.value} onValueChange={field.onChange}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select ticket type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="general">
                              General Admission (₹{event.general_admission_price})
                            </SelectItem>
                            {event.vip_ticket_price && (
                              <SelectItem value="vip">
                                VIP Ticket (₹{event.vip_ticket_price})
                              </SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {!event.is_free && (
                  <div className="mt-6 p-4 bg-gray-50 rounded-md">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-500">
                        {parseInt(numGuests, 10) || 1} × {ticketType === "vip" ? "VIP Ticket" : "General Admission"}
                      </span>
                      <span className="text-sm font-medium">
                        ₹{(parseInt(numGuests, 10) || 1) * 
                           (ticketType === "vip" 
                             ? (event.vip_ticket_price || 0) 
                             : (event.general_admission_price || 0))}
                      </span>
                    </div>
                    <div className="border-t my-3 border-gray-200"></div>
                    <div className="flex justify-between items-center">
                      <span className="font-semibold">Total</span>
                      <span className="font-semibold text-blue-primary">
                        ₹{(parseInt(numGuests, 10) || 1) * 
                           (ticketType === "vip" 
                             ? (event.vip_ticket_price || 0) 
                             : (event.general_admission_price || 0))}
                      </span>
                    </div>
                  </div>
                )}

                <FormField
                  control={form.control}
                  name="acceptTerms"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Accept Terms and Conditions</FormLabel>
                        <FormMessage />
                      </div>
                    </FormItem>
                  )}
                />

                <DialogFooter className="flex flex-col sm:flex-row gap-2 mt-6 px-0">
                  <Button 
                    variant="outline" 
                    type="button" 
                    onClick={onClose}
                    className="w-full sm:w-auto order-2 sm:order-1"
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    className="w-full sm:w-auto bg-blue-primary hover:bg-blue-secondary text-white order-1 sm:order-2"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'Registering...' : event.is_free ? 'Register Now' : 'Proceed to Payment'}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default RegistrationForm;
