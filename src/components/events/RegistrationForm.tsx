import React, { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Users, Check } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Event } from '@/hooks/useEvents';
import { useRegistrations } from '@/hooks/useRegistrations';
import { PaymentInstructions } from './PaymentInstructions';
import { PaymentProofUpload } from './PaymentProofUpload';
import { PaymentInstructions as PaymentInstructionsType, PaymentProofFormData } from '@/types/payment';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';

const formSchema = z.object({
  name: z.string(),
  email: z.string().email(),
  phone: z.string().min(10, {
    message: 'Phone number must be at least 10 digits',
  }),
  numGuests: z.string().min(1),
  ticketType: z.string(),
  acceptTerms: z.boolean().refine((value) => value === true, {
    message: "You must accept the terms and conditions.",
  }),
});

type FormValues = z.infer<typeof formSchema>;

interface RegistrationFormProps {
  event: Event;
  isOpen: boolean;
  onClose: () => void;
  onRegistrationComplete?: () => void;
  initialStep?: 'registration' | 'payment' | 'proof';
}

const RegistrationForm: React.FC<RegistrationFormProps> = ({
  event,
  isOpen,
  onClose,
  onRegistrationComplete,
  initialStep = 'registration',
}) => {
  const { user, userProfile: authUserProfile } = useAuth(); // Renamed to avoid conflict
  const { toast } = useToast();
  const { registerForEvent, uploadPaymentProof, getRegistrationDetails } = useRegistrations();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [registrationSuccess, setRegistrationSuccess] = useState(false);
  const [currentStep, setCurrentStep] = useState<'registration' | 'payment' | 'proof'>(initialStep);
  const [registrationId, setRegistrationId] = useState<string | null>(null);
  const [paymentInstructions, setPaymentInstructions] = useState<PaymentInstructionsType | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      numGuests: '1',
      ticketType: 'general',
      acceptTerms: false,
    },
  });

  const { watch, setValue, handleSubmit, reset } = form;

  const preparePaymentInstructions = useCallback((numGuestsParam: string, ticketTypeParam: string): PaymentInstructionsType | null => {
    if (event.is_free) return null;

    const quantity = parseInt(numGuestsParam, 10) || 1;
    const unitPrice = ticketTypeParam === "vip"
      ? (event.vip_ticket_price || 0)
      : (event.general_admission_price || 0);
    const totalAmount = quantity * unitPrice;

    const instructions: PaymentInstructionsType = { eventTitle: event.title, totalAmount: totalAmount };

    if (event.upi_id && event.upi_account_holder_name) {
      instructions.upi = { upiId: event.upi_id, accountHolderName: event.upi_account_holder_name };
    }
    if (event.bank_account_holder_name && event.bank_name && event.bank_account_number && event.bank_ifsc_code) {
      instructions.netBanking = {
        accountHolderName: event.bank_account_holder_name,
        bankName: event.bank_name,
        accountNumber: event.bank_account_number,
        ifscCode: event.bank_ifsc_code,
      };
    }
    if (event.payment_reference_message) {
      instructions.referenceMessage = event.payment_reference_message;
    }

    const hasPaymentMethod = instructions.upi || instructions.netBanking;
    return hasPaymentMethod ? instructions : null;
  }, [event]);

  useEffect(() => {
    if (isOpen) {
      setCurrentStep(initialStep);
      setRegistrationSuccess(false);
      setIsSubmitting(false);

      const fetchInitialData = async () => {
        if (!user) { // Should not happen if EventDetail guards properly
            onClose();
            return;
        }

        // Fetch user profile from Supabase, not relying on potentially stale authUserProfile
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('full_name, phone_number') // email is from user.email
          .eq('id', user.id)
          .maybeSingle();

        let formName = user.user_metadata?.full_name || '';
        let formEmail = user.email || '';
        let formPhone = '';
        let formNumGuests = '1';
        let formTicketType = 'general';

        if (profileError) {
          console.error('Error fetching user profile for form:', profileError);
        } else if (profileData) {
          formName = profileData.full_name || formName;
          formPhone = profileData.phone_number || '';
        }

        reset({
          name: formName,
          email: formEmail,
          phone: formPhone,
          numGuests: formNumGuests,
          ticketType: formTicketType,
          acceptTerms: false,
        });

        if ((initialStep === 'payment' || initialStep === 'proof') && event.id) {
          const existingRegistration = await getRegistrationDetails(event.id);
          if (existingRegistration) {
            setRegistrationId(existingRegistration.id);
            formNumGuests = existingRegistration.quantity.toString();
            formTicketType = existingRegistration.ticket_type;

            setValue('numGuests', formNumGuests);
            setValue('ticketType', formTicketType);

            if (!event.is_free) {
              const instructions = preparePaymentInstructions(formNumGuests, formTicketType);
              setPaymentInstructions(instructions);
              if (initialStep === 'payment' && !instructions) {
                 toast({ title: 'Error', description: 'Could not load payment details for your registration.', variant: 'destructive' });
                 onClose();
                 return;
              }
            }
          } else if (initialStep !== 'registration') {
            toast({ title: 'Registration Not Found', description: 'Could not find your existing registration. Please register first.', variant: 'destructive' });
            setCurrentStep('registration');
          }
        } else if (initialStep === 'registration' && !event.is_free) {
            // For new registrations for paid events, pre-calculate payment instructions
            const instructions = preparePaymentInstructions(formNumGuests, formTicketType);
            setPaymentInstructions(instructions);
        }
      };
      fetchInitialData();
    }
  }, [isOpen, initialStep, user, event.id, event.is_free, getRegistrationDetails, reset, setValue, toast, onClose, preparePaymentInstructions]);

  const watchedNumGuests = watch('numGuests');
  const watchedTicketType = watch('ticketType');

  useEffect(() => {
    // Update payment instructions if guest count/ticket type changes during new registration for paid event
    if (isOpen && currentStep === 'registration' && !event.is_free) {
        const instructions = preparePaymentInstructions(watchedNumGuests, watchedTicketType);
        setPaymentInstructions(instructions);
    }
  }, [isOpen, currentStep, watchedNumGuests, watchedTicketType, event.is_free, preparePaymentInstructions]);

  const onSubmit = async (data: FormValues) => {
    if (!user) {
      toast({ title: 'Authentication required', description: 'Please sign in.', variant: 'destructive' });
      return;
    }

    if (currentStep !== 'registration') {
        // This form submission is only for the registration step.
        // Payment/Proof steps are handled by their own component interactions.
        // If user is in payment/proof step and this is triggered, it's likely an issue.
        // We can try to gracefully guide them.
        if (registrationId && !event.is_free && paymentInstructions) {
            setCurrentStep('payment');
        } else if (registrationId && !event.is_free && !paymentInstructions) {
            // Attempt to re-prepare instructions
            const currentInstructions = preparePaymentInstructions(data.numGuests, data.ticketType);
            if(currentInstructions) {
                setPaymentInstructions(currentInstructions);
                setCurrentStep('payment');
            } else {
                toast({ title: 'Error', description: 'Payment details missing.', variant: 'destructive' });
            }
        }
        return;
    }

    setIsSubmitting(true);
    try {
      // Update user's phone number if changed (check against authUserProfile which is less likely to be stale than form's initial)
      const currentProfile = authUserProfile || (await supabase.from('profiles').select('phone_number').eq('id', user.id).single()).data;
      if (currentProfile && currentProfile.phone_number !== data.phone) {
        await supabase.from('profiles').update({ phone_number: data.phone }).eq('id', user.id);
      }

      const guests = parseInt(data.numGuests, 10) || 1;
      const unitPrice = data.ticketType === 'vip' ? (event.vip_ticket_price || 0) : (event.general_admission_price || 0);

      const registration = await registerForEvent(event.id, data.ticketType, guests, unitPrice);

      if (registration) {
        setRegistrationId(registration.id);
        if (event.is_free) {
          setRegistrationSuccess(true);
          toast({ title: 'Registration successful!', description: 'You have successfully registered.' });
          if (onRegistrationComplete) onRegistrationComplete();
          setTimeout(onClose, 3000);
        } else {
          // For paid events, instructions should be ready from useEffect.
          // Ensure they are set one last time with submitted data if somehow different.
          const finalInstructions = preparePaymentInstructions(data.numGuests, data.ticketType);
          if (!finalInstructions) {
            toast({ title: 'Payment Configuration Missing', description: 'Contact organizer.', variant: 'destructive' });
          } else {
            setPaymentInstructions(finalInstructions);
            setCurrentStep('payment');
          }
        }
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to register.';
      toast({ title: 'Registration failed', description: errorMessage, variant: 'destructive' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePaymentProofUpload = async (proofData: PaymentProofFormData) => {
    if (!registrationId) {
      toast({ title: 'Error', description: 'Registration ID missing.', variant: 'destructive' });
      return;
    }
    if (!proofData.paymentProofFile) {
      toast({ title: 'Error', description: 'Please select a payment proof file.', variant: 'destructive' });
      return;
    }
    setIsSubmitting(true);
    try {
      await uploadPaymentProof(registrationId, proofData.paymentProofFile, proofData.transactionRef);
      setRegistrationSuccess(true);
      toast({ title: 'Payment completed!', description: 'Your registration is now awaiting confirmation.' });
      if (onRegistrationComplete) onRegistrationComplete();
      // Close modal and navigate back to event details
      onClose();
    } catch (error) {
      toast({ title: 'Upload failed', description: 'Please try again.', variant: 'destructive' });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;
  if (!user && isOpen) { // Extra guard
    onClose();
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md md:max-w-lg w-[calc(100%-2rem)] p-0 overflow-hidden">
        {registrationSuccess ? (
          <div className="p-6 flex flex-col items-center">
            <Check className="h-16 w-16 text-green-600 mb-4" /> {/* Simplified success icon */}
            <h3 className="text-xl font-semibold mb-2">Registration Successful!</h3>
            <p className="text-center text-muted-foreground mb-6">
              Thank you for registering for {event.title}.
              {event.is_free || currentStep === 'proof' ? " We look forward to seeing you!" : " Please proceed with payment."}
            </p>
            <Button onClick={onClose} className="w-full">Close</Button>
          </div>
        ) : (
          <>
            <DialogHeader className="border-b p-6">
              <DialogTitle className="text-xl font-raleway">
                {currentStep === 'registration' && 'Register for Event'}
                {currentStep === 'payment' && 'Payment Instructions'}
                {currentStep === 'proof' && 'Upload Payment Proof'}
              </DialogTitle>
              <DialogDescription>
                {currentStep === 'registration' && `Complete your registration for ${event.title}`}
                {currentStep === 'payment' && 'Please make payment using the details below.'}
                {currentStep === 'proof' && 'Upload your payment proof to complete registration.'}
              </DialogDescription>
            </DialogHeader>

            {currentStep === 'registration' && (
              <Form {...form}>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 px-6 py-4">
                <FormField control={form.control} name="name" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl><Input {...field} disabled className="bg-gray-50" /></FormControl>
                    </FormItem>
                  )}
                />
                <FormField control={form.control} name="email" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl><Input {...field} disabled className="bg-gray-50" /></FormControl>
                    </FormItem>
                  )}
                />
                <FormField control={form.control} name="phone" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl><Input {...field} placeholder="Enter your phone number" /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField control={form.control} name="numGuests" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Number of Guests</FormLabel>
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <Select value={field.value || '1'} onValueChange={field.onChange}>
                          <SelectTrigger className="w-full"><SelectValue placeholder="Select number of guests" /></SelectTrigger>
                          <SelectContent>{[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                              <SelectItem key={num} value={num.toString()}>{num} {num === 1 ? 'Guest' : 'Guests'}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {!event.is_free && (
                  <FormField control={form.control} name="ticketType" render={({ field }) => (
                      <FormItem>
                        <FormLabel>Ticket Type</FormLabel>
                        <Select value={field.value || 'general'} onValueChange={field.onChange}>
                          <SelectTrigger><SelectValue placeholder="Select ticket type" /></SelectTrigger>
                          <SelectContent>
                            <SelectItem value="general">General Admission (₹{event.general_admission_price})</SelectItem>
                            {event.vip_ticket_price && <SelectItem value="vip">VIP Ticket (₹{event.vip_ticket_price})</SelectItem>}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
                {!event.is_free && paymentInstructions && ( // Display total if instructions are ready
                  <div className="mt-6 p-4 bg-gray-50 rounded-md">
                    <div className="flex justify-between items-center font-semibold">
                      <span>Total</span>
                      <span className="text-blue-primary">₹{paymentInstructions.totalAmount}</span>
                    </div>
                  </div>
                )}
                <FormField control={form.control} name="acceptTerms" render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl><Checkbox checked={field.value} onCheckedChange={field.onChange} /></FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Accept Terms and Conditions</FormLabel><FormMessage />
                      </div>
                    </FormItem>
                  )}
                />
                <DialogFooter className="flex flex-col sm:flex-row gap-2 mt-6 px-0">
                  <Button variant="outline" type="button" onClick={onClose} className="w-full sm:w-auto order-2 sm:order-1">Cancel</Button>
                  <Button type="submit" className="w-full sm:w-auto bg-blue-primary hover:bg-blue-secondary text-white order-1 sm:order-2" disabled={isSubmitting}>
                    {isSubmitting ? 'Processing...' : event.is_free ? 'Register Now' : (paymentInstructions ? 'Proceed to Payment' : 'Calculating...')}
                  </Button>
                </DialogFooter>
                </form>
              </Form>
            )}

            {currentStep === 'payment' && paymentInstructions && (
              <div className="px-6 py-4">
                <PaymentInstructions instructions={paymentInstructions} onPaymentCompleted={() => setCurrentStep('proof')} />
              </div>
            )}
            {currentStep === 'payment' && !paymentInstructions && !event.is_free && (
                 <div className="p-6 text-center">
                    <p className="text-red-500">Payment instructions could not be loaded. Please contact the event organizer.</p>
                    <Button onClick={onClose} className="mt-4">Close</Button>
                 </div>
            )}
            {currentStep === 'proof' && registrationId && (
              <div className="px-6 py-4">
                <PaymentProofUpload
                  onSubmit={handlePaymentProofUpload}
                  onCancel={() => {
                    if (!event.is_free && paymentInstructions) setCurrentStep('payment');
                    else if (!event.is_free && !paymentInstructions) { // Attempt to reload instructions
                        const reloadedInstructions = preparePaymentInstructions(watch('numGuests'), watch('ticketType'));
                        if(reloadedInstructions) {
                            setPaymentInstructions(reloadedInstructions);
                            setCurrentStep('payment');
                        } else {
                            toast({title: "Error", description: "Payment details missing.", variant: "destructive"});
                            onClose();
                        }
                    } else setCurrentStep('registration');
                  }}
                  isSubmitting={isSubmitting}
                />
              </div>
            )}
             {currentStep === 'proof' && !registrationId && (
                 <div className="p-6 text-center">
                    <p className="text-red-500">Registration details missing. Cannot upload proof.</p>
                    <Button onClick={() => setCurrentStep('registration')} className="mt-4">Back to Registration</Button>
                 </div>
            )}
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default RegistrationForm;
