import React, { useState, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Upload, 
  FileImage, 
  X, 
  CheckCircle, 
  AlertCircle,
  Loader2
} from 'lucide-react';
import { PaymentProofFormData } from '@/types/payment';

interface PaymentProofUploadProps {
  onSubmit: (data: PaymentProofFormData) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

export const PaymentProofUpload: React.FC<PaymentProofUploadProps> = ({
  onSubmit,
  onCancel,
  isSubmitting = false
}) => {
  const [formData, setFormData] = useState<PaymentProofFormData>({
    transactionRef: ''
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      setErrors(prev => ({
        ...prev,
        file: 'Please select a valid image file (JPEG, PNG, WebP) or PDF'
      }));
      return;
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      setErrors(prev => ({
        ...prev,
        file: 'File size must be less than 5MB'
      }));
      return;
    }

    setSelectedFile(file);
    setFormData(prev => ({ ...prev, paymentProofFile: file }));
    
    // Clear any previous errors
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors.file;
      return newErrors;
    });

    // Create preview for images
    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setPreviewUrl(null);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    setFormData(prev => ({ ...prev, paymentProofFile: undefined }));
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleTransactionRefChange = (value: string) => {
    setFormData(prev => ({ ...prev, transactionRef: value }));
    
    // Clear transaction ref error if user starts typing
    if (errors.transactionRef) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.transactionRef;
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!selectedFile) {
      newErrors.file = 'Please select a payment proof file';
    }

    if (!formData.transactionRef.trim()) {
      newErrors.transactionRef = 'Transaction reference is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    // Include the selected file in the form data
    const submitData: PaymentProofFormData = {
      ...formData,
      paymentProofFile: selectedFile || undefined
    };

    try {
      await onSubmit(submitData);
      // Reset form on success
      setSelectedFile(null);
      setPreviewUrl(null);
      setFormData({ transactionRef: '' });
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      // Error handling is done in the parent component
      console.error('Payment proof upload error:', error);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Complete Payment
        </CardTitle>
        <div className="text-sm text-muted-foreground">
          <p>Upload your payment proof to complete the registration process</p>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* File Upload Section */}
          <div className="space-y-4">
            <Label htmlFor="payment-proof">Payment Screenshot/Receipt *</Label>
            
            {!selectedFile ? (
              <div 
                className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors"
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-lg font-medium mb-2">Upload Payment Proof</p>
                <p className="text-sm text-muted-foreground mb-4">
                  Click to select or drag and drop your payment screenshot
                </p>
                <p className="text-xs text-muted-foreground">
                  Supported formats: JPEG, PNG, WebP, PDF (Max 5MB)
                </p>
              </div>
            ) : (
              <div className="border rounded-lg p-4">
                <div className="flex items-start gap-4">
                  {previewUrl ? (
                    <img 
                      src={previewUrl} 
                      alt="Payment proof preview" 
                      className="w-24 h-24 object-cover rounded border"
                    />
                  ) : (
                    <div className="w-24 h-24 bg-muted rounded border flex items-center justify-center">
                      <FileImage className="h-8 w-8 text-muted-foreground" />
                    </div>
                  )}
                  
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">{selectedFile.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                    <div className="flex items-center gap-2 mt-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm text-green-600">File selected</span>
                    </div>
                  </div>
                  
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleRemoveFile}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}

            <input
              ref={fileInputRef}
              type="file"
              accept="image/*,.pdf"
              onChange={handleFileSelect}
              className="hidden"
            />

            {errors.file && (
              <div className="flex items-center gap-2 text-sm text-destructive">
                <AlertCircle className="h-4 w-4" />
                {errors.file}
              </div>
            )}
          </div>

          {/* Transaction Reference */}
          <div className="space-y-2">
            <Label htmlFor="transaction-ref">Transaction Reference Number *</Label>
            <Input
              id="transaction-ref"
              placeholder="e.g., TXN123456789, UPI Ref: 123456789012"
              value={formData.transactionRef}
              onChange={(e) => handleTransactionRefChange(e.target.value)}
              className={errors.transactionRef ? 'border-destructive' : ''}
            />
            <p className="text-sm text-muted-foreground">
              Enter the transaction ID, UPI reference number, or any reference from your payment
            </p>
            {errors.transactionRef && (
              <div className="flex items-center gap-2 text-sm text-destructive">
                <AlertCircle className="h-4 w-4" />
                {errors.transactionRef}
              </div>
            )}
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">What happens next?</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>Your payment proof will be sent to the event organizer</li>
                  <li>The organizer will verify your payment and approve your registration</li>
                  <li>You'll receive a confirmation once your registration is approved</li>
                  <li>This process usually takes 1-2 business days</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !selectedFile}
              className="flex-1"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Completing...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Complete Payment
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
