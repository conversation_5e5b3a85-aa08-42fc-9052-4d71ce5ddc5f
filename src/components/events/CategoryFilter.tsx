
import React from 'react';
import { Check, ChevronDown } from 'lucide-react';
import { Button } from '../ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';

interface CategoryFilterProps {
  categories: Array<{
    id: string;
    name: string;
    color?: string;
  }> | null;
  selectedCategory: string | null;
  onCategoryChange: (categoryId: string | null) => void;
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  categories,
  selectedCategory,
  onCategoryChange,
}) => {
  const handleSelectCategory = (categoryId: string | null) => {
    onCategoryChange(categoryId);
  };

  const selectedCategoryName = selectedCategory
    ? categories?.find((cat) => cat.id === selectedCategory)?.name
    : null;

  return (
    <div className="w-full md:w-auto">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="w-full md:w-auto justify-between">
            {selectedCategoryName || 'All Categories'}
            <ChevronDown className="ml-2 h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          <DropdownMenuItem
            className="flex items-center justify-between"
            onClick={() => handleSelectCategory(null)}
          >
            <span>All Categories</span>
            {!selectedCategory && <Check className="h-4 w-4" />}
          </DropdownMenuItem>
          {categories?.map((category) => (
            <DropdownMenuItem
              key={category.id}
              className="flex items-center justify-between"
              onClick={() => handleSelectCategory(category.id)}
            >
              <span>{category.name}</span>
              {selectedCategory === category.id && <Check className="h-4 w-4" />}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default CategoryFilter;
