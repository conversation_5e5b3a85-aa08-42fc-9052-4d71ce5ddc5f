import React from 'react';
import { Control, useFormContext } from 'react-hook-form';
import { FileText } from 'lucide-react';

import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/enhanced-accordion';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { EventFormValues } from '@/types/event-form'; // Assuming this path is correct

interface OrganizerTermsSectionProps {
  control: Control<EventFormValues>;
  errorCount?: number;
  isValid?: boolean;
  showValidation?: boolean;
  completionStatus?: 'untouched' | 'partial' | 'complete' | 'error';
  showProgressIndicators?: boolean;
}

const OrganizerTermsSection: React.FC<OrganizerTermsSectionProps> = ({
  control,
  errorCount,
  isValid,
  showValidation,
  completionStatus,
  showProgressIndicators,
}) => {
  const { watch } = useFormContext<EventFormValues>();
  const organizerTermsEnabled = watch('organizerTermsEnabled');

  return (
    <AccordionItem value="item-6" data-value="item-6">
      <AccordionTrigger
        className="text-lg font-medium"
        errorCount={errorCount}
        isValid={isValid}
        showValidation={showValidation}
        completionStatus={completionStatus}
        showProgressIndicators={showProgressIndicators}
      >
        {/* Children of the trigger - Icon and Title */}
        <div className="flex items-center gap-3">
          <FileText className="h-6 w-6 text-gray-600" />
          <h3 className="text-lg font-semibold text-left">Organizer's Terms and Conditions</h3>
        </div>
      </AccordionTrigger>
      <AccordionContent className="pt-4 pb-2 px-1 space-y-6">
        <FormField
          control={control}
          name="organizerTermsEnabled"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Enable Organizer's Terms and Conditions</FormLabel>
                <FormDescription>
                  As an organizer, I have specific terms and conditions for my attendees (e.g., refund policy, entry rules, safety protocols). This should be displayed to attendees during registration.
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        {organizerTermsEnabled && (
          <FormField
            control={control}
            name="organizerTerms"
            render={({ field }) => (
              <FormItem>
                <FormLabel required={organizerTermsEnabled}>
                  Terms and Conditions
                </FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Please enter the term and conditions here."
                    {...field}
                    // No maxLength attribute here, Zod handles it.
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}
      </AccordionContent>
    </AccordionItem>
  );
};

export default OrganizerTermsSection;
