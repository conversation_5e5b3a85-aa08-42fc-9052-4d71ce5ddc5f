import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { format } from 'date-fns';
import {
  Calendar,
  Clock,
  MapPin,
  User,
  Mail,
  Share2,
  ChevronLeft,
  ChevronRight,
  AlertTriangle,
  Check,
  LogIn,
  Copy,
  Building2
} from 'lucide-react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import CategoryBadge from '@/components/ui/CategoryBadge';
import { useEvents, Event } from '@/hooks/useEvents';
import { useAuth } from '@/context/AuthContext';
import RegistrationForm from './RegistrationForm';
import { useToast } from '@/hooks/use-toast';
import { shareEvent, copyEventUrl, isWebShareSupported } from '@/utils/shareUtils';
import CalendarSelector from './CalendarSelector';
import { useEventAnalytics } from '@/hooks/useAnalytics';

const EventDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isRegistrationOpen, setIsRegistrationOpen] = useState(false);
  const [isUserRegistered, setIsUserRegistered] = useState(false);
  const { getEventById, checkUserRegistration } = useEvents();
  const { user } = useAuth();
  const { toast } = useToast();
  const { trackEventView, trackEventRegister, trackEventShare } = useEventAnalytics();

  useEffect(() => {
    // Exit early if no ID is provided
    if (!id) return;

    const loadEvent = async () => {
      setLoading(true);
      const eventData = await getEventById(id);
      setEvent(eventData);
      setLoading(false);

      // Track event view when event data is loaded
      if (eventData) {
        trackEventView(eventData.id, eventData.title);
      }

      // Check if user is registered for this event
      if (user && eventData) {
        const registered = await checkUserRegistration(id);
        setIsUserRegistered(registered);
      }
    };

    loadEvent();
    // Include checkUserRegistration in dependency array
  }, [id, getEventById, checkUserRegistration, user]);

  const getAllImages = () => {
    if (!event) return [];

    const images = [];
    if (event.image_url) images.push(event.image_url);

    if (event.additional_images && Array.isArray(event.additional_images)) {
      images.push(...event.additional_images);
    }

    return images;
  };

  const allImages = getAllImages();

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % allImages.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + allImages.length) % allImages.length);
  };

  const handleRegisterClick = () => {
    if (!user) {
      // Redirect to login if not authenticated without showing error toast
      window.location.href = '/auth?redirect=' + encodeURIComponent(window.location.pathname);
      return;
    }

    // If user is already registered, don't open registration form
    if (isUserRegistered) {
      return;
    }

    // Track registration attempt
    if (event) {
      trackEventRegister(event.id, event.title);
    }

    setIsRegistrationOpen(true);
  };

  const onRegistrationComplete = async () => {
    // Update the registration status after successful registration
    if (id) {
      const registered = await checkUserRegistration(id);
      setIsUserRegistered(registered);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 md:px-6 pt-24 pb-16">
        <div className="max-w-5xl mx-auto">
          <Skeleton className="h-96 w-full rounded-2xl" />
          <div className="mt-8 space-y-4">
            <Skeleton className="h-12 w-3/4" />
            <Skeleton className="h-6 w-1/2" />
            <Skeleton className="h-6 w-1/3" />
            <div className="mt-6 space-y-4">
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="container mx-auto px-4 md:px-6 pt-24 pb-16">
        <div className="max-w-5xl mx-auto text-center py-12">
          <h2 className="text-2xl font-semibold mb-4">Event Not Found</h2>
          <p className="text-muted-foreground mb-6">The event you're looking for doesn't exist or has been removed.</p>
          <Button asChild>
            <Link to="/events">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back to Events
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  const eventDate = new Date(event.start_date);
  const formattedDate = format(eventDate, 'EEEE, MMMM d, yyyy');
  const formattedTime = format(eventDate, 'h:mm a');

  // Format end date if it exists
  let endDateDisplay = null;
  if (event.end_date) {
    const endDate = new Date(event.end_date);
    if (event.is_multi_day) {
      endDateDisplay = format(endDate, 'EEEE, MMMM d, yyyy');
    } else {
      endDateDisplay = format(endDate, 'h:mm a');
    }
  }

  // Calculate if the event is upcoming or past
  const isUpcoming = new Date(event.start_date) > new Date();

  // Location is displayed in the UI directly from the venue object

  // Get tags
  const tags = event.tags || [];

  // Check if event is pending or rejected
  const isPending = event.approval_status === 'pending';
  const isRejected = event.approval_status === 'rejected';

  return (
    <div className="container mx-auto px-4 md:px-6 pt-24 pb-16">
      <div className="max-w-5xl mx-auto">
        {/* Back button */}
        <div className="mb-6">
          <Button variant="ghost" asChild>
            <Link to="/events" className="flex items-center text-muted-foreground hover:text-foreground">
              <ChevronLeft className="mr-1 h-4 w-4" />
              Back to Events
            </Link>
          </Button>
        </div>

        {/* Approval Status Alert */}
        {(isPending || isRejected) && (
          <Alert variant={isRejected ? "destructive" : "default"} className="mb-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>
              {isPending ? "Pending Approval" : "Event Rejected"}
            </AlertTitle>
            <AlertDescription>
              {isPending
                ? "This event is currently awaiting approval from an administrator."
                : `This event has been rejected. Reason: ${event.approval_notes || "No reason provided."}`
              }
            </AlertDescription>
          </Alert>
        )}

        {/* Event Header */}
        <div className="relative">
          <div className="relative h-96 w-full rounded-2xl overflow-hidden mb-8">
            <div className="absolute inset-0 bg-gradient-to-b from-black/30 to-black/60 z-10" />

            {/* Image gallery */}
            {allImages.length > 0 ? (
              <>
                <img
                  src={allImages[currentImageIndex]}
                  alt={event.title}
                  className="w-full h-full object-cover transition-opacity duration-300"
                  onError={(e) => {
                    e.currentTarget.src = '/placeholder-event.jpg';
                  }}
                />

                {/* Navigation arrows for multiple images */}
                {allImages.length > 1 && (
                  <>
                    <Button
                      onClick={prevImage}
                      variant="ghost"
                      size="icon"
                      className="absolute left-4 top-1/2 -translate-y-1/2 z-20 bg-black/40 text-white hover:bg-black/60 rounded-full"
                    >
                      <ChevronLeft className="h-6 w-6" />
                    </Button>
                    <Button
                      onClick={nextImage}
                      variant="ghost"
                      size="icon"
                      className="absolute right-4 top-1/2 -translate-y-1/2 z-20 bg-black/40 text-white hover:bg-black/60 rounded-full"
                    >
                      <ChevronRight className="h-6 w-6" />
                    </Button>

                    {/* Image counter */}
                    <div className="absolute bottom-4 right-4 z-20 bg-black/60 text-white px-3 py-1 rounded-full text-sm">
                      {currentImageIndex + 1} / {allImages.length}
                    </div>
                  </>
                )}
              </>
            ) : (
              <img
                src="/placeholder-event.jpg"
                alt={event.title}
                className="w-full h-full object-cover"
              />
            )}

            <div className="absolute top-4 left-4 z-20">
              <CategoryBadge category={event.category} />
            </div>

            <div className="absolute bottom-6 left-6 right-6 z-20">
              <h1 className="text-4xl font-bold text-white mb-2 font-raleway">{event.title}</h1>
              <div className="flex flex-wrap items-center gap-3 text-white">
                <div className="flex items-center">
                  <Calendar className="mr-2 h-4 w-4" />
                  <span>{formattedDate}</span>
                </div>

                {/* Status badges */}
                {isPending && (
                  <Badge variant="outline" className="bg-yellow-500/80 text-white border-yellow-600 font-semibold shadow-sm">
                    Pending Approval
                  </Badge>
                )}
                {isRejected && (
                  <Badge variant="destructive">
                    Rejected
                  </Badge>
                )}
                {!isUpcoming && (
                  <Badge variant="secondary">Past Event</Badge>
                )}
              </div>
            </div>
          </div>

          {/* Thumbnail gallery */}
          {allImages.length > 1 && (
            <div className="flex space-x-2 mb-8 overflow-x-auto pb-2">
              {allImages.map((img, idx) => (
                <button
                  key={idx}
                  onClick={() => setCurrentImageIndex(idx)}
                  className={`relative flex-shrink-0 h-16 w-24 rounded-md overflow-hidden border-2 transition-all ${
                    currentImageIndex === idx ? "border-blue-primary" : "border-transparent"
                  }`}
                >
                  <img
                    src={img}
                    alt={`${event.title} - image ${idx + 1}`}
                    className="h-full w-full object-cover"
                    onError={(e) => {
                      e.currentTarget.src = '/placeholder-event.jpg';
                    }}
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Event Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 order-2 lg:order-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <section className="mb-8">
                <h2 className="text-2xl font-semibold mb-4 font-raleway">About This Event</h2>
                <div className="prose prose-blue max-w-none">
                  <p>{event.description}</p>
                </div>
              </section>

              {tags && tags.length > 0 && (
                <section className="mb-8">
                  <h2 className="text-xl font-semibold mb-3 font-raleway">Tags</h2>
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag, index) => (
                      <Badge key={index} variant="outline">{tag}</Badge>
                    ))}
                  </div>
                </section>
              )}

              <section className="mb-8">
                <h2 className="text-xl font-semibold mb-3 font-raleway">Location</h2>
                <div className="glass-card p-4 rounded-xl">
                  <div className="flex items-start">
                    <MapPin className="h-5 w-5 text-blue-primary mt-0.5 mr-3 flex-shrink-0" />
                    <div>
                      <h3 className="font-medium">{event.venue?.name}</h3>
                      <p className="text-muted-foreground">{event.venue?.address}</p>
                      <p className="text-muted-foreground">{event.venue?.city}, {event.venue?.state}</p>
                    </div>
                  </div>
                </div>
              </section>

              <section>
                <h2 className="text-xl font-semibold mb-3 font-raleway">
                  {event.hosted_by_type === 'organization' ? 'Hosted by' : 'Organizer'}
                </h2>
                <div className="glass-card p-4 rounded-xl">
                  {event.hosted_by_type === 'organization' && event.organization ? (
                    <div className="flex items-start">
                      {event.organization.logo_url ? (
                        <img
                          src={event.organization.logo_url}
                          alt={`${event.organization.name} logo`}
                          className="w-12 h-12 rounded-lg object-cover mt-0.5 mr-3 flex-shrink-0"
                        />
                      ) : (
                        <Building2 className="h-5 w-5 text-blue-primary mt-0.5 mr-3 flex-shrink-0" />
                      )}
                      <div>
                        <h3 className="font-medium">{event.organization.name}</h3>
                        <p className="text-sm text-muted-foreground">Organization</p>
                        {event.organization.contact_email && (
                          <div className="flex items-center mt-2 text-sm text-muted-foreground">
                            <Mail className="h-4 w-4 mr-2" />
                            <a href={`mailto:${event.organization.contact_email}`} className="hover:text-blue-primary">
                              {event.organization.contact_email}
                            </a>
                          </div>
                        )}
                        {event.organization.website_url && (
                          <div className="flex items-center mt-1 text-sm text-muted-foreground">
                            <Building2 className="h-4 w-4 mr-2" />
                            <a
                              href={event.organization.website_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="hover:text-blue-primary"
                            >
                              Visit Website
                            </a>
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-start">
                      <User className="h-5 w-5 text-blue-primary mt-0.5 mr-3 flex-shrink-0" />
                      <div>
                        <h3 className="font-medium">{event.organizer_name}</h3>
                        {event.organizer_email && (
                          <div className="flex items-center mt-2 text-sm text-muted-foreground">
                            <Mail className="h-4 w-4 mr-2" />
                            <a href={`mailto:${event.organizer_email}`} className="hover:text-blue-primary">
                              {event.organizer_email}
                            </a>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </section>
            </motion.div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 order-1 lg:order-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <div className="glass-card p-5 rounded-xl space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-3 font-raleway">Date and Time</h3>
                  <div className="space-y-2">
                    <div className="flex items-center text-sm">
                      <Calendar className="h-4 w-4 text-blue-primary mr-2" />
                      <span>{formattedDate}</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Clock className="h-4 w-4 text-blue-primary mr-2" />
                      <span>{formattedTime}</span>
                      {endDateDisplay && (
                        <span> - {endDateDisplay}</span>
                      )}
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-3 font-raleway">Price</h3>
                  <div className="text-lg font-medium">
                    {event.is_free ? (
                      <span className="text-green-600">Free</span>
                    ) : (
                      <span>From ₹{event.general_admission_price || '0'}</span>
                    )}
                  </div>
                </div>

                <div className="pt-2">
                  {isUpcoming && event.approval_status === 'approved' ? (
                    isUserRegistered ? (
                      <Button
                        disabled
                        className="w-full bg-green-500 text-white hover:bg-green-500 cursor-not-allowed"
                      >
                        <Check className="mr-2 h-4 w-4" />
                        Already Registered
                      </Button>
                    ) : user ? (
                      <Button
                        className="w-full bg-blue-primary hover:bg-blue-secondary text-white"
                        onClick={handleRegisterClick}
                      >
                        Register Now
                      </Button>
                    ) : (
                      <Button
                        className="w-full bg-blue-primary hover:bg-blue-secondary text-white"
                        onClick={handleRegisterClick}
                      >
                        <LogIn className="mr-2 h-4 w-4" />
                        Sign in to Register
                      </Button>
                    )
                  ) : isPending ? (
                    <Button disabled className="w-full">Awaiting Approval</Button>
                  ) : isRejected ? (
                    <Button disabled className="w-full">Event Rejected</Button>
                  ) : (
                    <Button disabled className="w-full">Event has ended</Button>
                  )}
                </div>

                <div className="pt-2">
                  {isWebShareSupported() ? (
                    <Button
                      variant="outline"
                      className="w-full"
                      disabled={isPending || isRejected}
                      onClick={() => {
                        if (!event) return;

                        // Check if event is approved
                        if (isPending || isRejected) {
                          toast({
                            title: "Event not approved",
                            description: "This event needs to be approved by an admin before it can be shared.",
                            variant: "destructive"
                          });
                          return;
                        }

                        const eventUrl = window.location.href;
                        const description = `Check out this event: ${event.title} on ${formattedDate}`;

                        // Track event share
                        trackEventShare(event.id, event.title);

                        // Pass the poster image filename (not the URL) - the shareEvent function
                        // will download the file from Supabase storage and create a File object
                        shareEvent(
                          event.title,
                          description,
                          eventUrl,
                          event.poster_image_filename // Just the filename, not the full URL
                        );
                      }}
                    >
                      <Share2 className="h-4 w-4 mr-2" />
                      Share Event Poster
                    </Button>
                  ) : (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full"
                          disabled={isPending || isRejected}
                        >
                          <Share2 className="h-4 w-4 mr-2" />
                          Share Event Poster
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem
                          onClick={() => {
                            if (!event) return;

                            // Check if event is approved
                            if (isPending || isRejected) {
                              toast({
                                title: "Event not approved",
                                description: "This event needs to be approved by an admin before it can be shared.",
                                variant: "destructive"
                              });
                              return;
                            }

                            // Track event share (copy link)
                            trackEventShare(event.id, event.title);

                            copyEventUrl(window.location.href);
                          }}
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          Copy Link
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>

                {isUpcoming && event.approval_status === 'approved' && (
                  <CalendarSelector
                    event={{
                      title: event.title,
                      description: event.description,
                      location: event.venue ?
                        `${event.venue.name}, ${event.venue.address}, ${event.venue.city}, ${event.venue.state}` :
                        'Location TBD',
                      startDate: new Date(event.start_date),
                      endDate: event.end_date ? new Date(event.end_date) : null,
                      url: window.location.href
                    }}
                  />
                )}
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Registration Dialog */}
      {event && (
        <RegistrationForm
          event={event}
          isOpen={isRegistrationOpen}
          onClose={() => setIsRegistrationOpen(false)}
          onRegistrationComplete={onRegistrationComplete}
        />
      )}
    </div>
  );
};

export default EventDetail;
