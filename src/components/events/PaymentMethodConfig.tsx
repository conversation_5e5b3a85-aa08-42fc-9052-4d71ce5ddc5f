import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { CreditCard, Building2, QrCode, AlertCircle } from 'lucide-react';
import { PaymentMethodFormData, PaymentMethodValidation } from '@/types/payment';

interface PaymentMethodConfigProps {
  isEventFree: boolean;
  onPaymentMethodChange: (data: Partial<PaymentMethodFormData>) => void;
  initialData?: Partial<PaymentMethodFormData>;
  errors?: Record<string, string>;
}

export const PaymentMethodConfig: React.FC<PaymentMethodConfigProps> = ({
  isEventFree,
  onPaymentMethodChange,
  initialData = {},
  errors = {}
}) => {
  const [formData, setFormData] = useState<PaymentMethodFormData>({
    enableUPI: false,
    upiId: '',
    upiAccountHolderName: '',
    enableNetBanking: false,
    bankAccountHolderName: '',
    bankName: '',
    bankAccountNumber: '',
    bankIfscCode: '',
    paymentReferenceMessage: '',
    ...initialData
  });

  const [validation, setValidation] = useState<PaymentMethodValidation>({
    isValid: true,
    errors: {}
  });

  // Use ref to track if we should notify parent (avoid infinite loops)
  const isInitialMount = useRef(true);
  const lastNotifiedData = useRef<PaymentMethodFormData | null>(null);

  // Update parent component when form data changes (with loop prevention)
  useEffect(() => {
    // Skip initial mount to avoid unnecessary calls
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    // Only notify if data actually changed
    const dataChanged = JSON.stringify(formData) !== JSON.stringify(lastNotifiedData.current);
    if (dataChanged) {
      lastNotifiedData.current = { ...formData };
      onPaymentMethodChange(formData);
    }
  }, [formData]); // Remove onPaymentMethodChange from dependencies to prevent infinite loop

  // Validate form data
  useEffect(() => {
    if (isEventFree) {
      setValidation({ isValid: true, errors: {} });
      return;
    }

    const newErrors: Record<string, string> = {};

    // Check if at least one payment method is enabled
    if (!formData.enableUPI && !formData.enableNetBanking) {
      newErrors.general = 'At least one payment method must be enabled for paid events';
    }

    // Validate UPI fields
    if (formData.enableUPI) {
      if (!formData.upiId.trim()) {
        newErrors.upiId = 'UPI ID is required';
      } else if (!/^[\w.-]+@[\w.-]+$/.test(formData.upiId)) {
        newErrors.upiId = 'Please enter a valid UPI ID (e.g., user@paytm)';
      }

      if (!formData.upiAccountHolderName.trim()) {
        newErrors.upiAccountHolderName = 'Account holder name is required for UPI';
      }
    }

    // Validate Net Banking fields
    if (formData.enableNetBanking) {
      if (!formData.bankAccountHolderName.trim()) {
        newErrors.bankAccountHolderName = 'Account holder name is required';
      }

      if (!formData.bankName.trim()) {
        newErrors.bankName = 'Bank name is required';
      }

      if (!formData.bankAccountNumber.trim()) {
        newErrors.bankAccountNumber = 'Account number is required';
      } else if (!/^\d{9,18}$/.test(formData.bankAccountNumber.replace(/\s/g, ''))) {
        newErrors.bankAccountNumber = 'Please enter a valid account number (9-18 digits)';
      }

      if (!formData.bankIfscCode.trim()) {
        newErrors.bankIfscCode = 'IFSC code is required';
      } else if (!/^[A-Z]{4}0[A-Z0-9]{6}$/.test(formData.bankIfscCode.toUpperCase())) {
        newErrors.bankIfscCode = 'Please enter a valid IFSC code (e.g., SBIN0001234)';
      }
    }

    setValidation({
      isValid: Object.keys(newErrors).length === 0,
      errors: { ...newErrors, ...errors }
    });
  }, [formData, isEventFree, errors]);

  const handleInputChange = (field: keyof PaymentMethodFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const formatAccountNumber = (value: string) => {
    // Remove all non-digits and limit to 18 characters
    const digits = value.replace(/\D/g, '').slice(0, 18);
    // Add spaces every 4 digits for readability
    return digits.replace(/(\d{4})(?=\d)/g, '$1 ');
  };

  const formatIFSC = (value: string) => {
    return value.toUpperCase().replace(/[^A-Z0-9]/g, '').slice(0, 11);
  };

  if (isEventFree) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Payment Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8 text-muted-foreground">
            <div className="text-center">
              <CreditCard className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>This is a free event - no payment configuration needed</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          Payment Configuration
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Configure payment methods for participants to pay for this event
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {validation.errors.general && (
          <div className="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
            <AlertCircle className="h-4 w-4 text-destructive" />
            <span className="text-sm text-destructive">{validation.errors.general}</span>
          </div>
        )}

        {/* UPI Payment Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <QrCode className="h-5 w-5" />
              <Label htmlFor="enable-upi" className="text-base font-medium">
                UPI Payment
              </Label>
            </div>
            <Switch
              id="enable-upi"
              checked={formData.enableUPI}
              onCheckedChange={(checked) => handleInputChange('enableUPI', checked)}
            />
          </div>

          {formData.enableUPI && (
            <div className="ml-7 space-y-4 p-4 bg-muted/30 rounded-lg">
              <div className="space-y-2">
                <Label htmlFor="upi-id">UPI ID (VPA) *</Label>
                <Input
                  id="upi-id"
                  placeholder="e.g., yourname@paytm"
                  value={formData.upiId}
                  onChange={(e) => handleInputChange('upiId', e.target.value)}
                  className={validation.errors.upiId ? 'border-destructive' : ''}
                />
                {validation.errors.upiId && (
                  <p className="text-sm text-destructive">{validation.errors.upiId}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="upi-holder-name">Account Holder Name *</Label>
                <Input
                  id="upi-holder-name"
                  placeholder="Full name as per bank account"
                  value={formData.upiAccountHolderName}
                  onChange={(e) => handleInputChange('upiAccountHolderName', e.target.value)}
                  className={validation.errors.upiAccountHolderName ? 'border-destructive' : ''}
                />
                {validation.errors.upiAccountHolderName && (
                  <p className="text-sm text-destructive">{validation.errors.upiAccountHolderName}</p>
                )}
              </div>
            </div>
          )}
        </div>

        <Separator />

        {/* Net Banking Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              <Label htmlFor="enable-netbanking" className="text-base font-medium">
                Net Banking / Bank Transfer
              </Label>
            </div>
            <Switch
              id="enable-netbanking"
              checked={formData.enableNetBanking}
              onCheckedChange={(checked) => handleInputChange('enableNetBanking', checked)}
            />
          </div>

          {formData.enableNetBanking && (
            <div className="ml-7 space-y-4 p-4 bg-muted/30 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="bank-holder-name">Account Holder Name *</Label>
                  <Input
                    id="bank-holder-name"
                    placeholder="Full name as per bank account"
                    value={formData.bankAccountHolderName}
                    onChange={(e) => handleInputChange('bankAccountHolderName', e.target.value)}
                    className={validation.errors.bankAccountHolderName ? 'border-destructive' : ''}
                  />
                  {validation.errors.bankAccountHolderName && (
                    <p className="text-sm text-destructive">{validation.errors.bankAccountHolderName}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bank-name">Bank Name *</Label>
                  <Input
                    id="bank-name"
                    placeholder="e.g., State Bank of India"
                    value={formData.bankName}
                    onChange={(e) => handleInputChange('bankName', e.target.value)}
                    className={validation.errors.bankName ? 'border-destructive' : ''}
                  />
                  {validation.errors.bankName && (
                    <p className="text-sm text-destructive">{validation.errors.bankName}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="account-number">Account Number *</Label>
                  <Input
                    id="account-number"
                    placeholder="1234 **************"
                    value={formData.bankAccountNumber}
                    onChange={(e) => handleInputChange('bankAccountNumber', formatAccountNumber(e.target.value))}
                    className={validation.errors.bankAccountNumber ? 'border-destructive' : ''}
                  />
                  {validation.errors.bankAccountNumber && (
                    <p className="text-sm text-destructive">{validation.errors.bankAccountNumber}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="ifsc-code">IFSC Code *</Label>
                  <Input
                    id="ifsc-code"
                    placeholder="SBIN0001234"
                    value={formData.bankIfscCode}
                    onChange={(e) => handleInputChange('bankIfscCode', formatIFSC(e.target.value))}
                    className={validation.errors.bankIfscCode ? 'border-destructive' : ''}
                  />
                  {validation.errors.bankIfscCode && (
                    <p className="text-sm text-destructive">{validation.errors.bankIfscCode}</p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        <Separator />

        {/* Reference Message */}
        <div className="space-y-2">
          <Label htmlFor="reference-message">Payment Reference Message (Optional)</Label>
          <Textarea
            id="reference-message"
            placeholder="e.g., Please mention your phone number or order ID in the payment description"
            value={formData.paymentReferenceMessage}
            onChange={(e) => handleInputChange('paymentReferenceMessage', e.target.value)}
            rows={3}
          />
          <p className="text-sm text-muted-foreground">
            This message will be shown to participants to help them include reference information in their payments
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
