import React, { useEffect, useState } from 'react';
import { Control, useWatch } from "react-hook-form";
import { User, Mail, Phone, Globe, Building2 } from 'lucide-react';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from "@/components/ui/enhanced-accordion";
import { EventFormValues } from '@/types/event-form';
import { useOrganizations } from '@/hooks/useOrganizations';
import { UserOrganization } from '@/types/organization';

interface OrganizerSectionProps {
  control: Control<EventFormValues>;
  errorCount?: number;
  isValid?: boolean;
  showValidation?: boolean;
  completionStatus?: 'untouched' | 'partial' | 'complete' | 'error';
  showProgressIndicators?: boolean;
  preSelectedOrganization?: UserOrganization;
}

const OrganizerSection: React.FC<OrganizerSectionProps> = ({
  control,
  errorCount = 0,
  isValid = false,
  showValidation = false,
  completionStatus = 'untouched',
  showProgressIndicators = false,
  preSelectedOrganization
}) => {
  const { organizations } = useOrganizations();
  const [approvedOrganizations, setApprovedOrganizations] = useState<UserOrganization[]>([]);

  const hostedByType = useWatch({
    control,
    name: 'hostedByType',
  });

  const selectedOrganizationId = useWatch({
    control,
    name: 'organizationId',
  });

  useEffect(() => {
    // Filter organizations that are approved and user can create events for
    const approved = organizations.filter(org =>
      org.approval_status === 'approved' &&
      ['owner', 'admin', 'event_manager'].includes(org.user_role)
    );
    setApprovedOrganizations(approved);
  }, [organizations]);

  useEffect(() => {
    // Set pre-selected organization if provided
    if (preSelectedOrganization && preSelectedOrganization.approval_status === 'approved') {
      // This would be handled by the parent component setting default values
    }
  }, [preSelectedOrganization]);
  return (
    <AccordionItem value="item-4" data-value="item-4">
      <AccordionTrigger
        className="text-lg font-medium"
        errorCount={errorCount}
        isValid={isValid}
        showValidation={showValidation}
        completionStatus={completionStatus}
        showProgressIndicators={showProgressIndicators}
      >
        <div className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Organizer Details
        </div>
      </AccordionTrigger>
      <AccordionContent className="pt-4">
        <div className="space-y-6">
          {/* Hosting Type Selection */}
          <FormField
            control={control}
            name="hostedByType"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel required>Hosted By</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className="flex flex-col space-y-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="individual" id="individual" />
                      <Label htmlFor="individual" className="flex items-center gap-2 cursor-pointer">
                        <User className="h-4 w-4" />
                        Individual (Personal)
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="organization" id="organization" />
                      <Label htmlFor="organization" className="flex items-center gap-2 cursor-pointer">
                        <Building2 className="h-4 w-4" />
                        Organization
                      </Label>
                    </div>
                  </RadioGroup>
                </FormControl>
                <FormDescription>
                  Choose whether you're hosting this event as an individual or on behalf of an organization
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Organization Selection */}
          {hostedByType === 'organization' && (
            <FormField
              control={control}
              name="organizationId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel required>Select Organization</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose an organization" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {approvedOrganizations.map((org) => (
                        <SelectItem key={org.id} value={org.id}>
                          <div className="flex items-center gap-2">
                            {org.logo_url ? (
                              <img
                                src={org.logo_url}
                                alt={org.name}
                                className="w-4 h-4 rounded object-cover"
                              />
                            ) : (
                              <Building2 className="h-4 w-4" />
                            )}
                            {org.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Select the organization on whose behalf you're hosting this event
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {/* Organizer Name */}
          <FormField
            control={control}
            name="organizerName"
            render={({ field }) => (
              <FormItem>
                <FormLabel required>
                  {hostedByType === 'organization' ? 'Contact Person Name' : 'Organizer Name'}
                </FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormDescription>
                  {hostedByType === 'organization'
                    ? 'Name of the contact person for this event'
                    : 'Your name as the event organizer'
                  }
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={control}
              name="organizerEmail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <div className="flex items-center">
                    <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                      />
                    </FormControl>
                  </div>
                  <FormDescription>
                    For attendee inquiries (optional)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="organizerPhone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone</FormLabel>
                  <div className="flex items-center">
                    <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
                    <FormControl>
                      <Input placeholder="+91 1234567890" {...field} />
                    </FormControl>
                  </div>
                  <FormDescription>
                    Contact number (optional)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={control}
            name="websiteUrl"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Website or Social Media</FormLabel>
                <div className="flex items-center">
                  <Globe className="mr-2 h-4 w-4 text-muted-foreground" />
                  <FormControl>
                    <Input
                      type="url"
                      placeholder="https://example.com"
                      {...field}
                    />
                  </FormControl>
                </div>
                <FormDescription>
                  Website or social media link (optional)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};

export default OrganizerSection;
