import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CreditCard, 
  Building2, 
  QrCode, 
  Copy, 
  CheckCircle,
  Upload,
  FileText,
  AlertCircle
} from 'lucide-react';
import { PaymentInstructions as PaymentInstructionsType } from '@/types/payment';
import { useToast } from '@/hooks/use-toast';

interface PaymentInstructionsProps {
  instructions: PaymentInstructionsType;
  onPaymentCompleted: () => void;
  showUploadSection?: boolean;
}

export const PaymentInstructions: React.FC<PaymentInstructionsProps> = ({
  instructions,
  onPaymentCompleted,
  showUploadSection = true
}) => {
  const [copiedField, setCopiedField] = useState<string | null>(null);
  const { toast } = useToast();

  const copyToClipboard = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);
      toast({
        title: 'Copied!',
        description: `${fieldName} copied to clipboard`,
      });
      
      // Reset copied state after 2 seconds
      setTimeout(() => setCopiedField(null), 2000);
    } catch (error) {
      toast({
        title: 'Copy Failed',
        description: 'Failed to copy to clipboard',
        variant: 'destructive',
      });
    }
  };

  const CopyButton: React.FC<{ text: string; fieldName: string }> = ({ text, fieldName }) => (
    <Button
      variant="outline"
      size="sm"
      onClick={() => copyToClipboard(text, fieldName)}
      className="ml-2"
    >
      {copiedField === fieldName ? (
        <CheckCircle className="h-4 w-4 text-green-600" />
      ) : (
        <Copy className="h-4 w-4" />
      )}
    </Button>
  );

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Payment Instructions
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-lg font-semibold">
              ₹{instructions.totalAmount.toFixed(2)}
            </Badge>
            <span className="text-sm text-muted-foreground">
              for {instructions.eventTitle}
            </span>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">Important Instructions:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>Complete the payment using one of the methods below</li>
                  <li>After payment, upload the screenshot/proof on this page</li>
                  <li>Your registration will be confirmed once the organizer verifies your payment</li>
                </ul>
              </div>
            </div>
          </div>

          {/* UPI Payment Section */}
          {instructions.upi && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <QrCode className="h-5 w-5 text-purple-600" />
                <h3 className="text-lg font-semibold">UPI Payment</h3>
              </div>
              
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">UPI ID</p>
                    <p className="font-mono text-lg">{instructions.upi.upiId}</p>
                  </div>
                  <CopyButton text={instructions.upi.upiId} fieldName="UPI ID" />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Account Holder</p>
                    <p className="font-medium">{instructions.upi.accountHolderName}</p>
                  </div>
                  <CopyButton text={instructions.upi.accountHolderName} fieldName="Account Holder Name" />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Amount</p>
                    <p className="font-bold text-lg">₹{instructions.totalAmount.toFixed(2)}</p>
                  </div>
                  <CopyButton text={instructions.totalAmount.toString()} fieldName="Amount" />
                </div>

                {instructions.upi.qrCodeUrl && (
                  <div className="text-center pt-2">
                    <img 
                      src={instructions.upi.qrCodeUrl} 
                      alt="UPI QR Code" 
                      className="mx-auto max-w-48 border rounded-lg"
                    />
                    <p className="text-sm text-muted-foreground mt-2">
                      Scan this QR code with any UPI app
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Separator if both payment methods are available */}
          {instructions.upi && instructions.netBanking && (
            <div className="flex items-center gap-4">
              <Separator className="flex-1" />
              <span className="text-sm text-muted-foreground bg-background px-2">OR</span>
              <Separator className="flex-1" />
            </div>
          )}

          {/* Net Banking Section */}
          {instructions.netBanking && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Building2 className="h-5 w-5 text-blue-600" />
                <h3 className="text-lg font-semibold">Bank Transfer / Net Banking</h3>
              </div>
              
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Account Holder</p>
                      <p className="font-medium">{instructions.netBanking.accountHolderName}</p>
                    </div>
                    <CopyButton text={instructions.netBanking.accountHolderName} fieldName="Account Holder" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Bank Name</p>
                      <p className="font-medium">{instructions.netBanking.bankName}</p>
                    </div>
                    <CopyButton text={instructions.netBanking.bankName} fieldName="Bank Name" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Account Number</p>
                      <p className="font-mono text-lg">{instructions.netBanking.accountNumber}</p>
                    </div>
                    <CopyButton text={instructions.netBanking.accountNumber.replace(/\s/g, '')} fieldName="Account Number" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">IFSC Code</p>
                      <p className="font-mono text-lg">{instructions.netBanking.ifscCode}</p>
                    </div>
                    <CopyButton text={instructions.netBanking.ifscCode} fieldName="IFSC Code" />
                  </div>
                </div>

                <div className="flex items-center justify-between pt-2 border-t">
                  <div>
                    <p className="text-sm text-muted-foreground">Amount to Transfer</p>
                    <p className="font-bold text-lg">₹{instructions.totalAmount.toFixed(2)}</p>
                  </div>
                  <CopyButton text={instructions.totalAmount.toString()} fieldName="Amount" />
                </div>
              </div>
            </div>
          )}

          {/* Reference Message */}
          {instructions.referenceMessage && (
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <FileText className="h-5 w-5 text-amber-600 mt-0.5" />
                <div>
                  <p className="font-medium text-amber-800 mb-1">Reference Message</p>
                  <p className="text-sm text-amber-700">{instructions.referenceMessage}</p>
                </div>
              </div>
            </div>
          )}

          {/* Upload Section */}
          {showUploadSection && (
            <div className="border-t pt-6">
              <div className="text-center space-y-4">
                <div className="flex items-center justify-center gap-2">
                  <Upload className="h-5 w-5" />
                  <h3 className="text-lg font-semibold">Upload Payment Proof</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  After completing the payment, click below to upload your payment screenshot or receipt
                </p>
                <Button 
                  onClick={onPaymentCompleted}
                  className="w-full md:w-auto"
                  size="lg"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Payment Proof
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
