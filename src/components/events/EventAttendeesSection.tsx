import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, ChevronUp, Users, IndianRupee, Phone, Mail, Search, Download } from 'lucide-react';
import { useEventRegistrations } from '@/hooks/useEventRegistrations';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';
import { useInView } from 'react-intersection-observer';
import { useToast } from '@/hooks/use-toast';
import { motion, AnimatePresence } from 'framer-motion';
import { supabase } from '@/integrations/supabase/client';

interface EventAttendeesSectionProps {
  eventId: string;
}

const EventAttendeesSection: React.FC<EventAttendeesSectionProps> = ({ eventId }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [isEventFree, setIsEventFree] = useState(false);
  const { 
    attendees, 
    stats, 
    loading, 
    hasMore, 
    loadMore, 
    searchAttendees 
  } = useEventRegistrations(eventId);
  const { toast } = useToast();
  
  // Set up intersection observer for infinite scrolling
  const { ref: loadMoreRef, inView } = useInView({
    threshold: 0.1,
    triggerOnce: false
  });
  
  // Handle search with debounce
  const searchTimeout = useRef<NodeJS.Timeout | null>(null);
  
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    
    // Clear previous timeout
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current);
    }
    
    // Set new timeout for debounce
    searchTimeout.current = setTimeout(() => {
      searchAttendees(value);
    }, 500);
  };
  
  // Load more when scrolling to the bottom
  useEffect(() => {
    if (inView && hasMore && isExpanded) {
      loadMore();
    }
  }, [inView, hasMore, loadMore, isExpanded]);
  
  // Fetch event details to check if it's free
  useEffect(() => {
    const fetchEventDetails = async () => {
      if (!eventId) return;
      
      try {
        const { data, error } = await supabase
          .from('events')
          .select('is_free')
          .eq('id', eventId)
          .single();
        
        if (error) throw error;
        
        setIsEventFree(data?.is_free || false);
      } catch (error) {
        console.error('Error fetching event details:', error);
      }
    };
    
    fetchEventDetails();
  }, [eventId]);
  
  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeout.current) {
        clearTimeout(searchTimeout.current);
      }
    };
  }, []);

  const toggleExpanded = () => {
    setIsExpanded(prev => !prev);
  };

  const formatPaymentStatus = (status: string | null) => {
    // If the event is free, always show "Free" regardless of payment status
    if (isEventFree) return 'Free';
    
    if (!status) return 'Pending';
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const getPaymentStatusColor = (status: string | null) => {
    // If the event is free, use a specific color for free events
    if (isEventFree) return 'bg-blue-500 text-white';
    
    switch (status) {
      case 'completed':
        return 'bg-green-500 text-white';
      case 'pending':
        return 'bg-yellow-500 text-black';
      case 'failed':
        return 'bg-red-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };
  
  // Format ticket count with max attendees if available
  const formatTicketCount = (totalTickets: number, maxAttendees: number | null) => {
    if (maxAttendees && maxAttendees > 0) {
      const percentage = Math.round((totalTickets / maxAttendees) * 100);
      return (
        <span className="relative group">
          <span className="text-blue-primary font-semibold">{totalTickets}</span> of <span className="text-blue-primary font-semibold">{maxAttendees}</span> tickets
          <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
            {percentage}% filled
          </span>
        </span>
      );
    }
    return (
      <><span className="text-blue-primary font-semibold">{totalTickets}</span> tickets</>
    );
  };
  
  const exportToCSV = () => {
    if (!attendees.length) return;
    
    // Create CSV header
    const headers = ['Name', 'Email', 'Phone', 'Tickets', 'Amount', 'Payment Status', 'Registration Date'];
    
    // Create CSV rows
    const rows = attendees.map(attendee => [
      attendee.user_details.full_name || 'Anonymous',
      attendee.user_details.email || 'N/A',
      attendee.user_details.phone_number || 'N/A',
      attendee.quantity?.toString() || '0',
      (attendee.total_amount || 0).toString(),
      formatPaymentStatus(attendee.payment_status),
      new Date(attendee.registration_date).toLocaleDateString()
    ]);
    
    // Combine header and rows
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');
    
    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `attendees-${eventId}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      title: 'Export Successful',
      description: 'Attendee list has been exported to CSV',
    });
  };

  // Animation variants for Framer Motion
  const sectionVariants = {
    hidden: { opacity: 0, height: 0 },
    visible: { 
      opacity: 1, 
      height: 'auto',
      transition: { 
        duration: 0.3,
        ease: "easeInOut"
      }
    },
    exit: { 
      opacity: 0, 
      height: 0,
      transition: { 
        duration: 0.2,
        ease: "easeInOut"
      }
    }
  };

  const iconVariants = {
    collapsed: { rotate: 0 },
    expanded: { rotate: 180 }
  };

  return (
    <div className="mt-4 pt-4 border-t">
      <Button
        variant="ghost"
        className="w-full flex items-center justify-between p-2 hover:bg-blue-primary/5"
        onClick={toggleExpanded}
      >
        <div className="flex items-center">
          <Users size={18} className="mr-2 text-blue-primary" />
          <span className="font-medium">
            {loading && !attendees.length ? 'Loading...' : (
              <>
                <span className="text-blue-primary font-semibold">{stats.totalAttendees}</span> 
                {stats.totalAttendees === 1 ? ' Registration' : ' Registrations'} 
                <span className="mx-1">•</span> 
                {formatTicketCount(stats.totalTickets, stats.maxAttendees)}
                <span className="mx-1">•</span> 
                <span className="text-blue-primary font-semibold">
                  {new Intl.NumberFormat('en-IN', {
                    style: 'currency',
                    currency: 'INR',
                    maximumFractionDigits: 0
                  }).format(stats.totalRevenue || 0)}
                </span>
              </>
            )}
          </span>
        </div>
        <motion.div
          animate={isExpanded ? "expanded" : "collapsed"}
          variants={iconVariants}
          transition={{ duration: 0.3 }}
        >
          <ChevronDown size={18} />
        </motion.div>
      </Button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={sectionVariants}
            className="mt-2 space-y-3 overflow-hidden"
          >
            <div className="flex flex-col sm:flex-row gap-2 items-center justify-between">
              <div className="relative w-full sm:w-64">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search attendees..."
                  className="pl-8 w-full focus-visible:ring-2 focus-visible:ring-offset-0"
                  value={searchValue}
                  onChange={handleSearchChange}
                />
              </div>
              
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full sm:w-auto"
                onClick={exportToCSV}
                disabled={!attendees.length}
              >
                <Download size={16} className="mr-2" />
                Export CSV
              </Button>
            </div>
            
            {loading && !attendees.length ? (
              <div className="space-y-3">
                {[1, 2, 3].map((i) => (
                  <Skeleton key={i} className="h-20 w-full rounded-md" />
                ))}
              </div>
            ) : attendees.length === 0 ? (
              <div className="text-center py-6 text-muted-foreground">
                {searchValue ? 'No matching attendees found.' : 'No registrations yet for this event.'}
              </div>
            ) : (
              <>
                <div className="rounded-md border">
                  <div className="hidden md:grid grid-cols-12 gap-4 p-4 bg-muted text-sm font-medium">
                    <div className="col-span-4">Attendee</div>
                    <div className="col-span-3">Contact</div>
                    <div className="col-span-2">Tickets</div>
                    <div className="col-span-3">Payment</div>
                  </div>
                  
                  <div className="divide-y">
                    {attendees.map((attendee, index) => (
                      <motion.div
                        key={attendee.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.2, delay: index * 0.05 }}
                      >
                        <Card className="border-0 rounded-none">
                          <CardContent className="p-4">
                            <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
                              {/* Attendee Info */}
                              <div className="col-span-4 space-y-1">
                                <div className="font-medium">
                                  {attendee.user_details.full_name || 'Anonymous'}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  Registered on {new Date(attendee.registration_date).toLocaleDateString()}
                                </div>
                              </div>
                              
                              {/* Contact Info */}
                              <div className="col-span-3 space-y-1">
                                {attendee.user_details.email && (
                                  <div className="flex items-center text-sm">
                                    <Mail size={14} className="mr-1 text-muted-foreground" />
                                    <span className="truncate">{attendee.user_details.email}</span>
                                  </div>
                                )}
                                {attendee.user_details.phone_number && (
                                  <div className="flex items-center text-sm">
                                    <Phone size={14} className="mr-1 text-muted-foreground" />
                                    <span>{attendee.user_details.phone_number}</span>
                                  </div>
                                )}
                              </div>
                              
                              {/* Ticket Info */}
                              <div className="col-span-2 flex items-center">
                                <div className="text-sm flex flex-row md:flex-col justify-between w-full">
                                  <div className="font-medium">{attendee.quantity || 0} ticket{(attendee.quantity || 0) !== 1 ? 's' : ''}</div>
                                  {attendee.ticket_type && (
                                    <div className="text-muted-foreground hidden md:block">{attendee.ticket_type}</div>
                                  )}
                                  {/* Payment Status Badge - Mobile Only */}
                                  <div className="md:hidden">
                                    <Badge 
                                      className={`w-fit ${getPaymentStatusColor(attendee.payment_status)}`}
                                    >
                                      {formatPaymentStatus(attendee.payment_status)}
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                              
                              {/* Payment Info */}
                              <div className="col-span-3 flex flex-col justify-center space-y-1 hidden md:flex">
                                <Badge 
                                  className={`w-fit ${getPaymentStatusColor(attendee.payment_status)}`}
                                >
                                  {formatPaymentStatus(attendee.payment_status)}
                                </Badge>
                                {!isEventFree && (attendee.total_amount || 0) > 0 && (
                                  <div className="flex items-center text-sm">
                                    <IndianRupee size={14} className="mr-1 text-muted-foreground" />
                                    <span>{attendee.total_amount}</span>
                                  </div>
                                )}
                              </div>
                              
                              {/* Payment Amount - Mobile Only */}
                              {!isEventFree && (attendee.total_amount || 0) > 0 && (
                                <div className="mt-1 md:hidden flex items-center text-sm">
                                  <IndianRupee size={14} className="mr-1 text-muted-foreground" />
                                  <span>{attendee.total_amount}</span>
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </div>
                
                {/* Load more section */}
                {hasMore && (
                  <div ref={loadMoreRef} className="py-4 text-center">
                    {loading ? (
                      <Skeleton className="h-10 w-32 mx-auto" />
                    ) : (
                      <Button 
                        variant="outline" 
                        onClick={loadMore}
                        className="mx-auto"
                      >
                        Load More
                      </Button>
                    )}
                  </div>
                )}
              </>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default EventAttendeesSection;
