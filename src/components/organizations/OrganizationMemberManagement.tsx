import React, { useState } from 'react';
import { useAuth } from '@/context/AuthContext'; // Added
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useOrganizations } from '@/hooks/useOrganizations';
import { OrganizationMember, OrganizationRole, organizationRoles } from '@/types/organization'; // Imported organizationRoles
import { UserPlus, MoreHorizontal, Trash2, Edit, Crown, Shield, Users, User } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';

interface OrganizationMemberManagementProps {
  organizationId: string;
  members: OrganizationMember[];
  currentUserRole: OrganizationRole; // Role of the viewing user within this specific organization
  isPlatformAdmin: boolean; // Is the viewing user a platform admin?
  onMembersUpdate: () => void;
}

export const OrganizationMemberManagement: React.FC<OrganizationMemberManagementProps> = ({
  organizationId,
  members,
  currentUserRole,
  isPlatformAdmin, // Added prop
  onMembersUpdate,
}) => {
  const { user: loggedInUser } = useAuth(); // Get the full logged-in user object for ID
  const { addMember, updateMemberRole, removeMember } = useOrganizations();
  const { toast } = useToast();
  const [isAddingMember, setIsAddingMember] = useState(false);
  const [newMemberEmail, setNewMemberEmail] = useState('');
  const [newMemberRole, setNewMemberRole] = useState<OrganizationRole>('member');
  const [loading, setLoading] = useState(false);

  const canManageMembers = currentUserRole === 'owner' || currentUserRole === 'admin';
  const canChangeRoles = currentUserRole === 'owner' || currentUserRole === 'admin';
  const canRemoveMembers = currentUserRole === 'owner' || currentUserRole === 'admin';

  type MemberStatus = OrganizationMember['status'];

  const getStatusColor = (status: MemberStatus) => {
    switch (status) {
      case 'pending_invitation':
        return 'bg-yellow-100 text-yellow-800';
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'declined':
        return 'bg-red-100 text-red-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatStatusText = (status: MemberStatus) => {
    switch (status) {
      case 'pending_invitation':
        return 'Pending Invitation';
      case 'active':
        return 'Active';
      case 'declined':
        return 'Declined';
      case 'inactive':
        return 'Inactive';
      default: {
        const _exhaustiveCheck: never = status;
        return status;
      }
    }
  };

  const getRoleIcon = (role: OrganizationRole) => {
    switch (role) {
      case 'owner':
        return <Crown className="h-4 w-4" />;
      case 'admin':
        return <Shield className="h-4 w-4" />;
      case 'event_manager':
        return <Users className="h-4 w-4" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  const getRoleColor = (role: OrganizationRole) => {
    switch (role) {
      case 'owner':
        return 'bg-purple-100 text-purple-800';
      case 'admin':
        return 'bg-blue-100 text-blue-800';
      case 'event_manager':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleAddMember = async () => {
    if (!newMemberEmail.trim()) {
      toast({
        title: "Email required",
        description: "Please enter a valid email address.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      await addMember(organizationId, newMemberEmail, newMemberRole);
      setNewMemberEmail('');
      setNewMemberEmail('');
      setNewMemberRole('member');
      setIsAddingMember(false);
      onMembersUpdate();
      // Success toast is now handled by the useOrganizations hook's addMember function
      // to provide more specific messages (e.g., "Invitation sent...").
      // toast({
      //   title: "Member added",
      //   description: "The member has been successfully added to the organization.",
      // });
    } catch (error) {
      // Errors are logged and toasted within the useOrganizations hook's addMember function.
      // The component's setLoading(false) in the finally block will still run.
      console.error('Error adding member (caught in component):', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRoleChange = async (memberId: string, newRole: OrganizationRole) => {
    setLoading(true);
    try {
      // Corrected: Pass only memberId and newRole as expected by the hook
      await updateMemberRole(memberId, newRole);
      onMembersUpdate(); // This will refresh the list
      // Toast is now primarily handled by the hook, but a generic one here is okay as fallback.
      // The hook's toast is more specific ("Member role updated successfully. Refreshing data...").
      // Consider removing this component toast if the hook's is sufficient.
      toast({
        title: "Role update initiated",
        description: "Refreshing member list...",
      });
    } catch (error) {
      // Error is already logged and toasted by the useOrganizations hook
      console.error('Error initiating role update from component:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    setLoading(true);
    try {
      await removeMember(organizationId, memberId);
      onMembersUpdate();
      toast({
        title: "Member removed",
        description: "The member has been successfully removed from the organization.",
      });
    } catch (error) {
      console.error('Error removing member:', error);
    } finally {
      setLoading(false);
    }
  };

  // Simplified permission helpers, direct logic will be used in JSX for clarity with platformAdmin
  const isViewingOwnRecord = (memberUserId?: string) => loggedInUser?.id === memberUserId;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Members ({members.length})
            </CardTitle>
            <CardDescription>
              Manage organization members and their roles
            </CardDescription>
          </div>
          {canManageMembers && (
            <Dialog open={isAddingMember} onOpenChange={setIsAddingMember}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add Member
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Member</DialogTitle>
                  <DialogDescription>
                    Invite a new member to join this organization.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={newMemberEmail}
                      onChange={(e) => setNewMemberEmail(e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="role">Role</Label>
                    <Select value={newMemberRole} onValueChange={(value) => setNewMemberRole(value as OrganizationRole)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a role" />
                      </SelectTrigger>
                      <SelectContent>
                        {organizationRoles.map(roleOption => (
                          <SelectItem key={roleOption.value} value={roleOption.value}>
                            {roleOption.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddingMember(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddMember} disabled={loading}>
                    {loading ? 'Adding...' : 'Add Member'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {/* Column Headers */}
        <div className="hidden md:flex items-center px-4 py-2 border-b font-medium text-sm text-muted-foreground mb-2">
          <div className="flex-grow">Member</div>
          <div className="w-36 flex-shrink-0 text-center">Role</div>
          <div className="w-40 flex-shrink-0 text-center">Status</div>
          <div className="w-20 flex-shrink-0 text-right">Actions</div>
        </div>

        <div className="space-y-4">
          {members.map((member) => (
            <div key={member.id} className="flex items-center p-4 border rounded-lg gap-4">
              {/* Member Info (Avatar + Name/Email) - Column 1 */}
              <div className="flex-grow flex items-center gap-3 min-w-0">
                <Avatar>
                  <AvatarImage src={member.user?.user_metadata?.avatar_url} />
                  <AvatarFallback>
                    {member.user?.user_metadata?.name?.charAt(0) || 
                     member.user?.email?.charAt(0).toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="min-w-0"> {/* Inner div for text truncation */}
                  <p className="font-medium truncate" title={member.user?.user_metadata?.name || member.user?.email}>
                    {member.user?.user_metadata?.name || member.user?.email}
                  </p>
                  <p className="text-sm text-muted-foreground truncate" title={member.user?.email}>
                    {member.user?.email}
                  </p>
                </div>
              </div>
              
              {/* Role Column - Column 2 */}
              <div className="w-36 flex-shrink-0 flex justify-center items-center">
                <Badge className={getRoleColor(member.role)}>
                  <div className="flex items-center justify-center gap-1 w-full"> {/* Centering content within badge */}
                    {getRoleIcon(member.role)}
                    {member.role}
                  </div>
                </Badge>
              </div>

              {/* Status Column - Column 3 */}
              <div className="w-40 flex-shrink-0 flex justify-center items-center">
                <Badge className={`${getStatusColor(member.status)} w-full text-center`}>
                  {formatStatusText(member.status)}
                </Badge>
              </div>

              {/* Actions Column - Column 4 */}
              <div className="w-20 flex-shrink-0 flex justify-end items-center">
                {(() => {
                  // Determine if the current viewing user can manage this specific member
                  const canManageThisMember =
                    (isPlatformAdmin && !isViewingOwnRecord(member.user?.id)) ||
                    (currentUserRole === 'owner' && !isViewingOwnRecord(member.user?.id)) ||
                    (currentUserRole === 'admin' && member.role !== 'owner' && !isViewingOwnRecord(member.user?.id));

                  if (!canManageThisMember) {
                    return null; // No actions if not allowed to manage this member or if it's their own record
                  }

                  // Determine specific actions
                  const showMakeOwner = (isPlatformAdmin || currentUserRole === 'owner') && member.role === 'member';
                  const showMakeMember = (isPlatformAdmin || currentUserRole === 'owner') && member.role === 'owner';

                  const showRemoveMember =
                    (isPlatformAdmin && !isViewingOwnRecord(member.user?.id)) ||
                    (currentUserRole === 'owner' && !isViewingOwnRecord(member.user?.id)) ||
                    (currentUserRole === 'admin' && member.role !== 'owner' && !isViewingOwnRecord(member.user?.id));

                  // Only render dropdown if there's at least one action to show
                  if (!showMakeOwner && !showMakeMember && !showRemoveMember) {
                    return null;
                  }

                  return (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {showMakeOwner && (
                          <DropdownMenuItem onClick={() => handleRoleChange(member.id, 'owner')}>
                            <Crown className="h-4 w-4 mr-2" />
                            Make Owner
                          </DropdownMenuItem>
                        )}
                        {showMakeMember && (
                          <DropdownMenuItem onClick={() => handleRoleChange(member.id, 'member')}>
                            <User className="h-4 w-4 mr-2" />
                            Make Member
                          </DropdownMenuItem>
                        )}
                        {showRemoveMember && (
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <DropdownMenuItem
                                onSelect={(e) => e.preventDefault()}
                                className="text-destructive hover:!text-destructive-foreground hover:!bg-destructive/90"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                <span>Remove Member</span>
                              </DropdownMenuItem>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Remove Member</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to remove {member.user?.user_metadata?.name || member.user?.email}?
                                  This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleRemoveMember(member.id)}
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                >
                                  Remove
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  );
                })()}
              </div>
            </div>
          ))}
          
          {members.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No members yet</p>
              <p className="text-sm">Add members to start collaborating</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
