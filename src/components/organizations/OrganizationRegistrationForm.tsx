import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { organizationFormSchema, OrganizationFormValues } from '@/types/organization';
import { useOrganizations } from '@/hooks/useOrganizations';
import { GooglePlacesAutocomplete } from '@/components/ui/google-places-autocomplete';
import { PlaceDetails } from '@/utils/googlePlacesLoader';
import { Loader2, Building2 } from 'lucide-react';

interface OrganizationRegistrationFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const OrganizationRegistrationForm: React.FC<OrganizationRegistrationFormProps> = ({
  onSuccess,
  onCancel,
}) => {
  const { createOrganization } = useOrganizations();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<OrganizationFormValues>({
    resolver: zodResolver(organizationFormSchema),
  });

  const onSubmit = async (data: OrganizationFormValues) => {
    try {
      setIsSubmitting(true);
      await createOrganization({
        name: data.name,
        description: data.description,
        logo_url: data.logoUrl,
        website_url: data.websiteUrl,
        contact_email: data.contactEmail,
        contact_phone: data.contactPhone,
        address: data.address,
        city: data.city,
        state: data.state,
        zip_code: data.zipCode,
        locality: data.locality,
        place_id: data.placeId,
      });
      onSuccess?.();
    } catch (error) {
      console.error('Failed to create organization:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePlaceSelect = (place: PlaceDetails) => {
    if (place.formattedAddress) {
      setValue('address', place.formattedAddress);
    }

    if (place.id) {
      setValue('placeId', place.id);
    }

    // Extract location coordinates
    if (place.location) {
      setValue('latitude', place.location.latitude);
      setValue('longitude', place.location.longitude);
    }

    // Extract address components
    place.addressComponents?.forEach((component) => {
      const types = component.types;

      if (types.includes('locality')) {
        setValue('city', component.longText);
      } else if (types.includes('administrative_area_level_1')) {
        setValue('state', component.longText);
      } else if (types.includes('postal_code')) {
        setValue('zipCode', component.longText);
      } else if (types.includes('sublocality_level_1')) {
        setValue('locality', component.longText);
      }
    });
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center gap-2">
          <Building2 className="h-6 w-6 text-primary" />
          <CardTitle>Register Organization</CardTitle>
        </div>
        <CardDescription>
          Create an organization to host events. Your organization will be reviewed before approval.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Basic Information</h3>
            
            <div>
              <Label htmlFor="name">Organization Name *</Label>
              <Input
                id="name"
                {...register('name')}
                placeholder="Enter organization name"
              />
              {errors.name && (
                <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Describe your organization's mission and activities"
                rows={3}
              />
              {errors.description && (
                <p className="text-sm text-red-600 mt-1">{errors.description.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="logoUrl">Logo URL</Label>
              <Input
                id="logoUrl"
                {...register('logoUrl')}
                placeholder="https://example.com/logo.png"
              />
              {errors.logoUrl && (
                <p className="text-sm text-red-600 mt-1">{errors.logoUrl.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="websiteUrl">Website URL</Label>
              <Input
                id="websiteUrl"
                {...register('websiteUrl')}
                placeholder="https://example.com"
              />
              {errors.websiteUrl && (
                <p className="text-sm text-red-600 mt-1">{errors.websiteUrl.message}</p>
              )}
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Contact Information</h3>
            
            <div>
              <Label htmlFor="contactEmail">Contact Email</Label>
              <Input
                id="contactEmail"
                type="email"
                {...register('contactEmail')}
                placeholder="<EMAIL>"
              />
              {errors.contactEmail && (
                <p className="text-sm text-red-600 mt-1">{errors.contactEmail.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="contactPhone">Contact Phone</Label>
              <Input
                id="contactPhone"
                {...register('contactPhone')}
                placeholder="+91 98765 43210"
              />
              {errors.contactPhone && (
                <p className="text-sm text-red-600 mt-1">{errors.contactPhone.message}</p>
              )}
            </div>
          </div>

          {/* Location Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Location</h3>
            
            <div>
              <Label htmlFor="address">Address</Label>
              <GooglePlacesAutocomplete
                onPlaceSelect={handlePlaceSelect}
                placeholder="Search for organization address (optional)"
                value={watch('address')}
                onChange={(value) => setValue('address', value)}
              />
              {errors.address && (
                <p className="text-sm text-red-600 mt-1">{errors.address.message}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  {...register('city')}
                  placeholder="City (optional)"
                />
                {errors.city && (
                  <p className="text-sm text-red-600 mt-1">{errors.city.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="state">State</Label>
                <Input
                  id="state"
                  {...register('state')}
                  placeholder="State (optional)"
                />
                {errors.state && (
                  <p className="text-sm text-red-600 mt-1">{errors.state.message}</p>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="zipCode">ZIP Code</Label>
              <Input
                id="zipCode"
                {...register('zipCode')}
                placeholder="110001"
              />
              {errors.zipCode && (
                <p className="text-sm text-red-600 mt-1">{errors.zipCode.message}</p>
              )}
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex gap-4 pt-4">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex-1"
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Register Organization
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
