import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { UserOrganization } from '@/types/organization';
import { Building2, Globe, Mail, Phone, MapPin, Users, Settings } from 'lucide-react';

interface OrganizationListProps {
  organizations: UserOrganization[];
  onManageOrganization?: (organization: UserOrganization) => void;
  onCreateEvent?: (organization: UserOrganization) => void;
}

export const OrganizationList: React.FC<OrganizationListProps> = ({
  organizations,
  onManageOrganization,
  onCreateEvent,
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'owner':
        return 'bg-purple-100 text-purple-800';
      case 'admin':
        return 'bg-blue-100 text-blue-800';
      case 'event_manager':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const canManage = (role: string) => {
    return role === 'owner' || role === 'admin';
  };

  const canCreateEvents = (role: string, status: string) => {
    return status === 'approved' && (role === 'owner' || role === 'admin' || role === 'event_manager');
  };

  if (organizations.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Building2 className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Organizations</h3>
          <p className="text-gray-600 text-center">
            You're not a member of any organizations yet. Create one to start hosting events as an organization.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {organizations.map((org) => (
        <Card key={org.id} className="hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                {org.logo_url ? (
                  <img
                    src={org.logo_url}
                    alt={`${org.name} logo`}
                    className="w-12 h-12 rounded-lg object-cover"
                  />
                ) : (
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Building2 className="h-6 w-6 text-primary" />
                  </div>
                )}
                <div>
                  <CardTitle className="text-xl">{org.name}</CardTitle>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge className={getStatusColor(org.approval_status)}>
                      {org.approval_status}
                    </Badge>
                    <Badge className={getRoleColor(org.user_role)}>
                      {org.user_role}
                    </Badge>
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                {canCreateEvents(org.user_role, org.approval_status) && onCreateEvent && (
                  <Button
                    size="sm"
                    onClick={() => onCreateEvent(org)}
                  >
                    Create Event
                  </Button>
                )}
                {canManage(org.user_role) && onManageOrganization && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onManageOrganization(org)}
                  >
                    <Settings className="h-4 w-4 mr-1" />
                    Manage
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
          
          {org.description && (
            <CardContent className="pt-0">
              <CardDescription className="text-base mb-4">
                {org.description}
              </CardDescription>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                {org.contact_email && (
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    <span>{org.contact_email}</span>
                  </div>
                )}
                
                {org.contact_phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    <span>{org.contact_phone}</span>
                  </div>
                )}
                
                {org.website_url && (
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    <a
                      href={org.website_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline"
                    >
                      Visit Website
                    </a>
                  </div>
                )}
                
                {(org.city || org.state) && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    <span>
                      {[org.city, org.state].filter(Boolean).join(', ')}
                    </span>
                  </div>
                )}
              </div>
              
              {org.approval_status === 'pending' && (
                <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-800">
                    <strong>Pending Approval:</strong> Your organization is under review. 
                    You'll be notified once it's approved and you can start creating events.
                  </p>
                </div>
              )}
              
              {org.approval_status === 'rejected' && org.approval_notes && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-800">
                    <strong>Rejected:</strong> {org.approval_notes}
                  </p>
                </div>
              )}
            </CardContent>
          )}
        </Card>
      ))}
    </div>
  );
};
