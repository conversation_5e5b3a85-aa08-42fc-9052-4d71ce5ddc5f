import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { UserOrganization, OrganizationMember, Organization } from '@/types/organization'; // Added Organization type
import { Building2, Globe, Mail, Phone, MapPin, Users, Settings, CheckCircle, XCircle, Loader2 } from 'lucide-react';
// import { useOrganizations } from '@/hooks/useOrganizations'; // No longer needed here

interface OrganizationListProps {
  organizations: UserOrganization[];
  onManageOrganization?: (organization: UserOrganization) => void;
  onCreateEvent?: (organization: UserOrganization) => void;
  acceptInvitation: (membershipId: string) => Promise<void>;
  declineInvitation: (membershipId: string) => Promise<void>;
  isProcessingInvitation: boolean;
}

export const OrganizationList: React.FC<OrganizationListProps> = ({
  organizations,
  onManageOrganization,
  onCreateEvent,
  acceptInvitation,
  declineInvitation,
  isProcessingInvitation,
}) => {
  // Renamed to avoid conflict if a different status (e.g. member status) color logic is needed
  const getOrgApprovalStatusColor = (status: Organization['approval_status']) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleColor = (role: UserOrganization['user_role']) => {
    switch (role) {
      case 'owner':
        return 'bg-purple-100 text-purple-800';
      case 'admin':
        return 'bg-blue-100 text-blue-800';
      case 'event_manager':
        return 'bg-green-100 text-green-800';
      case 'member':
        return 'bg-gray-100 text-gray-800';
      default: {
        const _exhaustiveCheck: never = role;
        return 'bg-gray-100 text-gray-800';
      }
    }
  };

  const getMembershipStatusColor = (status: OrganizationMember['status']) => {
    switch (status) {
      case 'pending_invitation':
        return 'bg-yellow-100 text-yellow-800';
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'declined':
        return 'bg-red-100 text-red-800';
      case 'inactive':
        return 'bg-gray-500 text-white'; // Darker gray for inactive
      default: {
        const _exhaustiveCheck: never = status;
        return 'bg-gray-100 text-gray-800';
      }
    }
  };

  const formatMembershipStatusText = (status: OrganizationMember['status']) => {
    switch (status) {
      case 'pending_invitation':
        return 'Pending Invitation';
      case 'active':
        return 'Active Member';
      case 'declined':
        return 'Invitation Declined';
      case 'inactive':
        return 'Inactive';
      default: {
        const _exhaustiveCheck: never = status;
        return status;
      }
    }
  };


  const canManage = (role: UserOrganization['user_role']) => {
    return role === 'owner' || role === 'admin';
  };

  const canCreateEvents = (role: UserOrganization['user_role'], orgApprovalStatus: Organization['approval_status']) => {
  return orgApprovalStatus === 'approved' &&
         (role === 'owner' || role === 'admin' || role === 'event_manager' || role === 'member');
  };

  if (organizations.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Building2 className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Organizations</h3>
          <p className="text-gray-600 text-center">
            You're not a member of any organizations yet. Create one to start hosting events as an organization.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {organizations.map((org) => (
        <Card key={org.id} className="hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                {org.logo_url ? (
                  <img
                    src={org.logo_url}
                    alt={`${org.name} logo`}
                    className="w-12 h-12 rounded-lg object-cover"
                  />
                ) : (
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Building2 className="h-6 w-6 text-primary" />
                  </div>
                )}
                <div>
                  <CardTitle className="text-xl">{org.name}</CardTitle>
                  <div className="flex items-center gap-2 mt-1 flex-wrap">
                    <Badge className={getOrgApprovalStatusColor(org.approval_status)}>
                      Org Status: {org.approval_status}
                    </Badge>
                    <Badge className={getRoleColor(org.user_role)}>
                      Your Role: {org.user_role}
                    </Badge>
                    <Badge className={getMembershipStatusColor(org.membership_status)}>
                      Membership: {formatMembershipStatusText(org.membership_status)}
                    </Badge>
                  </div>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-2 items-start sm:items-center">
                {org.membership_status === 'pending_invitation' && (
                  <>
                    <Button
                      size="sm"
                      variant="outline"
                      className="border-green-500 text-green-600 hover:bg-green-50 hover:text-green-700 w-full sm:w-auto"
                      onClick={async () => {
                        try {
                          await acceptInvitation(org.membership_id);
                        } catch (error) {
                          // Error already toasted by hook
                          console.error("Accept invitation failed from component", error);
                        }
                      }}
                      disabled={isProcessingInvitation}
                    >
                      {isProcessingInvitation ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <CheckCircle className="h-4 w-4 mr-1" />}
                      Accept
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="border-red-500 text-red-600 hover:bg-red-50 hover:text-red-700 w-full sm:w-auto"
                      onClick={async () => {
                        try {
                          await declineInvitation(org.membership_id);
                        } catch (error) {
                          // Error already toasted by hook
                           console.error("Decline invitation failed from component", error);
                        }
                      }}
                      disabled={isProcessingInvitation}
                    >
                      {isProcessingInvitation ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <XCircle className="h-4 w-4 mr-1" />}
                      Decline
                    </Button>
                  </>
                )}
                {org.membership_status === 'active' && (
                  <>
                    {canCreateEvents(org.user_role, org.approval_status) && onCreateEvent && (
                      <Button
                        size="sm"
                        onClick={() => onCreateEvent(org)}
                        disabled={isProcessingInvitation} // Also disable other actions if an invitation is being processed
                        className="w-full sm:w-auto"
                      >
                        Create Event
                      </Button>
                    )}
                    {canManage(org.user_role) && onManageOrganization && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onManageOrganization(org)}
                        disabled={isProcessingInvitation} // Also disable other actions if an invitation is being processed
                        className="w-full sm:w-auto"
                      >
                        <Settings className="h-4 w-4 mr-1" />
                        Manage
                      </Button>
                    )}
                  </>
                )}
                 {/* For 'declined' or 'inactive' status, typically no actions are shown from this list view */}
              </div>
            </div>
          </CardHeader>
          
          {org.description && (
            <CardContent className="pt-0">
              <CardDescription className="text-base mb-4">
                {org.description}
              </CardDescription>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                {org.contact_email && (
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    <span>{org.contact_email}</span>
                  </div>
                )}
                
                {org.contact_phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    <span>{org.contact_phone}</span>
                  </div>
                )}
                
                {org.website_url && (
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    <a
                      href={org.website_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline"
                    >
                      Visit Website
                    </a>
                  </div>
                )}
                
                {(org.city || org.state) && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    <span>
                      {[org.city, org.state].filter(Boolean).join(', ')}
                    </span>
                  </div>
                )}
              </div>
              
              {org.approval_status === 'pending' && (
                <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-800">
                    <strong>Pending Approval:</strong> Your organization is under review. 
                    You'll be notified once it's approved and you can start creating events.
                  </p>
                </div>
              )}
              
              {org.approval_status === 'rejected' && org.approval_notes && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-800">
                    <strong>Rejected:</strong> {org.approval_notes}
                  </p>
                </div>
              )}
            </CardContent>
          )}
        </Card>
      ))}
    </div>
  );
};
