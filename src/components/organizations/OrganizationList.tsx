import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { UserOrganization, OrganizationMember, Organization } from '@/types/organization'; // Added Organization
import { Building2, Globe, Mail, Phone, MapPin, Users, Settings, Check, X, Loader2 } from 'lucide-react';
import { useOrganizations } from '@/hooks/useOrganizations'; // Import the hook

interface OrganizationListProps {
  organizations: UserOrganization[];
  onManageOrganization?: (organization: UserOrganization) => void;
  onCreateEvent?: (organization: UserOrganization) => void;
  // onMembersUpdate prop might not be directly needed if hook handles refetch
}

export const OrganizationList: React.FC<OrganizationListProps> = ({
  organizations,
  onManageOrganization,
  onCreateEvent,
}) => {
  const {
    acceptInvitation,
    declineInvitation,
    loading: orgsHookLoading
  } = useOrganizations(); // Use the hook

  // Renamed to avoid conflict if a different status (e.g. member status) color logic is needed
  const getOrgApprovalStatusColor = (status: Organization['approval_status']) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleColor = (role: UserOrganization['user_role']) => {
    switch (role) {
      case 'owner':
        return 'bg-purple-100 text-purple-800';
      case 'admin':
        return 'bg-blue-100 text-blue-800';
      case 'event_manager':
        return 'bg-green-100 text-green-800';
      case 'member':
        return 'bg-gray-100 text-gray-800';
      default: {
        const _exhaustiveCheck: never = role;
        return 'bg-gray-100 text-gray-800';
      }
    }
  };

  const getMembershipStatusColor = (status: OrganizationMember['status']) => {
    switch (status) {
      case 'pending_invitation':
        return 'bg-yellow-100 text-yellow-800';
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'declined':
        return 'bg-red-100 text-red-800';
      case 'inactive':
        return 'bg-gray-500 text-white'; // Darker gray for inactive
      default: {
        const _exhaustiveCheck: never = status;
        return 'bg-gray-100 text-gray-800';
      }
    }
  };

  const formatMembershipStatusText = (status: OrganizationMember['status']) => {
    switch (status) {
      case 'pending_invitation':
        return 'Pending Invitation';
      case 'active':
        return 'Active Member';
      case 'declined':
        return 'Invitation Declined';
      case 'inactive':
        return 'Inactive';
      default: {
        const _exhaustiveCheck: never = status;
        return status;
      }
    }
  };


  const canManage = (role: UserOrganization['user_role']) => {
    return role === 'owner' || role === 'admin';
  };

  const canCreateEvents = (role: UserOrganization['user_role'], orgApprovalStatus: Organization['approval_status']) => {
    return orgApprovalStatus === 'approved' && (role === 'owner' || role === 'admin' || role === 'event_manager');
  };

  if (organizations.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Building2 className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Organizations</h3>
          <p className="text-gray-600 text-center">
            You're not a member of any organizations yet. Create one to start hosting events as an organization.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {organizations.map((org) => {
        // DIAGNOSTIC LOG
        console.log('OrgList Item Processing:', {
          name: org.name,
          id: org.id,
          membership_id: org.membership_id,
          user_role: org.user_role,
          membership_status: org.membership_status,
          approval_status: org.approval_status
        });

        return (
          <Card key={org.id} className="overflow-hidden">
            <CardHeader className="bg-gray-50 dark:bg-gray-800 p-4 border-b">
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">{org.name}</CardTitle>
                  {org.description && (
                    <CardDescription className="text-xs text-gray-500 dark:text-gray-400 mt-0.5 line-clamp-2">
                      {org.description}
                    </CardDescription>
                  )}
                </div>
                <Badge
                  className={`ml-2 text-xs px-2 py-0.5 rounded-full ${getOrgApprovalStatusColor(org.approval_status)}`}
                >
                  {org.approval_status.charAt(0).toUpperCase() + org.approval_status.slice(1)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="p-4 space-y-3">
              <div className="space-y-1 text-xs text-gray-700 dark:text-gray-300">
                <div className="flex items-center">
                  <Users className="h-3 w-3 mr-2 text-gray-500" />
                  <span>Your Role: </span>
                  <Badge variant="outline" className={`ml-1.5 px-1.5 py-0.5 text-xs ${getRoleColor(org.user_role)}`}>
                    {org.user_role.charAt(0).toUpperCase() + org.user_role.slice(1)}
                  </Badge>
                </div>
                <div className="flex items-center">
                  <Users className="h-3 w-3 mr-2 text-gray-500" />
                  <span>Membership: </span>
                  <Badge variant="outline" className={`ml-1.5 px-1.5 py-0.5 text-xs ${getMembershipStatusColor(org.membership_status)}`}>
                    {formatMembershipStatusText(org.membership_status)}
                  </Badge>
                </div>
                {org.contact_email && (
                  <div className="flex items-center">
                    <Mail className="h-3 w-3 mr-2 text-gray-500" /> {org.contact_email}
                  </div>
                )}
                {org.website_url && (
                  <div className="flex items-center">
                    <Globe className="h-3 w-3 mr-2 text-gray-500" />
                    <a href={org.website_url} target="_blank" rel="noopener noreferrer" className="hover:underline">
                      {org.website_url}
                    </a>
                  </div>
                )}
                {org.address && (
                  <div className="flex items-center">
                    <MapPin className="h-3 w-3 mr-2 text-gray-500" /> {org.address}
                  </div>
                )}
              </div>

              {org.membership_status === 'pending_invitation' && (
                <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3 mb-3">
                    <p className="text-sm text-yellow-800 dark:text-yellow-200 font-medium">
                      You have been invited to join this organization
                    </p>
                  </div>
                  <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                    <Button
                      onClick={() => {
                        console.log('Accept button clicked for org:', org.name, 'membership_id:', org.membership_id);
                        if (org.membership_id) {
                          acceptInvitation(org.membership_id).catch(err => {
                            console.error('Error accepting invitation:', err);
                          });
                        }
                      }}
                      disabled={orgsHookLoading || !org.membership_id}
                      className="w-full sm:w-auto flex-grow bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700"
                      size="sm"
                    >
                      {orgsHookLoading ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <Check className="mr-2 h-4 w-4" />
                      )}
                      Accept Invitation
                    </Button>
                    <Button
                      onClick={() => {
                        console.log('Decline button clicked for org:', org.name, 'membership_id:', org.membership_id);
                        if (org.membership_id) {
                          declineInvitation(org.membership_id).catch(err => {
                            console.error('Error declining invitation:', err);
                          });
                        }
                      }}
                      disabled={orgsHookLoading || !org.membership_id}
                      className="w-full sm:w-auto flex-grow bg-red-600 hover:bg-red-700 text-white border-red-600 hover:border-red-700"
                      size="sm"
                    >
                      {orgsHookLoading ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <X className="mr-2 h-4 w-4" />
                      )}
                      Decline Invitation
                    </Button>
                  </div>
                </div>
              )}

              <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                {onManageOrganization && canManage(org.user_role) && org.approval_status === 'approved' && (
                  <Button
                    onClick={() => onManageOrganization(org)}
                    variant="outline"
                    size="sm"
                    className="w-full sm:w-auto"
                    disabled={orgsHookLoading}
                  >
                    <Settings className="mr-2 h-4 w-4" /> Manage
                  </Button>
                )}
                {onCreateEvent && canCreateEvents(org.user_role, org.approval_status) && (
                  <Button
                    onClick={() => onCreateEvent(org)}
                    variant="default"
                    size="sm"
                    className="w-full sm:w-auto flex-grow"
                    disabled={orgsHookLoading}
                  >
                    Create Event
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};
