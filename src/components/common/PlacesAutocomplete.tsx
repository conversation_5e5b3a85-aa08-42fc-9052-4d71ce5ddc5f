import React, { useState, useEffect, useRef } from 'react';
import { getPlacePredictions, getPlaceDetails, PlacePrediction, PlaceDetails } from '../../utils/googlePlacesLoader';
import { useUserCoordinates } from '../../hooks/useUserCoordinates';
import { DEFAULT_SEARCH_RADIUS_KM } from '../../config/maps';

interface PlacesAutocompleteProps {
  onPlaceSelect: (placeDetails: PlaceDetails) => void;
  placeholder?: string;
  className?: string;
  initialValue?: string;
}

const PlacesAutocomplete: React.FC<PlacesAutocompleteProps> = ({
  onPlaceSelect,
  placeholder = 'Search for a place',
  className = '',
  initialValue = '',
}) => {
  const [inputValue, setInputValue] = useState(initialValue);
  const [predictions, setPredictions] = useState<PlacePrediction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showPredictions, setShowPredictions] = useState(false);
  const [isSelectingPlace, setIsSelectingPlace] = useState(false);
  const userCoordinates = useUserCoordinates();

  // Set input value when component mounts or key changes
  useEffect(() => {
    setInputValue(initialValue);
    setPredictions([]);
    setShowPredictions(false);
  }, [initialValue]);

  // We don't need to request location as we use default location from config
  // Removing this effect to prevent infinite loops
  const inputRef = useRef<HTMLInputElement>(null);
  const predictionsRef = useRef<HTMLDivElement>(null);

  // Handle outside click to close predictions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        predictionsRef.current &&
        !predictionsRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowPredictions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // isSelectingPlace state is declared at the top of the component

  // Fetch predictions when input changes
  useEffect(() => {
    // Don't fetch predictions if we're in the process of selecting a place
    if (isSelectingPlace) return;

    // Don't fetch predictions if the input is empty or too short
    if (inputValue.length < 2) {
      setPredictions([]);
      setShowPredictions(false);
      return;
    }

    const fetchPredictions = async () => {
      setIsLoading(true);
      try {
        // Always use a location parameter - either from user location or default
        let locationParam;

        // Use the coordinates from our custom hook
        // This hook already handles extracting the coordinates from userLocation
        // or falling back to the default location
        locationParam = userCoordinates;
        console.log('Using coordinates for search:', locationParam);

        // Always pass the location parameter to restrict results
        const results = await getPlacePredictions(inputValue, locationParam);
        console.log('Places search results:', results.length);
        setPredictions(results);
        setShowPredictions(results.length > 0);
      } catch (error) {
        console.error('Error fetching predictions:', error);
        setPredictions([]);
        setShowPredictions(false);
      } finally {
        setIsLoading(false);
      }
    };

    const debounceTimer = setTimeout(fetchPredictions, 300);
    return () => clearTimeout(debounceTimer);
  }, [inputValue, isSelectingPlace]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    // Only show predictions if we have at least 2 characters and we're not in the process of selecting a place
    if (newValue.length >= 2 && !isSelectingPlace) {
      setShowPredictions(true);
    } else {
      setShowPredictions(false);
    }
  };

  const handlePredictionClick = async (prediction: PlacePrediction) => {
    // Set flag to prevent fetching predictions while selecting a place
    setIsSelectingPlace(true);

    // Set the input value to the full text of the prediction to show the complete address
    setInputValue(prediction.text.text);
    setShowPredictions(false);

    try {
      const placeDetails = await getPlaceDetails(prediction.placeId);
      if (placeDetails) {
        // Call the callback with the place details
        onPlaceSelect(placeDetails);
      }
    } catch (error) {
      console.error('Error fetching place details:', error);
    } finally {
      // Reset the flag after a short delay to allow the form to update
      setTimeout(() => setIsSelectingPlace(false), 500);
    }
  };

  return (
    <div className="relative w-full">
      <input
        ref={inputRef}
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        onFocus={() => inputValue.length >= 2 && predictions.length > 0 && setShowPredictions(true)}
        placeholder={placeholder}
        className={`w-full p-2 border rounded-md ${className}`}
        autoComplete="off"
      />

      {isLoading && (
        <div className="absolute right-3 top-3">
          <div className="animate-spin h-4 w-4 border-2 border-gray-500 rounded-full border-t-transparent"></div>
        </div>
      )}

      {showPredictions && (
        <div
          ref={predictionsRef}
          className="absolute z-10 w-full mt-1 bg-white border rounded-md shadow-lg max-h-60 overflow-y-auto"
        >
          {predictions.length > 0 ? (
            <>
              {predictions.map((prediction) => (
                <div
                  key={prediction.placeId}
                  className="p-2 hover:bg-gray-100 cursor-pointer"
                  onClick={() => handlePredictionClick(prediction)}
                >
                  <div className="font-medium">
                    {prediction.structuredFormat?.mainText.text || prediction.text.text}
                  </div>
                  {prediction.structuredFormat?.secondaryText && (
                    <div className="text-sm text-gray-600">
                      {prediction.structuredFormat.secondaryText.text}
                    </div>
                  )}
                </div>
              ))}
            </>
          ) : (
            <div className="p-3 text-sm text-gray-600">
              No places found within the search radius. Try a more specific search term.
            </div>
          )}
          <div className="p-1 text-right">
            <img
              src="https://developers.google.com/static/maps/documentation/images/powered_by_google_on_white.png"
              alt="Powered by Google"
              className="h-6 inline-block"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default PlacesAutocomplete;
