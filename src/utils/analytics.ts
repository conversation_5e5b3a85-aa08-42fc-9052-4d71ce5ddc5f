import { GA_TRACKING_ID, ANALYTICS_CONFIG, EventCategory, EventAction } from '@/config/analytics';

// Extend the Window interface to include gtag
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

// Debounce mechanism to prevent duplicate events
const eventDebounceMap = new Map<string, number>();
const DEBOUNCE_TIME = 1000; // 1 second

const shouldDebounceEvent = (eventKey: string): boolean => {
  const now = Date.now();
  const lastEventTime = eventDebounceMap.get(eventKey);

  if (lastEventTime && (now - lastEventTime) < DEBOUNCE_TIME) {
    return true; // Should debounce (skip this event)
  }

  eventDebounceMap.set(eventKey, now);
  return false; // Don't debounce (allow this event)
};

// Initialize Google Analytics
export const initializeAnalytics = (): void => {
  if (!ANALYTICS_CONFIG.enabled || !GA_TRACKING_ID) {
    if (ANALYTICS_CONFIG.debug) {
      console.log('Analytics disabled or tracking ID not provided');
    }
    return;
  }

  // Check if gtag is available
  if (typeof window.gtag === 'function') {
    window.gtag('config', GA_TRACKING_ID, ANALYTICS_CONFIG.config);
    
    if (ANALYTICS_CONFIG.debug) {
      console.log('Analytics initialized with tracking ID:', GA_TRACKING_ID);
    }
  } else {
    console.warn('Google Analytics gtag function not available');
  }
};

// Track page views
export const trackPageView = (page_path?: string, page_title?: string): void => {
  if (!ANALYTICS_CONFIG.enabled || typeof window.gtag !== 'function') {
    if (ANALYTICS_CONFIG.debug) {
      console.log('Page view tracked (debug):', { page_path, page_title });
    }
    return;
  }

  const params: any = {};
  if (page_path) params.page_path = page_path;
  if (page_title) params.page_title = page_title;

  window.gtag('event', 'page_view', params);
  
  if (ANALYTICS_CONFIG.debug) {
    console.log('Page view tracked:', params);
  }
};

// Track custom events
export const trackEvent = (
  action: EventAction | string,
  category: EventCategory | string,
  label?: string,
  value?: number,
  customParameters?: Record<string, any>
): void => {
  if (!ANALYTICS_CONFIG.enabled || typeof window.gtag !== 'function') {
    if (ANALYTICS_CONFIG.debug) {
      console.log('Event tracked (debug):', { action, category, label, value, customParameters });
    }
    return;
  }

  // Create a unique key for debouncing
  const eventKey = `${action}_${category}_${label || 'no_label'}`;

  // Check if we should debounce this event
  if (shouldDebounceEvent(eventKey)) {
    if (ANALYTICS_CONFIG.debug) {
      console.log('Event debounced (duplicate):', { action, category, label });
    }
    return;
  }

  const params: any = {
    event_category: category,
    ...(label && { event_label: label }),
    ...(value !== undefined && { value }),
    ...customParameters
  };

  window.gtag('event', action, params);

  if (ANALYTICS_CONFIG.debug) {
    console.log('Event tracked:', { action, ...params });
  }
};

// Track user authentication
export const trackAuth = (method: 'login' | 'logout' | 'signup', provider?: string): void => {
  trackEvent(
    method,
    'Authentication',
    provider,
    undefined,
    { method: provider || 'unknown' }
  );
};

// Track event interactions
export const trackEventInteraction = (
  action: 'view' | 'register' | 'share' | 'create',
  eventId?: string,
  eventTitle?: string
): void => {
  // Debug: Log what we're receiving
  if (ANALYTICS_CONFIG.debug || import.meta.env.DEV) {
    console.log('🔍 trackEventInteraction called with:', {
      action,
      eventId,
      eventTitle,
      eventTitleType: typeof eventTitle,
      eventTitleLength: eventTitle?.length
    });
  }

  // Use more specific event names to avoid conflicts with GA4 auto-tracking
  const eventName = `localadda_event_${action}`;

  // Ensure we have clean, non-empty values
  const cleanEventId = eventId?.toString().trim() || '';
  const cleanEventTitle = eventTitle?.toString().trim() || '';

  // Combine event title and ID for better analytics reporting
  const combinedLabel = cleanEventTitle && cleanEventId
    ? `${cleanEventTitle} (ID: ${cleanEventId})`
    : cleanEventTitle || cleanEventId || 'Unknown Event';

  if (ANALYTICS_CONFIG.debug || import.meta.env.DEV) {
    console.log('🏷️ Combined label created:', combinedLabel);
  }

  trackEvent(
    eventName,
    'Event Interaction',
    combinedLabel,
    undefined,
    {
      event_id: cleanEventId,
      event_title: cleanEventTitle,
      custom_event_type: action,
      source: 'localadda_custom_tracking',
      // Additional debugging info
      original_event_id: eventId,
      original_event_title: eventTitle
    }
  );
};

// Track search queries
export const trackSearch = (searchTerm: string, resultCount?: number): void => {
  // Create a more descriptive label with search term and result count
  const searchLabel = resultCount !== undefined
    ? `"${searchTerm}" (${resultCount} results)`
    : `"${searchTerm}"`;

  trackEvent(
    'search',
    'Search',
    searchLabel,
    resultCount,
    {
      search_term: searchTerm,
      result_count: resultCount,
      source: 'localadda_search'
    }
  );
};

// Track form interactions
export const trackForm = (
  action: 'start' | 'submit' | 'error',
  formName: string,
  errorMessage?: string
): void => {
  // Create descriptive label with form name and action
  const formLabel = errorMessage
    ? `${formName} - ${action} (Error: ${errorMessage})`
    : `${formName} - ${action}`;

  trackEvent(
    `form_${action}`,
    'Form Submission',
    formLabel,
    undefined,
    {
      form_name: formName,
      form_action: action,
      ...(errorMessage && { error_message: errorMessage }),
      source: 'localadda_form'
    }
  );
};

// Track errors
export const trackError = (errorMessage: string, errorLocation?: string): void => {
  trackEvent(
    'error_occurred',
    'Error',
    errorLocation,
    undefined,
    { error_message: errorMessage }
  );
};

// Track performance metrics
export const trackTiming = (
  name: string,
  value: number,
  category: string = 'Performance'
): void => {
  if (!ANALYTICS_CONFIG.enabled || typeof window.gtag !== 'function') {
    if (ANALYTICS_CONFIG.debug) {
      console.log('Timing tracked (debug):', { name, value, category });
    }
    return;
  }

  window.gtag('event', 'timing_complete', {
    name,
    value,
    event_category: category
  });
  
  if (ANALYTICS_CONFIG.debug) {
    console.log('Timing tracked:', { name, value, category });
  }
};

// Set user properties
export const setUserProperties = (properties: Record<string, any>): void => {
  if (!ANALYTICS_CONFIG.enabled || typeof window.gtag !== 'function') {
    if (ANALYTICS_CONFIG.debug) {
      console.log('User properties set (debug):', properties);
    }
    return;
  }

  window.gtag('set', properties);
  
  if (ANALYTICS_CONFIG.debug) {
    console.log('User properties set:', properties);
  }
};

// Set user ID for cross-device tracking
export const setUserId = (userId: string): void => {
  if (!ANALYTICS_CONFIG.enabled || typeof window.gtag !== 'function') {
    if (ANALYTICS_CONFIG.debug) {
      console.log('User ID set (debug):', userId);
    }
    return;
  }

  window.gtag('config', GA_TRACKING_ID, {
    user_id: userId,
    // Additional settings for better user identification
    custom_map: {
      'custom_user_id': userId
    }
  });

  if (ANALYTICS_CONFIG.debug) {
    console.log('User ID set:', userId);
  }
};

// Clear user ID when user logs out
export const clearUserId = (): void => {
  if (!ANALYTICS_CONFIG.enabled || typeof window.gtag !== 'function') {
    if (ANALYTICS_CONFIG.debug) {
      console.log('User ID cleared (debug)');
    }
    return;
  }

  window.gtag('config', GA_TRACKING_ID, {
    user_id: null
  });

  if (ANALYTICS_CONFIG.debug) {
    console.log('User ID cleared');
  }
};

// Generate a consistent anonymous user identifier
export const generateAnonymousUserId = (): string => {
  // Check if we already have an anonymous ID stored
  let anonymousId = localStorage.getItem('localadda_anonymous_id');

  if (!anonymousId) {
    // Generate a new anonymous ID
    anonymousId = 'anon_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now().toString(36);
    localStorage.setItem('localadda_anonymous_id', anonymousId);
  }

  return anonymousId;
};

// Set anonymous user tracking for non-logged-in users
export const setAnonymousUser = (): void => {
  const anonymousId = generateAnonymousUserId();

  setUserProperties({
    user_type: 'anonymous',
    anonymous_id: anonymousId,
    login_method: 'none'
  });

  if (ANALYTICS_CONFIG.debug) {
    console.log('Anonymous user set:', anonymousId);
  }
};
