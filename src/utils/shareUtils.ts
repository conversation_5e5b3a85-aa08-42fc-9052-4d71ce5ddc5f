import { createClient } from '@supabase/supabase-js';
import { toast } from '@/hooks/use-toast';

const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL!,
  import.meta.env.VITE_SUPABASE_ANON_KEY!
);

/**
 * Checks if the Web Share API is available in the current browser
 */
export function isWebShareSupported(): boolean {
  return !!navigator.share;
}

/**
 * Checks if the Web Share API with file sharing is supported
 */
export function isFileShareSupported(): boolean {
  return !!navigator.canShare && !!navigator.share;
}

/**
 * Fetches a poster image from Supabase storage and converts it to a File object
 * @param posterFilename The filename of the poster in the event-posters bucket
 * @param eventTitle The title of the event (used for the file name)
 * @returns A File object containing the poster image or null if fetching failed
 */
export async function fetchPosterAsFile(
  posterFilename: string,
  eventTitle: string
): Promise<File | null> {
  try {
    const { data, error } = await supabase.storage
      .from('event-posters')
      .download(posterFilename);

    if (error || !data) {
      console.error('Error downloading poster:', error);
      return null;
    }

    // Create a File object from the blob
    return new File([data], `${eventTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_poster.jpg`, {
      type: 'image/jpeg'
    });
  } catch (error) {
    console.error('Exception downloading poster:', error);
    return null;
  }
}

/**
 * Shares an event using the Web Share API
 * @param eventTitle The title of the event
 * @param eventDescription A short description of the event
 * @param eventUrl The URL to the event page
 * @param posterFilename Optional filename (not the full URL) of the poster image in the event-posters bucket.
 *                      The function will download the file from Supabase storage and create a File object.
 */
export async function shareEvent(
  eventTitle: string,
  eventDescription: string,
  eventUrl: string,
  posterFilename?: string | null
): Promise<void> {
  try {
    // Prepare the share data
    const shareData: ShareData = {
      title: eventTitle,
      text: eventDescription,
      url: eventUrl
    };

    // If poster filename is provided and file sharing is supported, include the poster
    if (posterFilename && isFileShareSupported()) {
      const posterFile = await fetchPosterAsFile(posterFilename, eventTitle);

      if (posterFile) {
        const filesArray = [posterFile];

        // Check if we can share this specific file
        if (navigator.canShare && navigator.canShare({ files: filesArray })) {
          shareData.files = filesArray;
        }
      }
    }

    // Share the data
    await navigator.share(shareData);

    toast({
      title: 'Shared successfully',
      description: 'Event has been shared',
    });
  } catch (error) {
    // User cancelled or sharing failed
    if ((error as Error).name !== 'AbortError') {
      console.error('Error sharing event:', error);
      toast({
        title: 'Sharing failed',
        description: 'Could not share the event. Try copying the link instead.',
        variant: 'destructive',
      });
    }
  }
}

/**
 * Fallback function to copy the event URL to clipboard
 * @param eventUrl The URL to copy
 */
export async function copyEventUrl(eventUrl: string): Promise<void> {
  try {
    await navigator.clipboard.writeText(eventUrl);
    toast({
      title: 'Link copied',
      description: 'Event link copied to clipboard',
    });
  } catch (error) {
    console.error('Error copying to clipboard:', error);
    toast({
      title: 'Copy failed',
      description: 'Could not copy the link to clipboard',
      variant: 'destructive',
    });
  }
}
