import { createClient } from '@supabase/supabase-js';
import { format } from 'date-fns';
import { tailwindToHexMap, hexToImageScriptColor } from './tailwindColors';

const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL!,
  import.meta.env.VITE_SUPABASE_ANON_KEY!
);

/**
 * Interface for the payload required by the create-poster edge function
 */
interface PosterGenerationPayload {
  eventImagePath: string;
  outputPosterPath: string;
  title: string;
  date: string;
  startTime: string;
  endTime?: string;
  location: string;
  category: {
    name: string;
    color: number;
    textColor: number;
  };
}

/**
 * Generates a poster for an event using the create-poster edge function
 * @param eventId The ID of the event
 * @param eventImagePath Path of the event image in the event-images bucket
 * @param title Event title
 * @param startDate Event start date
 * @param startTime Event start time
 * @param endTime Optional event end time
 * @param location Event location (venue name, locality)
 * @param categoryName Category name
 * @param categoryColor Category background color as Tailwind class (e.g., 'bg-blue-100')
 * @param categoryTextColor Category text color as Tailwind class (e.g., 'text-blue-800')
 * @returns The filename of the generated poster or null if generation failed
 */
export async function generateEventPoster(
  eventId: string,
  eventImagePath: string,
  title: string,
  startDate: Date,
  startTime: string,
  endTime: string | null,
  location: string,
  categoryName: string,
  categoryColor: string,
  categoryTextColor: string
): Promise<string | null> {
  try {
    const session = await supabase.auth.getSession();
    const token = session.data.session?.access_token;

    if (!token) {
      console.error('No authentication token available');
      return null;
    }

    // Format the date for display
    const formattedDate = format(startDate, 'EEEE, MMMM d, yyyy');
    
    // Generate a unique filename for the poster
    const outputPosterFilename = `${eventId}_poster.jpg`;

    // Convert Tailwind color classes to ARGB values
    const bgColorHex = tailwindToHexMap[categoryColor] || '#DBEAFE'; // Default to blue if not found
    const textColorHex = tailwindToHexMap[categoryTextColor] || '#1E40AF'; // Default to blue if not found
    
    const payload: PosterGenerationPayload = {
      eventImagePath,
      outputPosterPath: outputPosterFilename,
      title,
      date: formattedDate,
      startTime,
      ...(endTime && { endTime }),
      location,
      category: {
        name: categoryName,
        color: hexToImageScriptColor(bgColorHex),
        textColor: hexToImageScriptColor(textColorHex)
      }
    };

    const response = await fetch(
      `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/create-poster`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Poster generation failed:', errorData.error || response.statusText);
      return null;
    }

    const result = await response.json();
    
    // Return just the filename, not the full URL
    return outputPosterFilename;
  } catch (error) {
    console.error('Error generating poster:', error);
    return null;
  }
}

/**
 * Updates the event record with the poster image filename
 * @param eventId The ID of the event
 * @param posterFilename The filename of the generated poster
 * @returns True if the update was successful, false otherwise
 */
export async function updateEventWithPosterFilename(
  eventId: string,
  posterFilename: string
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('events')
      .update({ poster_image_filename: posterFilename })
      .eq('id', eventId);

    if (error) {
      console.error('Error updating event with poster filename:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Exception updating event with poster filename:', error);
    return false;
  }
}
