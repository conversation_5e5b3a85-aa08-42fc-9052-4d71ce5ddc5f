/**
 * Simple cache utility for storing data with expiration
 */

interface CacheItem<T> {
  data: T;
  timestamp: number;
}

class DataCache {
  private cache: Record<string, CacheItem<any>> = {};
  private readonly DEFAULT_EXPIRY = 5 * 60 * 1000; // 5 minutes in milliseconds

  /**
   * Set data in the cache with a key
   * @param key - Cache key
   * @param data - Data to cache
   * @param expiryMs - Optional expiry time in milliseconds
   */
  set<T>(key: string, data: T, expiryMs?: number): void {
    this.cache[key] = {
      data,
      timestamp: Date.now() + (expiryMs || this.DEFAULT_EXPIRY)
    };
  }

  /**
   * Get data from the cache
   * @param key - Cache key
   * @returns The cached data or null if not found or expired
   */
  get<T>(key: string): T | null {
    const item = this.cache[key];
    
    // Return null if item doesn't exist or has expired
    if (!item || Date.now() > item.timestamp) {
      if (item) {
        // Clean up expired item
        delete this.cache[key];
      }
      return null;
    }
    
    return item.data as T;
  }

  /**
   * Check if a key exists in the cache and is not expired
   * @param key - Cache key
   * @returns <PERSON><PERSON><PERSON> indicating if the key exists and is valid
   */
  has(key: string): boolean {
    const item = this.cache[key];
    if (!item || Date.now() > item.timestamp) {
      if (item) {
        // Clean up expired item
        delete this.cache[key];
      }
      return false;
    }
    return true;
  }

  /**
   * Remove an item from the cache
   * @param key - Cache key
   */
  remove(key: string): void {
    delete this.cache[key];
  }

  /**
   * Clear all items from the cache
   */
  clear(): void {
    this.cache = {};
  }
  
  /**
   * Get all cache keys
   * @returns Array of all cache keys
   */
  getKeys(): string[] {
    return Object.keys(this.cache);
  }
}

// Create a singleton instance
export const dataCache = new DataCache();
