import { loadGoogleMapsScript } from './googleMapsLoader';

export interface GeocodingResult {
  coordinates: { lat: number; lng: number } | null;
  locality: string | null;
  sublocality_level_1: string | null;
  placeId?: string | null;
  postcode_localities?: string[] | null;
  error?: Error | string | null;
}

// Removed default location - we now require explicit location selection

const LOCAL_STORAGE_KEY = 'userLocation';
const MAPS_API_ERROR = 'Google Maps API not fully loaded';

/**
 * Helper function to extract desired location details from a GeocoderResult
 */
export const extractLocationDetails = (result: google.maps.GeocoderResult): GeocodingResult => {
  let locality: string | null = null;
  let sublocality_level_1: string | null = null;
  const coordinates = result.geometry?.location ? { lat: result.geometry.location.lat(), lng: result.geometry.location.lng() } : null;
  const placeId = result.place_id || null;

  if (result.address_components) {
    for (const component of result.address_components) {
      const types = component.types;

      if (types.includes('sublocality_level_1') && component.short_name) {
        sublocality_level_1 = component.short_name;
      } else if (types.includes('locality') && component.long_name) {
        locality = component.long_name;
      }
    }
  }

  // Simple fallback logic if primary fields missing
  if (!locality && result.address_components) {
    for (const component of result.address_components) {
      if (component.types.includes('sublocality') || component.types.includes('neighborhood')) {
        locality = component.long_name;
        break;
      }
    }
  }
  if (!locality) {
    locality = sublocality_level_1;
  }

  return {
    coordinates,
    locality,
    sublocality_level_1,
    placeId,
  };
};

/**
 * Geocode based on latitude and longitude.
 * @param latitude Latitude coordinate
 * @param longitude Longitude coordinate
 * @returns Promise resolving to raw Google Geocoder results array or rejecting on error.
 */
export const geocodeByLatLng = async (
  latitude: number,
  longitude: number
): Promise<google.maps.GeocoderResult[]> => {
  try {
    await loadGoogleMapsScript();
    if (!window.google?.maps?.Geocoder) {
      throw new Error(MAPS_API_ERROR);
    }
    const geocoder = new window.google.maps.Geocoder();
    const latlng = { lat: latitude, lng: longitude };

    return new Promise<google.maps.GeocoderResult[]>((resolve, reject) => {
      geocoder.geocode({ location: latlng }, (results, status) => {
        if (status === 'OK' && results) {
          resolve(results);
        } else {
          console.warn('geocodeByLatLng failed:', status);
          reject(new Error(`Geocoding failed: ${status || 'No results'}`));
        }
      });
    });
  } catch (error) {
    console.error('geocodeByLatLng error:', error);
    throw error;
  }
};

/**
 * Geocode based on an address string.
 * @param address The address string to geocode.
 * @returns Promise resolving to raw Google Geocoder results array or rejecting on error.
 */
export const geocodeByAddress = async (
  address: string
): Promise<google.maps.GeocoderResult[]> => {
  try {
    await loadGoogleMapsScript();
    if (!window.google?.maps?.Geocoder) {
      throw new Error(MAPS_API_ERROR);
    }
    const geocoder = new window.google.maps.Geocoder();
    const request: google.maps.GeocoderRequest = { address };

    console.log('Geocoding by address request:', request);

    return new Promise<google.maps.GeocoderResult[]>((resolve, reject) => {
      geocoder.geocode(request, (results, status) => {
        if (status === 'OK' && results) {
          resolve(results);
        } else {
          console.warn('geocodeByAddress failed:', status);
          reject(new Error(`Geocoding failed for address \'${address}\'. Status: ${status}`));
        }
      });
    });
  } catch (error) {
    console.error('geocodeByAddress error:', error);
    throw error;
  }
};

/**
 * Geocode based on address components (e.g., postal code).
 * @param components Object containing component restrictions (e.g., { postalCode: '110017', country: 'IN' }).
 * @returns Promise resolving to raw Google Geocoder results array or rejecting on error.
 */
export const geocodeByComponent = async (
  components: google.maps.GeocoderComponentRestrictions
): Promise<google.maps.GeocoderResult[]> => {
  try {
    await loadGoogleMapsScript();
    if (!window.google?.maps?.Geocoder) {
      throw new Error(MAPS_API_ERROR);
    }
    const geocoder = new window.google.maps.Geocoder();
    const request: google.maps.GeocoderRequest = { componentRestrictions: components };

    console.log('Geocoding by component request:', request);

    return new Promise<google.maps.GeocoderResult[]>((resolve, reject) => {
      geocoder.geocode(request, (results, status) => {
        if (status === 'OK' && results) {
          resolve(results);
        } else {
          console.warn('geocodeByComponent failed:', status);
          reject(new Error(`Geocoding failed for components ${JSON.stringify(components)}. Status: ${status}`));
        }
      });
    });
  } catch (error) {
    console.error('geocodeByComponent error:', error);
    throw error;
  }
};

// Save location data to localStorage
export const saveLocationToStorage = (
  coords: { lat: number; lng: number },
  locality: string | null,
  sublocality_level_1: string | null
) => {
  const locationData = {
    coords,
    locality: locality || 'Unknown location',
    sublocality_level_1: sublocality_level_1 || 'Unknown sublocality',
    timestamp: Date.now()
  };

  localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(locationData));
};

// Clear location data from localStorage
export const clearStoredLocation = (): void => {
  try {
    localStorage.removeItem(LOCAL_STORAGE_KEY);
  } catch (error) {
    console.error('Error clearing stored location:', error);
  }
};

/**
 * Get stored location from local storage
 * @returns Stored location data or null if not found
 */
export const getStoredLocation = () => {
  const locationData = localStorage.getItem(LOCAL_STORAGE_KEY);
  if (!locationData) return null;

  try {
    return JSON.parse(locationData);
  } catch (error) {
    console.error('Error parsing stored location:', error);
    return null;
  }
};

/**
 * Check if stored location is still valid (not expired)
 * @param maxAgeMs Maximum age in milliseconds
 * @returns Boolean indicating if stored location is valid
 */
export const isStoredLocationValid = (maxAgeMs = 24 * 60 * 60 * 1000) => {
  const locationData = getStoredLocation();
  if (!locationData || !locationData.timestamp) return false;

  const now = Date.now();
  const age = now - locationData.timestamp;

  return age < maxAgeMs;
};

export interface StoredLocationData {
  coords: { lat: number; lng: number };
  locality: string | null;
  sublocality_level_1: string | null;
  timestamp: number;
}
