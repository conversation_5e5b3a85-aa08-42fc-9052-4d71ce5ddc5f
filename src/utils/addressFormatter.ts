import { PlaceDetails } from './googlePlacesLoader';

/**
 * Extracts and formats the address from place details
 * Uses premise, street_number, route, and sublocality_level_2 components as available
 */
export const formatDetailedAddress = (placeDetails: PlaceDetails): string => {
  // Initialize address parts array
  const addressParts: string[] = [];

  // Extract the components we want for the address
  let premise = '';
  let streetNumber = '';
  let route = '';
  let sublocality2 = '';

  // Extract each component if available
  placeDetails.addressComponents.forEach(component => {
    if (component.types.includes('premise')) {
      premise = component.longText;
    } else if (component.types.includes('street_number')) {
      streetNumber = component.longText;
    } else if (component.types.includes('route')) {
      route = component.longText;
    } else if (component.types.includes('sublocality_level_2')) {
      sublocality2 = component.longText;
    }
  });

  // Add components to address parts if they exist
  if (premise) addressParts.push(premise);
  if (streetNumber) addressParts.push(streetNumber);
  if (route) addressParts.push(route);
  if (sublocality2) addressParts.push(sublocality2);

  // If no address parts were found, fall back to the first part of the formatted address
  if (addressParts.length === 0 && placeDetails.formattedAddress) {
    return placeDetails.formattedAddress.split(',')[0];
  }

  // Join the address parts with commas
  return addressParts.join(', ');
};

/**
 * Extracts the locality (sublocality_level_1) from place details
 * This is typically the neighborhood or district
 */
export const extractLocality = (placeDetails: PlaceDetails): string | null => {
  // Look for sublocality_level_1 in the address components
  const sublocalityComponent = placeDetails.addressComponents.find(component =>
    component.types.includes('sublocality_level_1')
  );

  // Return the short text if found, otherwise null
  return sublocalityComponent ? sublocalityComponent.shortText : null;
};
