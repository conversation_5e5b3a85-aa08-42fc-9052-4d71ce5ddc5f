import { GOOGLE_MAPS_API_KEY, DEFAULT_SEARCH_RADIUS_KM } from '../config/maps';

export interface PlacePrediction {
  place: string;
  placeId: string;
  text: {
    text: string;
    matches?: Array<{
      startOffset?: number;
      endOffset?: number;
    }>;
  };
  structuredFormat?: {
    mainText: {
      text: string;
      matches?: Array<{
        startOffset?: number;
        endOffset?: number;
      }>;
    };
    secondaryText: {
      text: string;
    };
  };
  types?: string[];
  distanceMeters?: number;
}

export interface PlaceDetails {
  id: string;
  name: string;
  formattedAddress: string;
  addressComponents: Array<{
    longText: string;
    shortText: string;
    types: string[];
  }>;
  location: {
    latitude: number;
    longitude: number;
  };
  plusCode?: {
    globalCode: string;
    compoundCode: string;
  };
}

export interface PlacesAutocompleteResponse {
  suggestions: Array<{
    placePrediction: PlacePrediction;
  }>;
}

export interface PlaceDetailsResponse {
  id: string;
  displayName: {
    text: string;
    languageCode: string;
  };
  formattedAddress: string;
  addressComponents: Array<{
    longText: string;
    shortText: string;
    types: string[];
  }>;
  location: {
    latitude: number;
    longitude: number;
  };
  plusCode?: {
    globalCode: string;
    compoundCode: string;
  };
}

/**
 * Fetches place predictions from Google Places (New) API
 */
export const getPlacePredictions = async (
  input: string,
  userLocation?: { latitude: number; longitude: number }
): Promise<PlacePrediction[]> => {
  if (!input || input.length < 2) {
    return [];
  }

  try {
    console.log('getPlacePredictions called with:', { input, userLocation });

    const requestBody: any = {
      input,
    };

    // Always add location restriction - either from user location or default
    // Restricting to configured radius as per user preference for better local relevance
    if (userLocation) {
      console.log('Adding location restriction with user location:', userLocation);
      requestBody.locationRestriction = {
        circle: {
          center: {
            latitude: userLocation.latitude,
            longitude: userLocation.longitude,
          },
          radius: DEFAULT_SEARCH_RADIUS_KM * 1000, // Convert km to meters - user preference for hyper-local results
        },
      };
    } else {
      // If no user location provided, use default location from config
      console.log('No user location provided, using default location for restriction');
      // This should never happen as we always pass a location from the component
      // But adding as a fallback just in case
      requestBody.locationRestriction = {
        circle: {
          center: {
            latitude: 28.5403056, // Default to C.R.Park in Delhi
            longitude: 77.2486667,
          },
          radius: DEFAULT_SEARCH_RADIUS_KM * 1000, // Convert km to meters
        },
      };
    }

    console.log('Final request body:', JSON.stringify(requestBody, null, 2));

    // Log the exact request being sent
    const requestBodyString = JSON.stringify(requestBody);
    console.log('Places API request:', requestBodyString);

    const response = await fetch('https://places.googleapis.com/v1/places:autocomplete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Goog-Api-Key': GOOGLE_MAPS_API_KEY,
        'X-Goog-FieldMask': '*',
      },
      body: requestBodyString,
    });

    if (!response.ok) {
      throw new Error(`Places API error: ${response.status}`);
    }

    const data = await response.json() as PlacesAutocompleteResponse;
    return data.suggestions.map(suggestion => suggestion.placePrediction);
  } catch (error) {
    console.error('Error fetching place predictions:', error);
    return [];
  }
};

/**
 * Fetches place details from Google Places (New) API
 */
export const getPlaceDetails = async (placeId: string): Promise<PlaceDetails | null> => {
  try {
    const response = await fetch(`https://places.googleapis.com/v1/places/${placeId}`, {
      method: 'GET',
      headers: {
        'X-Goog-Api-Key': GOOGLE_MAPS_API_KEY,
        'X-Goog-FieldMask': 'id,displayName,formattedAddress,addressComponents,location,plusCode',
      },
    });

    if (!response.ok) {
      throw new Error(`Place Details API error: ${response.status}`);
    }

    const data = await response.json() as PlaceDetailsResponse;

    return {
      id: data.id || placeId, // Use the placeId parameter as fallback
      name: data.displayName.text,
      formattedAddress: data.formattedAddress,
      addressComponents: data.addressComponents,
      location: data.location,
      plusCode: data.plusCode
    };
  } catch (error) {
    console.error('Error fetching place details:', error);
    return null;
  }
};
