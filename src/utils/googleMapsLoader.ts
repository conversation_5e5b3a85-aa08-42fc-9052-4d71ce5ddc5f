import { GOOGLE_MAPS_API_KEY } from '@/config/maps';

// Global variable to track if we're already loading the script
let isLoadingScript = false;
let isScriptLoaded = false;

/**
 * Centralized utility to load the Google Maps API script
 * This ensures we only load the script once across the entire application
 */
export const loadGoogleMapsScript = (): Promise<void> => {
  // If the API is already loaded, return immediately
  if (window.google?.maps?.Geocoder && isScriptLoaded) {
    return Promise.resolve();
  }

  // Check if the script tag already exists
  const existingScript = document.querySelector(`script[src*="maps.googleapis.com/maps/api/js"]`);
  if (existingScript) {
    return new Promise<void>((resolve) => {
      // If the script exists but hasn't loaded yet, wait for it
      if (window.google?.maps?.Geocoder) {
        isScriptLoaded = true;
        resolve();
      } else {
        existingScript.addEventListener('load', () => {
          isScriptLoaded = true;
          resolve();
        });
      }
    });
  }

  // Check if another component is already loading the script
  if (isLoadingScript) {
    // Wait for the script to be loaded by the other component
    return new Promise<void>((resolve) => {
      const checkIfLoaded = () => {
        if (window.google?.maps?.Geocoder) {
          isScriptLoaded = true;
          resolve();
        } else {
          setTimeout(checkIfLoaded, 100);
        }
      };
      checkIfLoaded();
    });
  }

  // Otherwise, create and load the script
  isLoadingScript = true;
  return new Promise<void>((resolve, reject) => {
    // Define the callback function before creating the script
    window.initMap = () => {
      // Make sure the Geocoder service is available
      if (window.google?.maps?.Geocoder) {
        isScriptLoaded = true;
        isLoadingScript = false;
        resolve();
      } else {
        // If Geocoder is not available, wait a bit and check again
        setTimeout(() => {
          if (window.google?.maps?.Geocoder) {
            isScriptLoaded = true;
            isLoadingScript = false;
            resolve();
          } else {
            reject(new Error('Google Maps Geocoder service not available'));
          }
        }, 500);
      }
    };

    const script = document.createElement('script');
    // Use the recommended format for the script URL, ensure we load the Geocoder service
    script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&libraries=places,geocoding&callback=initMap&v=weekly`;
    script.async = true;
    script.defer = true;
    script.id = 'google-maps-script'; // Add an ID to easily identify this script
    
    script.onerror = () => {
      isLoadingScript = false;
      reject(new Error('Failed to load Google Maps API'));
    };
    
    document.head.appendChild(script);
  });
};

// Add the type definition for the global initMap function
declare global {
  interface Window {
    initMap: () => void;
    google: any;
  }
}
