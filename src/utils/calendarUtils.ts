/**
 * Utility functions for generating calendar links
 */

interface CalendarEvent {
  title: string;
  description: string;
  location: string;
  startDate: Date;
  endDate?: Date | null;
  url?: string;
}

/**
 * Format a date for Google Calendar URL
 * Format: YYYYMMDDTHHMMSSZ
 */
function formatDateForGoogle(date: Date): string {
  return date.toISOString().replace(/-|:|\.\d+/g, '');
}

/**
 * Format a date for iCal/Outlook URL
 * Format: YYYY-MM-DDTHH:MM:SS
 */
function formatDateForICal(date: Date): string {
  return date.toISOString().replace(/\.\d+Z$/, '');
}

/**
 * Generate a Google Calendar URL
 */
export function generateGoogleCalendarUrl(event: CalendarEvent): string {
  const startDate = formatDateForGoogle(event.startDate);
  const endDate = event.endDate ? formatDateForGoogle(event.endDate) : startDate;
  
  const params = new URLSearchParams({
    action: 'TEMPLATE',
    text: event.title,
    details: event.description,
    location: event.location,
    dates: `${startDate}/${endDate}`
  });
  
  if (event.url) {
    params.append('sprop', 'website:' + event.url);
  }
  
  return `https://calendar.google.com/calendar/render?${params.toString()}`;
}

/**
 * Generate an Outlook.com Calendar URL
 */
export function generateOutlookCalendarUrl(event: CalendarEvent): string {
  const startDate = formatDateForICal(event.startDate);
  const endDate = event.endDate ? formatDateForICal(event.endDate) : formatDateForICal(new Date(event.startDate.getTime() + 3600000)); // Default to 1 hour
  
  const params = new URLSearchParams({
    path: '/calendar/action/compose',
    rru: 'addevent',
    subject: event.title,
    body: event.description,
    location: event.location,
    startdt: startDate,
    enddt: endDate
  });
  
  return `https://outlook.live.com/calendar/0/${params.toString()}`;
}

/**
 * Generate an iCalendar file content
 */
export function generateICalContent(event: CalendarEvent): string {
  const startDate = formatDateForICal(event.startDate).replace(/[-:]/g, '');
  const endDate = event.endDate 
    ? formatDateForICal(event.endDate).replace(/[-:]/g, '')
    : formatDateForICal(new Date(event.startDate.getTime() + 3600000)).replace(/[-:]/g, ''); // Default to 1 hour
  
  const now = formatDateForICal(new Date()).replace(/[-:]/g, '');
  
  return [
    'BEGIN:VCALENDAR',
    'VERSION:2.0',
    'PRODID:-//LocalAdda//Event Calendar//EN',
    'CALSCALE:GREGORIAN',
    'BEGIN:VEVENT',
    `DTSTART:${startDate}`,
    `DTEND:${endDate}`,
    `DTSTAMP:${now}`,
    `SUMMARY:${event.title}`,
    `DESCRIPTION:${event.description.replace(/\n/g, '\\n')}`,
    `LOCATION:${event.location}`,
    'END:VEVENT',
    'END:VCALENDAR'
  ].join('\r\n');
}

/**
 * Generate a downloadable iCal file URL
 */
export function generateICalFileUrl(event: CalendarEvent): string {
  const content = generateICalContent(event);
  const blob = new Blob([content], { type: 'text/calendar;charset=utf-8' });
  return URL.createObjectURL(blob);
}

/**
 * Generate a Yahoo Calendar URL
 */
export function generateYahooCalendarUrl(event: CalendarEvent): string {
  const startDate = formatDateForICal(event.startDate).replace(/[-:]/g, '');
  const endDate = event.endDate 
    ? formatDateForICal(event.endDate).replace(/[-:]/g, '')
    : formatDateForICal(new Date(event.startDate.getTime() + 3600000)).replace(/[-:]/g, ''); // Default to 1 hour
  
  const params = new URLSearchParams({
    v: '60',
    title: event.title,
    desc: event.description,
    in_loc: event.location,
    st: startDate,
    et: endDate
  });
  
  return `https://calendar.yahoo.com/?${params.toString()}`;
}
