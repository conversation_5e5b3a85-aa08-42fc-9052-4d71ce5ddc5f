export const tailwindToHexMap: Record<string, string> = {
  'bg-blue-100': '#DBEAFE',
  'bg-green-100': '#D1FAE5',
  'bg-red-100': '#FECACA',
  'bg-yellow-100': '#FEF9C3',
  'bg-purple-100': '#E9D5FF',
  'bg-pink-100': '#FBCFE8',
  'bg-indigo-100': '#E0E7FF',
  'bg-cyan-100': '#CFFAFE',
  'bg-gray-100': '#F3F4F6',

  'text-blue-800': '#1E40AF',
  'text-green-800': '#065F46',
  'text-red-800': '#991B1B',
  'text-yellow-800': '#92400E',
  'text-purple-800': '#6B21A8',
  'text-pink-800': '#9D174D',
  'text-indigo-800': '#3730A3',
  'text-cyan-800': '#155E75',
  'text-gray-800': '#1F2937',
};

/**
 * Converts a hex color string to ARGB number format required by the poster generation function
 * @param hex Hex color string (e.g., '#DBEAFE')
 * @returns ARGB value as a number with full opacity (0xFF alpha channel)
 */

export function hexToImageScriptColor(hexColor: string): number {
  if (!hexColor || typeof hexColor !== 'string') {
    throw new Error('Invalid hex color string provided.');
  }

  // Remove leading '#' if present
  let hex = hexColor.startsWith('#') ? hexColor.slice(1) : hexColor;

  // Handle shorthand hex codes (e.g., #F03 -> #FF0033)
  if (hex.length === 3) {
    // RGB shorthand
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
  } else if (hex.length === 4) {
    // RGBA shorthand
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2] + hex[3] + hex[3];
  }

  // Validate length (must be 6 for RGB or 8 for RGBA)
  if (hex.length !== 6 && hex.length !== 8) {
    throw new Error(`Invalid hex color string length: "${hexColor}". Must be #RGB, #RRGGBB, #RGBA, or #RRGGBBAA.`);
  }

  // Ensure hex contains only valid hex characters
  if (!/^[0-9a-fA-F]+$/.test(hex)) {
      throw new Error(`Invalid characters in hex color string: "${hexColor}".`);
  }

  // Parse R, G, B components
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  // Parse A component, default to 255 (FF) if not present
  const a = hex.length === 8 ? parseInt(hex.substring(6, 8), 16) : 255;

  // Combine into a 32-bit RGBA integer (0xRRGGBBAA)
  // Use unsigned right shift (>>> 0) to ensure the result is treated as a 32-bit unsigned integer
  const rgbaNumber = ((r << 24) | (g << 16) | (b << 8) | a) >>> 0;

  return rgbaNumber;
}
