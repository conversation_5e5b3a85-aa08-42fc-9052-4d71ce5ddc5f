// Google Analytics configuration
export const GA_TRACKING_ID = import.meta.env.VITE_GA_TRACKING_ID || 'G-K5ERWJ7N4Y';

// Analytics configuration
export const ANALYTICS_CONFIG = {
  // Enable analytics when tracking ID is provided (now always available)
  enabled: <PERSON><PERSON>an(GA_TRACKING_ID),
  
  // Debug mode for development
  debug: import.meta.env.DEV && import.meta.env.VITE_GA_DEBUG === 'true',
  
  // Default configuration for gtag
  config: {
    // Disable automatic page views to prevent double counting
    send_page_view: false,
    // Respect user privacy
    anonymize_ip: true,
    // Cookie settings for better user identification
    cookie_expires: 63072000, // 2 years in seconds
    cookie_update: true,
    cookie_flags: 'SameSite=None;Secure',
    // Enhanced user identification
    allow_google_signals: true, // Enables cross-device tracking
    allow_ad_personalization_signals: false, // Privacy-friendly
  }
};

// Event categories for consistent tracking
export const EVENT_CATEGORIES = {
  USER_ENGAGEMENT: 'User Engagement',
  NAVIGATION: 'Navigation',
  EVENT_INTERACTION: 'Event Interaction',
  AUTHENTICATION: 'Authentication',
  SEARCH: 'Search',
  FORM_SUBMISSION: 'Form Submission',
  ERROR: 'Error',
  PERFORMANCE: 'Performance'
} as const;

// Common event actions
export const EVENT_ACTIONS = {
  // Navigation
  PAGE_VIEW: 'page_view',
  CLICK: 'click',
  SCROLL: 'scroll',
  
  // Event interactions
  EVENT_VIEW: 'event_view',
  EVENT_REGISTER: 'event_register',
  EVENT_SHARE: 'event_share',
  EVENT_CREATE: 'event_create',
  EVENT_SEARCH: 'event_search',
  
  // Authentication
  LOGIN: 'login',
  LOGOUT: 'logout',
  SIGNUP: 'sign_up',
  
  // Forms
  FORM_START: 'form_start',
  FORM_SUBMIT: 'form_submit',
  FORM_ERROR: 'form_error',
  
  // Errors
  ERROR_OCCURRED: 'error_occurred',
  
  // Performance
  TIMING_COMPLETE: 'timing_complete'
} as const;

export type EventCategory = typeof EVENT_CATEGORIES[keyof typeof EVENT_CATEGORIES];
export type EventAction = typeof EVENT_ACTIONS[keyof typeof EVENT_ACTIONS];
