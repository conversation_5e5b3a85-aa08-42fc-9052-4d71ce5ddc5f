
@import url('https://fonts.googleapis.com/css2?family=Raleway:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom utilities for safe area insets */
@layer utilities {
  .pb-safe {
    padding-bottom: env(safe-area-inset-bottom, 1rem);
  }
  .pt-safe {
    padding-top: env(safe-area-inset-top, 1rem);
  }

  /* Hide scrollbars for carousel */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Smooth scrolling for carousel */
  .scroll-smooth {
    scroll-behavior: smooth;
  }

  /* Touch scrolling for mobile */
  .touch-pan-x {
    touch-action: pan-x;
    -webkit-overflow-scrolling: touch;
  }

  /* Prevent accidental clicks during carousel navigation */
  .carousel-button {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .carousel-button:active {
    transform: scale(0.95);
  }
}

/* Custom scrollbar for locality selection */
.locality-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.locality-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.locality-scroll-container::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}

.locality-scroll-container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

.locality-scroll-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;

    --primary: 196 100% 45.5%;
    --primary-foreground: 0 0% 100%;

    --secondary: 197 100% 35.7%;
    --secondary-foreground: 0 0% 100%;

    --accent: 47 91% 61.4%;
    --accent-foreground: 0 0% 0%;

    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;

    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;

    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 196 100% 45.5%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 224 71.4% 4.1%;
    --sidebar-primary: 196 100% 45.5%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 14.3% 95.9%;
    --sidebar-accent-foreground: 224 71.4% 4.1%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 196 100% 45.5%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-roboto overflow-x-hidden;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-raleway font-semibold;
  }
}

/* Common animations and transitions */
@layer components {
  .transition-all-300 {
    @apply transition-all duration-300 ease-in-out;
  }

  .transition-transform-300 {
    @apply transition-transform duration-300 ease-in-out;
  }

  .hover-scale {
    @apply hover:scale-[1.02] transition-transform-300;
  }
}

/* Glass morphism components */
.glass {
  @apply bg-white/80 backdrop-blur-md;
}

.glass-card {
  @apply bg-white/70 backdrop-blur-md shadow-glass border border-white/20 rounded-2xl;
}

/* Animation delays */
.delay-100 {
  animation-delay: 100ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-300 {
  animation-delay: 300ms;
}

.delay-400 {
  animation-delay: 400ms;
}

.delay-500 {
  animation-delay: 500ms;
}
