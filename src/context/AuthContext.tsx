import React, { createContext, useState, useEffect, useContext, useRef } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { getStoredLocation, clearStoredLocation } from '@/utils/geocoding';
import { trackAuth, setUserId, setUserProperties, clearUserId, setAnonymousUser } from '@/utils/analytics';

// Get the current domain for redirects
// During development, it should use the current window location
// In production, it should use the deployed domain
const REDIRECT_DOMAIN = window.location.origin;

export interface UserProfile {
  id: string;
  username?: string;
  full_name?: string;
  avatar_url?: string;
  website?: string;
  is_admin?: boolean;
  location?: { lat: number; lng: number } | null; // Represent point as object
  location_updated_at?: string | null;
  locality_short_name?: string | null;
}

type AuthContextType = {
  user: User | null;
  session: Session | null;
  loading: boolean;
  userProfile: UserProfile | null;
  userProfileLoading: boolean;
  updateUserProfile: (updates: Partial<UserProfile>) => Promise<{ error: Error | null }>;
  signInWithGoogle: (redirectPath?: string) => Promise<void>;
  signOut: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [userProfileLoading, setUserProfileLoading] = useState(true);
  const { toast } = useToast();

  // Track if this is the initial page load to prevent showing the toast on tab focus
  const isInitialMount = useRef(true);
  // Track if we've shown the sign-in toast already
  const hasShownSignInToast = useRef(false);

  // Fetch user profile data and transfer location from local storage if needed
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!user) {
        setUserProfile(null);
        setUserProfileLoading(false);
        return;
      }

      try {
        setUserProfileLoading(true);

        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .maybeSingle();

        if (error) {
          console.error('Error fetching user profile:', error);
          throw error;
        }

        if (data) {
          setUserProfile(data as UserProfile);

          // Check if we need to transfer location from local storage to profile
          const storedLocation = getStoredLocation();
          const profileHasLocation = data.location &&
                                   data.locality_short_name &&
                                   data.location_updated_at;

          // If profile doesn't have location but local storage does, transfer it
          if (!profileHasLocation && storedLocation && storedLocation.coords) {
            console.log('Transferring location from local storage to user profile');
            await updateUserProfile({
              location: storedLocation.coords,
              locality_short_name: storedLocation.sublocality_level_1,
              location_updated_at: new Date().toISOString()
            });

            // No need to clear local storage - we'll keep it as a backup
            // and for consistency between signed-in and non-signed-in states
          }
        }
      } catch (error) {
        console.error('Failed to fetch user profile:', error);
      } finally {
        setUserProfileLoading(false);
      }
    };

    fetchUserProfile();
  }, [user]);

  // Update user profile
  const updateUserProfile = async (updates: Partial<UserProfile>) => {
    if (!user) {
      return { error: new Error('User not authenticated') };
    }

    // Prepare updates for Supabase, converting location if necessary
    const supabaseUpdates: { [key: string]: any } = { ...updates };

    if (updates.location &&
        typeof updates.location === 'object' &&
        'lat' in updates.location &&
        'lng' in updates.location) {
      const coords = updates.location as { lat: number; lng: number }; // Type assertion
      supabaseUpdates.location = `POINT(${coords.lng} ${coords.lat})`;
      console.log('Converted location object to PostGIS string:', supabaseUpdates.location);
    } else if (updates.location && typeof updates.location !== 'string') {
      // Handle potential invalid location format being passed
      console.error('Invalid location format provided for update:', updates.location);
      delete supabaseUpdates.location; // Don't send invalid format
    }

    // Remove null/undefined values if Supabase doesn't handle them well
    Object.keys(supabaseUpdates).forEach(key => {
      if (supabaseUpdates[key] === undefined || supabaseUpdates[key] === null) {
         // If you want to explicitly set to null in DB, keep it.
         // If you want to ignore the field if value is null/undefined, delete it:
         // delete supabaseUpdates[key];
      }
    });

    try {
      const { error } = await supabase
        .from('profiles')
        .update(supabaseUpdates) // Use the converted updates
        .eq('id', user.id);

      if (error) {
        throw error;
      }

      // Update local state if update was successful
      setUserProfile(prev => {
        if (!prev) return null;
        return { ...prev, ...updates };
      });

      return { error: null };
    } catch (error) {
      console.error('Error updating profile:', error);
      return { error: error as Error };
    }
  };

  useEffect(() => {
    // Set up auth state listener FIRST
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);

        // Only show authentication toasts for actual auth events (not on page reload or tab focus)
        if (event === 'SIGNED_IN' && session && !hasShownSignInToast.current) {
          toast({
            title: "Successfully signed in",
            description: `Welcome${session.user?.user_metadata?.full_name ? ` ${session.user.user_metadata.full_name}` : ''}!`,
          });
          hasShownSignInToast.current = true;

          // Track successful login
          trackAuth('login', 'google');

          // Set user ID for analytics (this ensures distinct user tracking)
          if (session.user?.id) {
            setUserId(session.user.id);
          }

          // Set comprehensive user properties for analytics
          setUserProperties({
            user_id: session.user?.id,
            user_name: session.user?.user_metadata?.full_name || 'Unknown',
            user_email: session.user?.email || 'Unknown',
            login_method: 'google',
            user_type: 'authenticated',
            first_login: !hasShownSignInToast.current ? 'yes' : 'no'
          });
        } else if (event === 'SIGNED_OUT') {
          toast({
            title: "Signed out",
            description: "You have been signed out successfully.",
          });
          // Reset the sign-in toast flag when user signs out
          hasShownSignInToast.current = false;

          // Track logout and clear user identification
          trackAuth('logout', 'google');
          clearUserId();

          // Set anonymous user tracking for continued analytics
          setAnonymousUser();
        }
      }
    );

    // THEN check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);

      // Mark initial mount as complete
      isInitialMount.current = false;

      // If we have a session on initial load, mark that we've shown the sign-in toast
      if (session) {
        hasShownSignInToast.current = true;
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [toast]);

  const signInWithGoogle = async (redirectPath?: string) => {
    try {
      // Use the provided redirectPath or default to home page
      const redirectUrl = redirectPath
        ? `${REDIRECT_DOMAIN}${redirectPath}`
        : `${REDIRECT_DOMAIN}/`;

      console.log('Signing in with Google using redirect URL:', redirectUrl);
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          }
        },
      });

      if (error) {
        console.error('Error during Google sign-in:', error);
        toast({
          title: "Sign in failed",
          description: error.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Exception during Google sign-in:', error);
      toast({
        title: "Sign in failed",
        description: "An unexpected error occurred during sign in.",
        variant: "destructive",
      });
    }
  };

  const signOut = async () => {
    try {
      await supabase.auth.signOut();

      // Clear user state
      setUser(null);
      setSession(null);
      setUserProfile(null);

      // Note: We intentionally don't clear local storage location here
      // This allows the user to maintain their location preference when signed out

      toast({
        title: "Signed out",
        description: "You have been successfully signed out.",
      });
    } catch (error) {
      console.error('Error signing out:', error);
      toast({
        title: "Sign out failed",
        description: "An unexpected error occurred during sign out.",
        variant: "destructive",
      });
    }
  };

  const value = {
    user,
    session,
    loading,
    userProfile,
    userProfileLoading,
    updateUserProfile,
    signInWithGoogle,
    signOut
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
