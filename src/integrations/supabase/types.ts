export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      event_categories: {
        Row: {
          active: boolean
          color: string
          created_at: string
          description: string | null
          icon: string | null
          id: string
          name: string
          priority: number
          text_color: string
          updated_at: string
        }
        Insert: {
          active?: boolean
          color: string
          created_at?: string
          description?: string | null
          icon?: string | null
          id?: string
          name: string
          priority?: number
          text_color: string
          updated_at?: string
        }
        Update: {
          active?: boolean
          color?: string
          created_at?: string
          description?: string | null
          icon?: string | null
          id?: string
          name?: string
          priority?: number
          text_color?: string
          updated_at?: string
        }
        Relationships: []
      }
      event_venues: {
        Row: {
          address: string
          city: string
          created_at: string
          id: string
          name: string
          priority: number
          state: string
          updated_at: string
          zip_code: string | null
        }
        Insert: {
          address: string
          city: string
          created_at?: string
          id?: string
          name: string
          priority?: number
          state: string
          updated_at?: string
          zip_code?: string | null
        }
        Update: {
          address?: string
          city?: string
          created_at?: string
          id?: string
          name?: string
          priority?: number
          state?: string
          updated_at?: string
          zip_code?: string | null
        }
        Relationships: []
      }
      events: {
        Row: {
          additional_images: string[] | null
          approval_notes: string | null
          approval_status: string
          category_id: string | null
          created_at: string
          description: string
          early_bird_deadline: string | null
          end_date: string | null
          general_admission_price: number | null
          group_discount: boolean | null
          has_early_bird: boolean | null
          hosted_by_type: string
          id: string
          image_url: string | null
          is_free: boolean | null
          is_multi_day: boolean | null
          max_attendees: number | null
          organization_id: string | null
          organizer_email: string | null
          organizer_id: string
          organizer_name: string
          organizer_phone: string | null
          parking_instructions: string | null
          payment_methods: string[] | null
          poster_image_filename: string | null
          refund_policy: string | null
          registration_deadline: string | null
          start_date: string
          tags: string[] | null
          title: string
          updated_at: string
          venue_id: string | null
          vip_ticket_price: number | null
          website_url: string | null
        }
        Insert: {
          additional_images?: string[] | null
          approval_notes?: string | null
          approval_status?: string
          category_id?: string | null
          created_at?: string
          description: string
          early_bird_deadline?: string | null
          end_date?: string | null
          general_admission_price?: number | null
          group_discount?: boolean | null
          has_early_bird?: boolean | null
          hosted_by_type?: string
          id?: string
          image_url?: string | null
          is_free?: boolean | null
          is_multi_day?: boolean | null
          max_attendees?: number | null
          organization_id?: string | null
          organizer_email?: string | null
          organizer_id: string
          organizer_name: string
          organizer_phone?: string | null
          parking_instructions?: string | null
          payment_methods?: string[] | null
          poster_image_filename?: string | null
          refund_policy?: string | null
          registration_deadline?: string | null
          start_date: string
          tags?: string[] | null
          title: string
          updated_at?: string
          venue_id: string | null
          vip_ticket_price?: number | null
          website_url?: string | null
        }
        Update: {
          additional_images?: string[] | null
          approval_notes?: string | null
          approval_status?: string
          category_id?: string | null
          created_at?: string
          description?: string
          early_bird_deadline?: string | null
          end_date?: string | null
          general_admission_price?: number | null
          group_discount?: boolean | null
          has_early_bird?: boolean | null
          hosted_by_type?: string
          id?: string
          image_url?: string | null
          is_free?: boolean | null
          is_multi_day?: boolean | null
          max_attendees?: number | null
          organization_id?: string | null
          organizer_email?: string | null
          organizer_id?: string
          organizer_name?: string
          organizer_phone?: string | null
          parking_instructions?: string | null
          payment_methods?: string[] | null
          poster_image_filename?: string | null
          refund_policy?: string | null
          registration_deadline?: string | null
          start_date?: string
          tags?: string[] | null
          title?: string
          updated_at?: string
          venue_id?: string | null
          vip_ticket_price?: number | null
          website_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "events_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "event_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "events_organizer_id_fkey"
            columns: ["organizer_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "events_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "events_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "event_venues"
            referencedColumns: ["id"]
          },
        ]
      }
      organization_members: {
        Row: {
          created_at: string
          id: string
          invited_at: string | null
          invited_by: string | null
          joined_at: string
          organization_id: string
          role: string
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          invited_at?: string | null
          invited_by?: string | null
          joined_at?: string
          organization_id: string
          role?: string
          status?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          invited_at?: string | null
          invited_by?: string | null
          joined_at?: string
          organization_id?: string
          role?: string
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "organization_members_invited_by_fkey"
            columns: ["invited_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "organization_members_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "organization_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      non_members: {
        Row: {
          created_at: string
          email: string
          id: string
          invited_at: string
          invited_by: string
          organization_id: string
          role: string
          status: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          email: string
          id?: string
          invited_at?: string
          invited_by: string
          organization_id: string
          role?: string
          status?: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          email?: string
          id?: string
          invited_at?: string
          invited_by?: string
          organization_id?: string
          role?: string
          status?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "non_members_invited_by_fkey"
            columns: ["invited_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "non_members_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      organizations: {
        Row: {
          address: string | null
          approval_notes: string | null
          approval_status: string
          city: string | null
          contact_email: string | null
          contact_phone: string | null
          created_at: string
          created_by: string
          description: string | null
          id: string
          locality: string | null
          logo_url: string | null
          name: string
          place_id: string | null
          state: string | null
          updated_at: string
          website_url: string | null
          zip_code: string | null
        }
        Insert: {
          address?: string | null
          approval_notes?: string | null
          approval_status?: string
          city?: string | null
          contact_email?: string | null
          contact_phone?: string | null
          created_at?: string
          created_by: string
          description?: string | null
          id?: string
          locality?: string | null
          logo_url?: string | null
          name: string
          place_id?: string | null
          state?: string | null
          updated_at?: string
          website_url?: string | null
          zip_code?: string | null
        }
        Update: {
          address?: string | null
          approval_notes?: string | null
          approval_status?: string
          city?: string | null
          contact_email?: string | null
          contact_phone?: string | null
          created_at?: string
          created_by?: string
          description?: string | null
          id?: string
          locality?: string | null
          logo_url?: string | null
          name?: string
          place_id?: string | null
          state?: string | null
          updated_at?: string
          website_url?: string | null
          zip_code?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "organizations_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          email: string | null
          full_name: string | null
          id: string
          is_admin: boolean
          phone_number: string | null
          updated_at: string
          username: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          email?: string | null
          full_name?: string | null
          id: string
          is_admin?: boolean
          phone_number?: string | null
          updated_at?: string
          username?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          email?: string | null
          full_name?: string | null
          id?: string
          is_admin?: boolean
          phone_number?: string | null
          updated_at?: string
          username?: string | null
        }
        Relationships: []
      }
      registrations: {
        Row: {
          event_id: string
          id: string
          payment_method: string | null
          payment_status: string | null
          quantity: number | null
          registration_date: string
          ticket_type: string | null
          total_amount: number | null
          unit_price: number | null
          user_id: string
        }
        Insert: {
          event_id: string
          id?: string
          payment_method?: string | null
          payment_status?: string | null
          quantity?: number | null
          registration_date?: string
          ticket_type?: string | null
          total_amount?: number | null
          unit_price?: number | null
          user_id: string
        }
        Update: {
          event_id?: string
          id?: string
          payment_method?: string | null
          payment_status?: string | null
          quantity?: number | null
          registration_date?: string
          ticket_type?: string | null
          total_amount?: number | null
          unit_price?: number | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "registrations_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "registrations_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
