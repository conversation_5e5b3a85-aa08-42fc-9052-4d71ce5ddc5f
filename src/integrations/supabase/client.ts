// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// Use environment variables with fallback to the hardcoded values for development
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "https://teetxyvxjvbuztjmrvyt.supabase.co";
const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRlZXR4eXZ4anZidXp0am1ydnl0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDI1Mjc3MjMsImV4cCI6MjA1ODEwMzcyM30.W3z8E17awhlZtsykwuof79ZQWCGK4bRdHFE09TdQbXg";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_ANON_KEY);