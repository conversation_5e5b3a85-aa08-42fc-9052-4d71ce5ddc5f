import { z } from "zod";

export const organizationFormSchema = z.object({
  name: z.string().min(2, "Organization name must be at least 2 characters"),
  description: z.string().min(10, "Description must be at least 10 characters").optional(),
  logoUrl: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
  websiteUrl: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
  contactEmail: z.string().email("Please enter a valid email").optional(),
  contactPhone: z.string().optional(),

  // Location fields - all optional now
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zipCode: z.string().optional(),
  locality: z.string().optional(),
  placeId: z.string().optional(),
});

export type OrganizationFormValues = z.infer<typeof organizationFormSchema>;

export interface Organization {
  id: string;
  name: string;
  description?: string;
  logo_url?: string;
  website_url?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  locality?: string;
  place_id?: string;
  approval_status: 'pending' | 'approved' | 'rejected';
  approval_notes?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface OrganizationMember {
  id: string;
  organization_id: string;
  user_id: string;
  role: 'owner' | 'admin' | 'event_manager' | 'member';
  status: 'pending_invitation' | 'active' | 'declined' | 'inactive'; // Added status
  invited_by?: string; // User ID of the inviter
  invited_at?: string; // Timestamp when invitation was sent/created
  joined_at?: string | null; // Timestamp when member accepted/joined, nullable
  created_at: string;
  updated_at: string;
  // For UI purposes, to include user details when fetching members
  user?: {
    id: string;
    full_name?: string | null;
    avatar_url?: string | null;
    email?: string | null;
  } | null;
}

export interface OrganizationWithMembers extends Organization {
  organization_members: OrganizationMember[];
}

export interface UserOrganization extends Organization {
  user_role: OrganizationRole; // Use the defined OrganizationRole type
  membership_id: string; // ID of the organization_members record
  membership_status: OrganizationMember['status']; // Status from organization_members record
}

export const organizationRoles = [
  { value: 'owner', label: 'Owner', description: 'Full access to organization and member management' },
  { value: 'member', label: 'Member', description: 'Basic member with view access. Can create events for approved organizations.' },
  // { value: 'admin', label: 'Admin', description: 'Can manage organization and add/remove members' }, // Removed
  // { value: 'event_manager', label: 'Event Manager', description: 'Can create and manage events for the organization' }, // Removed
] as const;

export type OrganizationRole = typeof organizationRoles[number]['value'];

// Non-member invitation interface for users who haven't signed up yet
export interface NonMember {
  id: string;
  organization_id: string;
  email: string;
  role: OrganizationRole;
  status: 'pending_invitation' | 'expired' | 'cancelled';
  invited_by: string; // User ID of the inviter
  invited_at: string; // Timestamp when invitation was sent
  created_at: string;
  updated_at: string;
  // For UI purposes, to include inviter details when fetching non-members
  inviter?: {
    id: string;
    full_name?: string | null;
    avatar_url?: string | null;
    email?: string | null;
  } | null;
}

// Extended organization interface that includes both members and non-members
export interface OrganizationWithAllMembers extends Organization {
  organization_members: OrganizationMember[];
  non_members: NonMember[];
}
