import { z } from "zod";

export const organizationFormSchema = z.object({
  name: z.string().min(2, "Organization name must be at least 2 characters"),
  description: z.string().min(10, "Description must be at least 10 characters").optional(),
  logoUrl: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
  websiteUrl: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
  contactEmail: z.string().email("Please enter a valid email").optional(),
  contactPhone: z.string().optional(),

  // Location fields - all optional now
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zipCode: z.string().optional(),
  locality: z.string().optional(),
  placeId: z.string().optional(),
});

export type OrganizationFormValues = z.infer<typeof organizationFormSchema>;

export interface Organization {
  id: string;
  name: string;
  description?: string;
  logo_url?: string;
  website_url?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  locality?: string;
  place_id?: string;
  approval_status: 'pending' | 'approved' | 'rejected';
  approval_notes?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface OrganizationMember {
  id: string;
  organization_id: string;
  user_id: string;
  role: 'owner' | 'admin' | 'event_manager' | 'member';
  invited_by?: string;
  invited_at?: string;
  joined_at: string;
  created_at: string;
  updated_at: string;
}

export interface OrganizationWithMembers extends Organization {
  organization_members: OrganizationMember[];
}

export interface UserOrganization extends Organization {
  user_role: 'owner' | 'admin' | 'event_manager' | 'member';
}

export const organizationRoles = [
  { value: 'owner', label: 'Owner', description: 'Full access to organization and member management' },
  { value: 'admin', label: 'Admin', description: 'Can manage organization and add/remove members' },
  { value: 'event_manager', label: 'Event Manager', description: 'Can create and manage events for the organization' },
  { value: 'member', label: 'Member', description: 'Basic member with view access' },
] as const;

export type OrganizationRole = typeof organizationRoles[number]['value'];
