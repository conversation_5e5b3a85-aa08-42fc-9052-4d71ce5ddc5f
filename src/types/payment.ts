// Payment system types for events and registrations

export interface PaymentMethod {
  upi?: UPIPaymentMethod;
  netBanking?: NetBankingPaymentMethod;
}

export interface UPIPaymentMethod {
  upiId: string;
  accountHolderName: string;
}

export interface NetBankingPaymentMethod {
  accountHolderName: string;
  bankName: string;
  accountNumber: string;
  ifscCode: string;
}

export interface PaymentProof {
  proofUrl?: string;
  transactionRef?: string;
  uploadedAt?: string;
}

export type RegistrationStatus = 'pending' | 'approved' | 'declined' | 'cancelled';
export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'refunded';

export interface EnhancedRegistration {
  id: string;
  event_id: string;
  user_id: string;
  registration_date: string;
  ticket_type: string;
  quantity: number;
  unit_price?: number;
  total_amount?: number;
  payment_status: PaymentStatus;
  payment_method?: string;
  registration_status: RegistrationStatus;
  payment_proof_url?: string;
  payment_transaction_ref?: string;
  payment_proof_uploaded_at?: string;
  reviewed_by?: string;
  reviewed_at?: string;
  review_notes?: string;
  approved_at?: string;
}

export interface RegistrationManagement extends EnhancedRegistration {
  event_title: string;
  event_is_free: boolean;
  event_organizer_id: string;
  event_organization_id?: string;
  user?: { // Changed from flat user_name, user_email, user_phone
    full_name: string | null;
    email: string | null;
    phone_number: string | null;
  } | null;
  reviewer?: { // Changed from flat reviewed_by_name
    full_name: string | null;
  } | null;
  // Preserving old fields as optional for a moment, to minimize immediate errors elsewhere,
  // but they should be removed after updating consuming components.
  user_name?: string;
  user_email?: string;
  user_phone?: string;
  reviewed_by_name?: string;
}

export interface EventPaymentConfig {
  upi_id?: string;
  upi_account_holder_name?: string;
  bank_account_holder_name?: string;
  bank_name?: string;
  bank_account_number?: string;
  bank_ifsc_code?: string;
  payment_reference_message?: string;
}

export interface PaymentInstructions {
  eventTitle: string;
  totalAmount: number;
  upi?: {
    upiId: string;
    accountHolderName: string;
    qrCodeUrl?: string;
  };
  netBanking?: {
    accountHolderName: string;
    bankName: string;
    accountNumber: string;
    ifscCode: string;
  };
  referenceMessage?: string;
}

export interface RegistrationReview {
  registrationId: string;
  status: RegistrationStatus;
  notes?: string;
}

// Form validation schemas
export interface PaymentMethodFormData {
  enableUPI: boolean;
  upiId: string;
  upiAccountHolderName: string;
  enableNetBanking: boolean;
  bankAccountHolderName: string;
  bankName: string;
  bankAccountNumber: string;
  bankIfscCode: string;
  paymentReferenceMessage: string;
}

export interface PaymentProofFormData {
  paymentProofFile?: File;
  transactionRef: string;
}

// API response types
export interface PaymentMethodValidation {
  isValid: boolean;
  errors: Record<string, string>;
}

export interface UPIValidationResult {
  isValid: boolean;
  accountHolderName?: string;
  error?: string;
}

// Constants
export const REGISTRATION_STATUSES: Record<RegistrationStatus, string> = {
  pending: 'Pending Review',
  approved: 'Approved',
  declined: 'Declined',
  cancelled: 'Cancelled'
};

export const PAYMENT_STATUSES: Record<PaymentStatus, string> = {
  pending: 'Payment Pending',
  completed: 'Payment Completed',
  failed: 'Payment Failed',
  refunded: 'Refunded'
};

export const REGISTRATION_STATUS_COLORS: Record<RegistrationStatus, string> = {
  pending: 'bg-yellow-100 text-yellow-800',
  approved: 'bg-green-100 text-green-800',
  declined: 'bg-red-100 text-red-800',
  cancelled: 'bg-gray-100 text-gray-800'
};

export const PAYMENT_STATUS_COLORS: Record<PaymentStatus, string> = {
  pending: 'bg-yellow-100 text-yellow-800',
  completed: 'bg-green-100 text-green-800',
  failed: 'bg-red-100 text-red-800',
  refunded: 'bg-blue-100 text-blue-800'
};
