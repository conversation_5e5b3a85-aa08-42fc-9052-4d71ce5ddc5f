
export interface EventVenue {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  zip_code: string | null;
  locality?: string | null; // sublocality_level_1 from Google Places API
  place_id?: string | null; // Google Places ID for precise location linking
  priority: number;
  created_at: string;
  updated_at: string;
  location?: {
    latitude: number;
    longitude: number;
  };
  distance_km?: number; // Distance from user's location in kilometers
}
