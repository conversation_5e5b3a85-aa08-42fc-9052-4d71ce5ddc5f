import { z } from "zod";

export const eventFormSchema = z.object({
  title: z.string().min(5, "Title should be at least 5 characters long"),
  description: z.string().min(20, "Description should be at least 20 characters long"),
  categoryId: z.string({
    required_error: "Please select a category for your event",
  }),
  tags: z.string().optional(),

  // Dates and Times
  startDate: z.date({
    required_error: "Please select the start date",
  }),
  startTime: z.string({
    required_error: "Please select a start time",
  }),
  isMultiDay: z.boolean().default(false),
  endDate: z.date().optional(),
  endTime: z.string().optional(),

  // Event Setup
  maxAttendees: z.string().optional(),
  registrationDeadline: z.date().optional(),

  // Location
  venueId: z.string({
    required_error: "Please select or create a venue",
  }),
  venueName: z.string().optional(),
  address: z.string().optional(),
  venueLocality: z.string().optional(),
  venuePlaceId: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zipCode: z.string().optional(),
  parkingInstructions: z.string().optional(),

  // Organizer
  hostedByType: z.enum(["individual", "organization"]).default("individual"),
  organizationId: z.string().optional(),
  organizerName: z.string({
    required_error: "Please enter organizer name",
  }),
  organizerEmail: z.string().email("Please enter a valid email").optional(),
  organizerPhone: z.string().optional(),
  websiteUrl: z.string().url("Please enter a valid URL").optional(),

  // Ticketing
  isFree: z.boolean().default(true),
  generalAdmission: z.string().optional(),
  vipTicket: z.string().optional(),
  hasEarlyBird: z.boolean().default(false),
  earlyBirdDeadline: z.date().optional(),
  earlyBirdDiscount: z.string().optional(),
  groupDiscount: z.boolean().default(false),
  minGroupSize: z.string().optional(),
  groupDiscountPercentage: z.string().optional(),
  paymentMethods: z.array(z.string()).default(["upi"]),
  refundPolicy: z.string().optional(),

  // Terms
  termsAgreed: z.boolean({
    required_error: "You must agree to the terms",
  }),
  privacyPolicyAgreed: z.boolean({
    required_error: "You must agree to the privacy policy",
  }),

  // Organizer Terms
  organizerTermsEnabled: z.boolean().default(true),
  organizerTerms: z.string().max(5000, "Terms and conditions cannot exceed 5000 characters.").optional(),
}).refine((data) => {
  // If venueId is provided (existing venue selected), venue details are not required
  if (data.venueId && data.venueId.trim() !== '') {
    return true;
  }

  // If no venueId, then we're creating a new venue and need venue details
  const hasVenueName = data.venueName && data.venueName.trim() !== '';
  const hasAddress = data.address && data.address.trim() !== '';
  const hasCity = data.city && data.city.trim() !== '';
  const hasState = data.state && data.state.trim() !== '';

  return hasVenueName && hasAddress && hasCity && hasState;
}, {
  message: "Please either select an existing venue or provide venue details (name, address, city, state)",
  path: ["venueId"], // This will show the error on the venueId field
}).refine((data) => {
  if (data.organizerTermsEnabled) {
    return data.organizerTerms && data.organizerTerms.trim() !== "";
  }
  return true;
}, {
  message: "Organizer terms must be provided and cannot be empty when enabled.",
  path: ["organizerTerms"],
});

export type EventFormValues = z.infer<typeof eventFormSchema>;
