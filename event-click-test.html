<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Click Test - TheLocalAdda</title>
    
    <!-- Google tag (gtag.js) with enhanced measurement disabled -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-K5ERWJ7N4Y"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-K5ERWJ7N4Y', {
        send_page_view: false,
        enhanced_measurement: {
          scrolls: false,
          outbound_clicks: false,
          site_search: false,
          video_engagement: false,
          file_downloads: false
        }
      });
    </script>
    
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 600px; 
            margin: 50px auto; 
            padding: 20px;
        }
        .event-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: #f9f9f9;
        }
        button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .log {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🎯 Event Click Tracking Test</h1>
    <p>Test if event clicks are being counted correctly (only once per click).</p>
    
    <div class="event-card">
        <h3>Sample Event: Community Cooking Class</h3>
        <p>Join us for a fun cooking experience!</p>
        <button onclick="trackEventView('event-123', 'Community Cooking Class')">👁️ View Event</button>
        <button onclick="trackEventRegister('event-123', 'Community Cooking Class')">✅ Register</button>
        <button onclick="trackEventShare('event-123', 'Community Cooking Class')">📤 Share</button>
    </div>
    
    <button onclick="clearLog()">🧹 Clear Log</button>
    
    <div class="log" id="log"></div>
    
    <h3>Instructions:</h3>
    <ol>
        <li>Open Google Analytics Real-time → Events</li>
        <li>Click the buttons above</li>
        <li>Each click should generate only 1 event (not 2)</li>
        <li>Watch the log below for duplicate detection</li>
    </ol>

    <script>
        // Event tracking with duplicate detection
        let eventCount = 0;
        const eventHistory = [];
        
        function log(message) {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            logEl.innerHTML += `<div>${logMessage}</div>`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(logMessage);
        }
        
        function trackEventView(eventId, eventTitle) {
            eventCount++;
            const eventData = {
                action: 'localadda_event_view',
                category: 'Event Interaction',
                label: eventTitle,
                eventId: eventId,
                timestamp: Date.now()
            };
            
            // Check for recent duplicates
            const recentEvents = eventHistory.filter(e => 
                e.action === eventData.action && 
                e.eventId === eventData.eventId &&
                (eventData.timestamp - e.timestamp) < 2000
            );
            
            if (recentEvents.length > 0) {
                log(`⚠️ Potential duplicate detected for event view: ${eventTitle}`);
            }
            
            eventHistory.push(eventData);
            
            if (typeof gtag === 'function') {
                gtag('event', 'localadda_event_view', {
                    event_category: 'Event Interaction',
                    event_label: eventTitle,
                    event_id: eventId,
                    custom_event_type: 'view',
                    source: 'localadda_custom_tracking'
                });
                log(`✅ Event view tracked: ${eventTitle}`);
            } else {
                log('❌ gtag not available');
            }
        }
        
        function trackEventRegister(eventId, eventTitle) {
            if (typeof gtag === 'function') {
                gtag('event', 'localadda_event_register', {
                    event_category: 'Event Interaction',
                    event_label: eventTitle,
                    event_id: eventId,
                    custom_event_type: 'register',
                    source: 'localadda_custom_tracking'
                });
                log(`✅ Event registration tracked: ${eventTitle}`);
            }
        }
        
        function trackEventShare(eventId, eventTitle) {
            if (typeof gtag === 'function') {
                gtag('event', 'localadda_event_share', {
                    event_category: 'Event Interaction',
                    event_label: eventTitle,
                    event_id: eventId,
                    custom_event_type: 'share',
                    source: 'localadda_custom_tracking'
                });
                log(`✅ Event share tracked: ${eventTitle}`);
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            eventHistory.length = 0;
        }
        
        // Monitor all gtag calls
        if (typeof window.gtag === 'function') {
            const originalGtag = window.gtag;
            
            window.gtag = function(...args) {
                if (args[0] === 'event' && args[1].includes('localadda_event')) {
                    log(`📊 GA Event: ${args[1]} (${args[2]?.event_label || 'no label'})`);
                }
                return originalGtag.apply(this, args);
            };
        }
        
        log('🚀 Event tracking test page loaded');
        log('Click the buttons above to test event tracking');
    </script>
</body>
</html>
