-- Fix Admin RLS Policies for Organizations
-- Run this in your Supabase SQL Editor

-- Drop existing admin policies
DROP POLICY IF EXISTS "Admins can view all organizations" ON "public"."organizations";
DROP POLICY IF EXISTS "Admins can update organizations" ON "public"."organizations";

-- Create new admin policies with correct syntax
CREATE POLICY "Ad<PERSON> can view all organizations" ON "public"."organizations" 
FOR SELECT 
USING (
  (SELECT is_admin FROM "public"."profiles" WHERE id = auth.uid()) = true
);

CREATE POLICY "Admins can update organizations" ON "public"."organizations" 
FOR UPDATE 
USING (
  (SELECT is_admin FROM "public"."profiles" WHERE id = auth.uid()) = true
);

-- Verify the policies were created
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'organizations' 
AND policyname LIKE '%Admin%';
