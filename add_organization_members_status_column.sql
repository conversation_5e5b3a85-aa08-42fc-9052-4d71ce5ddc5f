-- Add status column to organization_members table
-- This column tracks the membership state: pending_invitation, active, declined, inactive

-- Add the status column with default value 'active' for existing records
ALTER TABLE public.organization_members 
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active' NOT NULL;

-- Add a check constraint to ensure valid status values
ALTER TABLE public.organization_members 
ADD CONSTRAINT organization_members_status_check 
CHECK (status IN ('pending_invitation', 'active', 'declined', 'inactive'));

-- Update existing records to have 'active' status (since they were created without invitations)
UPDATE public.organization_members 
SET status = 'active' 
WHERE status IS NULL OR status = '';

-- Add comment for documentation
COMMENT ON COLUMN public.organization_members.status IS 'Membership status: pending_invitation, active, declined, inactive';

-- Update the RLS policy to allow users to update their own membership status (for accepting/declining invitations)
CREATE POLICY "Users can update their own membership status" ON "public"."organization_members" FOR
UPDATE
  TO "authenticated" USING (
    "user_id" = "auth"."uid"()
  ) WITH CHECK (
    "user_id" = "auth"."uid"()
  );

-- Update the trigger function to include status when creating organization owners
CREATE OR REPLACE FUNCTION "public"."handle_new_organization"()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Create owner membership for the organization creator with active status
  INSERT INTO public.organization_members (organization_id, user_id, role, status, joined_at)
  VALUES (NEW.id, NEW.created_by, 'owner', 'active', NOW());

  RETURN NEW;
END;
$$;

-- Grant necessary permissions
GRANT ALL ON TABLE "public"."organization_members" TO "authenticated";
GRANT ALL ON TABLE "public"."organization_members" TO "service_role";
