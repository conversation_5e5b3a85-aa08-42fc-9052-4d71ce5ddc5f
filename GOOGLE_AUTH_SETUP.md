# Google OAuth Authentication Setup for Playwright Tests

## Current Authentication Setup

Your app uses **Supabase Auth with Google OAuth**:

- **Supabase Project**: `https://teetxyvxjvbuztjmrvyt.supabase.co`
- **Authentication Provider**: Google OAuth via Supabase
- **Auth Flow**: Users click "Sign in with Google" → Google OAuth → Redirect back to app

## Credentials Your App Is Using

### Supabase Configuration
```typescript
// From src/integrations/supabase/client.ts
SUPABASE_URL: "https://teetxyvxjvbuztjmrvyt.supabase.co"
SUPABASE_ANON_KEY: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### Google OAuth Setup
Your Supabase project is configured with Google OAuth credentials that include:
1. **Google Client ID** (configured in Supabase dashboard)
2. **Google Client Secret** (configured in Supabase dashboard)
3. **Authorized redirect URIs** (pointing back to your Supabase project)

## Setting Up Google OAuth Testing

### Option 1: Create Test Google Account (Recommended)

1. **Create a dedicated test Google account**:
   ```
   Email: <EMAIL>
   Password: YourSecureTestPassword123!
   ```

2. **Set environment variables**:
   ```bash
   # Add to your .env file or set as environment variables
   TEST_GOOGLE_EMAIL=<EMAIL>
   TEST_GOOGLE_PASSWORD=YourSecureTestPassword123!
   ```

3. **Update your test**:
   ```typescript
   import { signInWithGoogle, setupAuthenticatedTest } from './helpers/google-auth';

   test('should access create page when authenticated', async ({ page }) => {
     await setupAuthenticatedTest(page);
     await page.goto('/create');
     
     // Now you're authenticated and can test the form
     await expect(page).toHaveURL(/\/create/);
   });
   ```

### Option 2: Use Existing Google Account

If you want to use your personal Google account for testing:

```bash
TEST_GOOGLE_EMAIL=<EMAIL>
TEST_GOOGLE_PASSWORD=your-password
```

**⚠️ Security Warning**: Only use this for local testing, never commit real credentials.

### Option 3: Mock Authentication (Simpler)

If you don't want to deal with real Google OAuth, you can mock the authentication:

```typescript
// Mock Supabase auth state
await page.addInitScript(() => {
  // Mock authenticated user
  window.localStorage.setItem('sb-teetxyvxjvbuztjmrvyt-auth-token', JSON.stringify({
    access_token: 'mock-token',
    user: { id: 'test-user', email: '<EMAIL>' }
  }));
});
```

## Implementation Steps

### Step 1: Choose Your Approach

**For Real Google OAuth Testing**:
```bash
# Set environment variables
export TEST_GOOGLE_EMAIL="<EMAIL>"
export TEST_GOOGLE_PASSWORD="your-test-password"
```

### Step 2: Update Your Test File

```typescript
import { test, expect } from '@playwright/test';
import { setupAuthenticatedTest, signOut } from './helpers/google-auth';

test.describe('Event Creation with Authentication', () => {
  test('should create event when authenticated', async ({ page }) => {
    // This will handle the Google OAuth flow
    await setupAuthenticatedTest(page);
    
    // Navigate to create page
    await page.goto('/create');
    
    // Now you can test the authenticated form
    await expect(page.locator('[data-value="item-1"]')).toBeVisible();
    
    // Test form validation
    await page.locator('button[type="submit"]').click();
    await expect(page.locator('text=Please fix validation errors')).toBeVisible();
  });

  test.afterEach(async ({ page }) => {
    // Clean up - sign out after each test
    await signOut(page);
  });
});
```

### Step 3: Run Tests

```bash
# With environment variables
TEST_GOOGLE_EMAIL="<EMAIL>" TEST_GOOGLE_PASSWORD="password" npm run test:event-creation

# Or if you set them in .env
npm run test:event-creation
```

## Troubleshooting

### Common Issues

1. **"Credentials not provided" error**:
   - Make sure `TEST_GOOGLE_EMAIL` and `TEST_GOOGLE_PASSWORD` are set
   - Check that environment variables are loaded correctly

2. **Google blocks sign-in**:
   - Google might block automated sign-ins
   - Try using a dedicated test account
   - Enable "Less secure app access" for the test account (if available)

3. **Two-factor authentication**:
   - Disable 2FA for the test account
   - Or use app-specific passwords

4. **Popup blocked**:
   - The helper handles both popup and redirect flows
   - If issues persist, check browser settings

### Alternative: Supabase Test Users

Instead of Google OAuth, you can create test users directly in Supabase:

1. Go to your Supabase dashboard
2. Navigate to Authentication → Users
3. Create a test user manually
4. Use Supabase's `signInWithPassword` instead of Google OAuth

## Security Best Practices

1. **Never commit real credentials** to version control
2. **Use dedicated test accounts** separate from personal accounts
3. **Rotate test credentials** regularly
4. **Use environment variables** for sensitive data
5. **Consider using CI/CD secrets** for automated testing

## Current Status

Your authentication setup is:
- ✅ **Supabase configured** with Google OAuth
- ✅ **Auth flow working** (redirects to `/auth` when not authenticated)
- ⚠️ **Test credentials needed** for automated testing
- ⚠️ **Google OAuth testing** requires setup

Choose the approach that works best for your testing needs!
