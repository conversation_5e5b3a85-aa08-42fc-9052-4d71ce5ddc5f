# TheLocalAdda - Community Events Platform

TheLocalAdda is a modern web application that helps users discover, join, and create local community events. It connects neighbors and builds stronger communities through shared activities and interests, with a particular focus on helping parents find like-minded families in Delhi (with plans to expand to Delhi NCR and beyond).

![TheLocalAdda Screenshot](https://images.unsplash.com/photo-1511632765486-a01980e01a18?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80)

## Features

### 🏠 **Homepage Experience**
- **Hero Section**: Search functionality with popular categories for quick discovery
- **Featured Events Carousel**: Horizontal scrolling showcase of upcoming events with navigation arrows
- **Venue Discovery**: Interactive venue carousel showing popular venues by active event count
- **Host Event CTA**: Clear call-to-action for event creation
- **Mobile-Optimized**: Responsive design with hamburger menu and touch-friendly navigation

### 🔍 **Event Discovery**
- **Advanced Search**: Browse and search for events happening in your neighborhood
- **Enhanced Filter Panel**: Collapsible left-side filter panel with subtle background and blue filter icon
- **Smart Filtering**: Filter by category, venue, date ranges (today, this week, weekend, etc.)
- **Applied Filter Pills**: Visual filter pills with individual remove buttons for easy filter management
- **Active Venues Only**: Venue filter shows only venues with active events, ordered by event count
- **Collapsible Sections**: Filter sections (Categories, Venues, Dates) collapsed by default for cleaner interface
- **Infinite Scrolling**: Seamless event loading with pagination
- **Venue-Based Discovery**: Click venue cards to see all events at that location
- **Geolocation**: Automatically detect user's location for hyper-local event discovery

### 🎫 **Event Management**
- **Event Creation**: Comprehensive form with validation for all event details
- **Organization Support**: Host events as individuals or on behalf of organizations
- **Event Registration**: Register for events with ticketing options (Indian Rupee ₹ support)
- **Attendee Management**: Search, filter, and export attendee lists for your events
- **Event Approval Flow**: Admin approval system before events go live
- **Shareable Event Posters**: Automatically generate beautiful event posters for sharing

### 🏢 **Organization Management**
- **Organization Registration**: Create organizations with detailed profiles and branding
- **Role-Based Access**: Owner, Admin, and Event Manager roles with different permissions
- **Organization Approval**: Admin approval workflow for new organizations
- **Event Hosting**: Choose between individual or organization hosting during event creation
- **Organization Branding**: Events inherit branding from selected organizations
- **Member Management**: Add/remove members with role-based permissions

### 👤 **User Experience**
- **Google Authentication**: Secure OAuth sign-in with seamless redirect handling
- **User Profiles**: Personalized profiles with location and preference management
- **My Events Page**: View registered events and events you're hosting
- **Mobile Navigation**: Clean hamburger menu with home icon and intuitive navigation

### 🏢 **Venue & Location**
- **Venue Management**: Comprehensive venue database with Google Places integration
- **Location-Based Features**: Distance calculation and neighborhood context
- **Venue Filtering**: Events page shows venues ordered by most active events
- **Address Autocomplete**: Google Places API integration for accurate location data

### 🎛️ **Enhanced Filter Panel**
- **Collapsible Design**: Left-side filter panel with blue toggle icon for space efficiency
- **Subtle Background**: Glass-like background with backdrop blur for modern aesthetic
- **Section Management**: Individual collapsible sections (Categories, Venues, Dates) with chevron indicators
- **Applied Filter Pills**: Color-coded filter pills with individual remove buttons for easy management
- **Smart Venue Filtering**: Shows only venues with active events, displaying event counts
- **Mobile Overlay**: Filter panel slides over content on mobile without disrupting layout
- **Responsive Behavior**: Fixed panel on desktop, overlay on mobile for optimal UX
- **No Scroll Interference**: Filter panel stays fixed while content scrolls independently

### 📊 **Analytics & Sharing**
- **Google Analytics Integration**: Comprehensive tracking of user interactions and events
- **Web Share Integration**: Share events using native device sharing capabilities
- **Event Analytics**: Track event views, registrations, and engagement

### 🛡️ **Admin & Security**
- **Admin Dashboard**: Manage users, venues, categories, approve events and organizations
- **Organization Approval**: Dedicated admin interface for approving/rejecting organizations
- **Row Level Security**: Supabase RLS policies for data protection
- **Event Moderation**: Approval workflow with admin notes and feedback
- **Organization Moderation**: Admin approval system for organization registration

## Quick Start Guide

1. **Clone & Install**
   ```sh
   git clone <YOUR_GIT_URL>
   cd <YOUR_PROJECT_NAME>
   npm i
   ```

2. **Set Up Supabase**
   - Create a Supabase account at [supabase.com](https://supabase.com/)
   - Create a new project and note your project URL and anon key
   - Import the database schema from `db_schema.sql` or use the SQL commands in the Database Schema section

3. **Environment Setup**
   - Create a `.env` file in the project root with:
   ```
   VITE_SUPABASE_URL=your_supabase_project_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
   VITE_GA_TRACKING_ID=your_google_analytics_tracking_id
   ```
   - You'll need to enable the Google Places API in your Google Cloud Console
   - Set up Google Analytics 4 and get your tracking ID (see [Google Analytics Setup Guide](./GOOGLE_ANALYTICS_SETUP.md))

4. **Run Development Server**
   ```sh
   npm run dev
   ```

5. **First-time Setup**
   - Sign in with Google OAuth
   - To make yourself an admin, run this SQL in Supabase:
   ```sql
   UPDATE public.profiles
   SET is_admin = true
   WHERE id = '<your_user_id>';
   ```

## Tech Stack

This project is built with a modern tech stack:

- **Frontend**:
  - **React**: UI library
  - **TypeScript**: Type-safe JavaScript
  - **React Router**: Client-side routing
  - **Tailwind CSS**: Utility-first CSS framework
  - **shadcn/ui**: High-quality UI components
  - **Framer Motion**: Animation library
  - **Lucide Icons**: Beautiful SVG icons
  - **date-fns**: Date manipulation library
  - **React Intersection Observer**: For implementing infinite scrolling

- **Backend**:
  - **Supabase**: Backend-as-a-Service for authentication, database, and storage
  - **PostgreSQL**: Relational database (managed by Supabase)

## Key Workflows

### Authentication Flow

The platform features a seamless authentication experience:
1. Users sign in with Google OAuth
2. When a non-authenticated user attempts to access protected features (like event registration), they are redirected to the auth page
3. After successful authentication, users are redirected back to their original destination using the `redirect` URL parameter
4. UI clearly indicates when authentication is required with explicit button text (e.g., "Sign in to Register")

### Event Creation & Approval Flow

1. User creates a new event through the create event form
2. The event is saved with `approval_status = 'pending'`
3. Admins can see all pending events in the admin dashboard
4. Admins can approve or reject events, providing optional notes
5. Only approved events are visible to general users
6. Event creators can see their own events regardless of approval status

### Event Registration Process

1. User views event details on the EventPage
2. Clicks "Register Now" (or "Sign in to Register" if not authenticated)
3. Completes the registration form with ticket type and quantity
4. Pricing is displayed in Indian Rupee (₹) format
5. Registration is stored in the database with payment status
6. User can view their registrations in the My Events page

## Supabase Integration

### Database Schema

The Supabase PostgreSQL database has the following primary tables:

1. **profiles**:
   - Stores user profile information
   - Created automatically when a user signs up via the `handle_new_user()` trigger function
   - Fields: `id`, `username`, `full_name`, `avatar_url`, `email`, `phone_number`, `created_at`, `updated_at`, `is_admin`, `locality_short_name`
   - The `is_admin` flag identifies users with administrative privileges
   - Location field (PostGIS Point) store the user's geographic coordinates
   - Locality fields store the user's neighborhood or district (sublocality_level_1 from Google Geocoding API)
   - Linked to Supabase Auth users via foreign key to `auth.users(id)`

2. **events**:
   - Stores event details
   - Fields include:
     - Basic info: `id`, `title`, `description`, `tags`, `image_url`, `additional_images`
     - Dates: `start_date`, `end_date`, `is_multi_day`, `registration_deadline`, `created_at`, `updated_at`
     - Capacity: `max_attendees` (limits the number of registrations for an event)
     - Organizer info: `organizer_id`, `organizer_name`, `organizer_email`, `organizer_phone`, `website_url`
     - Pricing: `is_free`, `general_admission_price`, `vip_ticket_price`, `has_early_bird`, `early_bird_deadline`, `group_discount`, `payment_methods`, `refund_policy`
     - Approval workflow: `approval_status` ('pending', 'approved', or 'rejected'), `approval_notes`
     - Location: `venue_id` (foreign key to event_venues)
     - Sharing: `poster_image_filename` (filename of the generated poster image for sharing)
     - Categorization: `category_id` (foreign key to event_categories)
     - Hosting: `hosted_by_type` (TEXT, NOT NULL, DEFAULT 'individual' - Indicates if the event is hosted by an 'individual' or an 'organization'), `organization_id` (UUID, foreign key to organizations - Optional, links to the hosting organization if `hosted_by_type` is 'organization')
     - Additional info: `parking_instructions`

3. **event_categories**:
   - Stores categories for events
   - Fields: `id`, `name`, `description`, `color`, `text_color`, `icon`, `priority`, `active`, `created_at`, `updated_at`
   - Used for filtering and organizing events
   - Has unique constraint on `name`

4. **event_venues**:
   - Stores venue information for events
   - Fields: `id`, `name`, `address`, `city`, `state`, `zip_code`, `locality`, `place_id`, `location`, `priority`, `created_at`, `updated_at`
   - `locality` stores the neighborhood or district (sublocality_level_1 from Google Places API)
   - `place_id` stores the Google Places ID for precise location linking
   - `location` stores the geographic coordinates as a PostGIS Point
   - Allows for reusing common venues across multiple events
   - Has unique constraints on `name` and combination of `name`, `address`, `city`

5. **registrations**:
   - Tracks event registrations by users
   - Fields: `id`, `event_id`, `user_id`, `registration_date`, `ticket_type`, `quantity`, `unit_price`, `total_amount`, `payment_status`, `payment_method`
   - Links to the profiles table for attendee information
   - Has unique constraint on combination of `event_id` and `user_id` (one registration per user per event)
   - Includes an index on `user_id` for efficient querying of user registrations

6. **organizations**:
   - Stores organization information for event hosting
   - Fields: `id`, `name`, `description`, `website`, `email`, `phone`, `address`, `city`, `state`, `zip_code`, `country`, `created_by`, `created_at`, `updated_at`, `approval_status`, `approval_notes`, `approved_by`, `approved_at`
   - `created_by` links to the user who registered the organization
   - `approval_status` tracks pending/approved/rejected status
   - `approved_by` links to the admin who approved the organization
   - Address fields support Google Places integration for accurate location data

7. **organization_members**:
   - Manages organization membership and roles
   - Fields: `id`, `organization_id`, `user_id`, `role`, `joined_at`, `invited_by`, `status`
   - `role` defines permissions: 'owner', 'admin', 'event_manager', 'member'
   - `status` tracks active/inactive/pending membership states
   - Has unique constraint on combination of `organization_id` and `user_id`
   - Links organizations to users with role-based access control

### Database Relationships

The database tables are connected through the following relationships:

1. **events → profiles**:
   - Foreign key `organizer_id` references `profiles(id)`
   - Identifies the user who created the event

2. **events → event_categories**:
   - Foreign key `category_id` references `event_categories(id)` with ON DELETE SET NULL
   - Allows events to be categorized for filtering and organization

3. **events → event_venues**:
   - Foreign key `venue_id` references `event_venues(id)` with ON DELETE RESTRICT
   - Associates events with specific venues

4. **profiles → auth.users**:
   - Foreign key `id` references `auth.users(id)` with ON DELETE CASCADE
   - Links profile information to Supabase authentication

5. **registrations → events**:
   - Foreign key `event_id` references `events(id)` with ON DELETE CASCADE
   - Ensures registrations are removed if an event is deleted

6. **registrations → profiles**:
   - Foreign key `user_id` references `profiles(id)` with ON DELETE CASCADE
   - Links registrations to user profiles

7. **organizations → profiles**:
   - Foreign key `created_by` references `profiles(id)` with ON DELETE CASCADE
   - Links organizations to their creators
   - Foreign key `approved_by` references `profiles(id)` with ON DELETE SET NULL
   - Links organizations to the admin who approved them

8. **organization_members → organizations**:
   - Foreign key `organization_id` references `organizations(id)` with ON DELETE CASCADE
   - Ensures members are removed if organization is deleted

9. **organization_members → profiles**:
   - Foreign key `user_id` references `profiles(id)` with ON DELETE CASCADE
   - Links organization memberships to user profiles
   - Foreign key `invited_by` references `profiles(id)` with ON DELETE SET NULL
   - Tracks who invited each member

10. **events → organizations**:
    - Foreign key `organization_id` references `organizations(id)` with ON DELETE SET NULL
    - Links events to hosting organizations (optional, events can be hosted individually)

### Row Level Security (RLS) Policies

The database tables are secured using Supabase Row Level Security policies, ensuring users can only access and modify their own data:

1. **Events Table Policies**:
   - Users can view all approved events: `Users can view only approved events`
   - Users can view their own events (even if not approved): `Users can view only approved events`
   - Users can only create events with their own user ID: `Allow authenticated users to create events`
   - Users can only modify/delete their own events: `Allow event creators to modify their events`, `Allow event creators to delete their events`
   - Admins can view and update all events: `Admins can view all events`, `Admins can update events`

2. **Profiles Table Policies**:
   - Anyone can view profiles: `Allow anyone to read profiles`
   - Users can edit only their own profile information: `Allow users to update their own profiles`

3. **Registrations Table Policies**:
   - Users can view and manage only their own event registrations: `Allow users to read their own registrations`, `Allow users to update their own registrations`, `Allow users to delete their own registrations`
   - Event hosts can view registrations for events they created: `Allow event organizers to view registrations`
   - Users can only create registrations with their own user ID: `Allow users to create their own registrations`

4. **Event Categories Policies**:
   - Anyone can view categories: `Categories are visible to everyone`
   - Only admins can create, update, or delete categories: `Only admins can insert categories`, `Only admins can update categories`, `Only admins can delete categories`

5. **Event Venues Policies**:
   - Anyone can view venues: `Venues are visible to everyone`
   - Anyone can create venues: `Anyone can create venues`
   - Only admins can update or delete venues: `Only admins can update venues`, `Only admins can delete venues`

6. **Organizations Policies**:
   - Users can view approved organizations: `Users can view approved organizations`
   - Users can view their own organizations: `Users can view their own organizations`
   - Users can create organizations: `Users can create organizations`
   - Users can update their own organizations: `Users can update their own organizations`
   - Admins can view all organizations: `Admins can view all organizations`
   - Admins can update organization approval status: `Admins can update organizations`

7. **Organization Members Policies**:
   - Users can view members of organizations they belong to: `Users can view organization members`
   - Organization owners/admins can manage members: `Organization owners can manage members`
   - Users can view their own memberships: `Users can view their own memberships`
   - Users can create memberships for organizations they own/admin: `Users can create organization memberships`
   - Users can update their own pending invitation status (e.g., accept/decline): `Allow users to update own pending invitation status`

### Authentication Setup

The project uses Supabase Auth with Google OAuth. To configure Google authentication:

1. In your Supabase project dashboard, go to Authentication → Providers → Google
2. Enable Google auth and add your Google OAuth credentials (Client ID and Secret)
3. Set up the authorized redirect URLs in Google Cloud Console
4. Configure Site URL and Redirect URLs in Supabase Auth Settings

When a new user signs up, the `handle_new_user()` trigger function automatically creates a profile record in the `profiles` table, extracting information from the OAuth provider when available.

## Project Structure

The project follows a modular architecture:

- **`/src/components`**: Reusable React components
  - **`/ui`**: shadcn UI components and custom UI components like SearchBar, CategoryBadge
  - **`/layout`**: Layout components (Header with mobile navigation, Footer with About link)
  - **`/home`**: Homepage components (Hero, FeaturedEvents, FeaturedVenues, EventCard, VenueCard)
  - **`/events`**: Event components (EventDetail, EventCard, filtering components, OrganizerSection)
  - **`/auth`**: Authentication-related components with redirect handling
  - **`/admin`**: Admin dashboard components (CategoryManager, VenueManager, event/organization approval)
  - **`/venues`**: Venue-related components (VenueSelector with Google Places integration)
  - **`/profile`**: User profile management components
  - **`/location`**: Location-based components (LocationGate, location detection)
  - **`/analytics`**: Google Analytics integration components
  - **`/organizations`**: Organization components (registration, management, member handling)

- **`/src/context`**: React context providers
  - `AuthContext.tsx`: Manages authentication state

- **`/src/pages`**: Page components for each route
  - `Index.tsx`: Home page
  - `EventsPage.tsx`: Browse all events with filtering and pagination
  - `EventPage.tsx`: View individual event details
  - `CreateEventPage.tsx`: Create new events
  - `AuthPage.tsx`: Authentication page
  - `AboutPage.tsx`: About the platform
  - `AdminPage.tsx`: Admin dashboard for event and organization approval
  - `ProfilePage.tsx`: User profile management
  - `MyEventsPage.tsx`: View registered and hosted events
  - `OrganizationsPage.tsx`: Organization registration and management
  - `OrganizationManagementPage.tsx`: Individual organization management

- **`/src/hooks`**: Custom React hooks
  - `useEvents.ts`: Event data manipulation with pagination and filtering
  - `useCategories.ts`: Category data management with active category filtering
  - `useVenues.ts`: Venue data manipulation with location-based sorting
  - `useVenuesWithEvents.ts`: Fetch venues ordered by active event count
  - `useUserLocation.ts`: User location detection and management
  - `useUserCoordinates.ts`: Geographic coordinates access and updates
  - `useDebounce.ts`: Input debouncing for search functionality
  - `use-toast.ts`: Toast notification system
  - `use-mobile.tsx`: Responsive design and mobile detection
  - `useUserRegistrations.ts`: User event registration management
  - `useUserHostedEvents.ts`: Events hosted by user with statistics
  - `useEventRegistrations.ts`: Event attendee management with search and export
  - `useAnalytics.ts`: Google Analytics event tracking integration

- **`/src/integrations`**: External service integrations
  - **`/supabase`**: Supabase client and type definitions

- **`/src/types`**: TypeScript type definitions
  - `category.ts`: Types for event categories
  - `venue.ts`: Types for event venues
  - `event-form.ts`: Types for the event creation form

- **`/src/utils`**: Utility functions
  - `cacheUtils.ts`: Data caching utilities for improved performance
  - `googlePlacesLoader.ts`: Utilities for interacting with Google Places API
  - `addressFormatter.ts`: Functions for formatting address components from Google Places API
  - `posterGenerator.ts`: Functions for generating event posters using the Supabase Edge Function
  - `shareUtils.ts`: Utilities for sharing events using the Web Share API
  - `tailwindColors.ts`: Mapping of Tailwind CSS color classes to hex values for poster generation

- **`/src/config`**: Configuration files
  - `maps.ts`: Configuration for Google Maps and Places API, including default search radius

## Key Routes

- `/`: Home page with hero section, featured events carousel, and venue discovery
- `/events`: Browse all events with advanced filtering, search, and infinite scrolling
- `/events/:id`: Event details page with registration and sharing capabilities
- `/create`: Create new event (protected, requires authentication)
- `/auth`: Authentication page with redirect handling
- `/about`: About page (accessible from footer)
- `/admin`: Admin dashboard (protected, requires admin privileges)
- `/profile`: User profile management with location settings
- `/my-events`: User's registered and hosted events with attendee management
- `/organizations`: Organization registration and management hub
- `/organizations/:id/manage`: Individual organization management page

## UI/UX Highlights

### 🎨 **Modern Design System**
- **Consistent Styling**: shadcn/ui components with Tailwind CSS
- **Smooth Animations**: Framer Motion for page transitions and interactions
- **Responsive Layout**: Mobile-first design with breakpoint optimization
- **Color Consistency**: Category-based color schemes throughout the platform

### 📱 **Mobile Experience**
- **Hamburger Navigation**: Clean mobile menu with home icon
- **Touch-Friendly**: Optimized button sizes and touch targets
- **Carousel Navigation**: Horizontal scrolling with arrow controls
- **Drawer Components**: Mobile-optimized filtering with drawer UI

### 🎯 **User Interface Features**
- **Search Integration**: Real-time search with debouncing
- **Filter Pills**: Visual category and venue filtering
- **Infinite Scrolling**: Seamless content loading on events page
- **Loading States**: Skeleton components for better perceived performance
- **Toast Notifications**: User feedback for actions and errors

### 🏠 **Homepage Layout**
- **Hero Section**: Search bar with popular categories
- **Event Carousel**: 3-box layout with navigation arrows (hidden at extremes)
- **Venue Carousel**: Popular venues ordered by active events
- **Call-to-Action**: Prominent event creation encouragement

## Admin Dashboard

The admin dashboard is accessible only to users with the `is_admin` flag set to `true` in their profile. The dashboard provides features for:

1. **Event Management**:
   - View all events in the system (pending, approved, rejected)
   - Approve or reject events with feedback
   - View detailed event information

2. **Organization Management**:
   - View all organizations in the system (pending, approved, rejected)
   - Approve or reject organization registrations with feedback
   - Access organization management pages for approved organizations
   - View organization details and member information

3. **User Management**:
   - View all user profiles in the system
   - See user details and admin status

4. **Category Management**:
   - Create, edit, and delete event categories
   - Set category colors, priorities, and active status

5. **Venue Management**:
   - Create, edit, and delete venue locations
   - Manage venue details like address, city, state

To make a user an admin, you need to set the `is_admin` flag to `true` in the `profiles` table for that user:

```sql
UPDATE public.profiles
SET is_admin = true
WHERE id = '<user_id>';
```

## Code Conventions

### Component Organization

- Components are organized by feature area (`events`, `admin`, etc.)
- UI components use the shadcn/ui pattern with Tailwind CSS
- Each page has its own component in the `/pages` directory
- Form sections are split into logical components (BasicInfoSection, TicketingSection, etc.)

### State Management

- Authentication state is managed via AuthContext
- Data fetching is handled through custom hooks (useEvents, useCategories, etc.)
- Form state uses react-hook-form with zod validation
- Toast notifications for user feedback

### Styling Approach

- Tailwind CSS for utility-first styling
- shadcn/ui components as the foundation
- Custom classes for specialized UI elements
- Consistent use of Indian Rupee (₹) symbol for all currency displays
- Framer Motion for animations and transitions

## Common Development Tasks

### Adding a New Event Category

1. Log in as an admin user
2. Navigate to the Admin Dashboard
3. Go to the Categories tab
4. Use the form to add a new category with name, color, and icon

### Creating a New Component

1. Create a new file in the appropriate directory under `/src/components`
2. Import necessary UI components from shadcn/ui
3. Follow the existing patterns for props, state management, and styling
4. For forms, use react-hook-form with zod validation

### Implementing a New Feature

1. Create necessary database tables/columns in Supabase
2. Add TypeScript types in `/src/types` or `/src/integrations/supabase/types.ts`
3. Create custom hooks in `/src/hooks` for data operations
4. Build UI components in `/src/components`
5. Add routing in the appropriate page component

## Component Highlights

### SearchBar
A reusable search component that provides real-time filtering capabilities with optional debouncing for improved performance.

### CategoryFilter
Allows users to filter content by categories with a clean dropdown interface that shows all available categories.

### VenueSelector
A sophisticated venue selection component that enables users to search for existing venues or create new ones when organizing events.

### PlacesAutocomplete
A reusable component that integrates with Google Places API to provide location search with autocomplete suggestions, restricted to a 2km radius around the user's current location for better relevance.

### Event Creation Process
The event creation flow uses multiple specialized components:
- BasicInfoSection: For title, description, and category
- LocationSection: For venue selection and address information, with Google Places integration for accurate location data
- EventDetailsSection: For dates, times, and additional details
- TicketingSection: For pricing and registration options (with Indian Rupee ₹ support)
- OrganizerSection: For organizer contact information
- TermsSection: For terms and conditions

### User Profile Management
The profile page allows users to update their personal information:
- Update name, email, and phone number
- View account creation date
- Manage profile preferences

### My Events Page
A comprehensive page for users to track their event activity:
- My Registrations: View events the user has registered for
- My Hosted Events: View and manage events created by the user
  - See registration statistics (attendees, tickets, revenue)
  - View capacity limits and percentage filled
  - Manage attendee lists with search functionality
  - Export attendee data to CSV

### EventAttendeesSection
A component for event hosts to manage event attendees:
- Search attendees by name, email, or phone number
- View payment status and ticket information
- Export attendee list to CSV for offline management
- Infinite scrolling for efficient loading of large attendee lists
- Capacity tracking showing tickets sold vs. maximum capacity

## Geolocation Features

TheLocalAdda uses geolocation to provide a hyper-local experience:

1. **User Location Detection**:
   - Automatically detects the user's location with browser geolocation API
   - Stores location coordinates and locality information in the user's profile
   - Provides fallback to default location (C.R. Park, Delhi) if geolocation is unavailable
   - Allows users to manually select their locality through pincode search

2. **Venue Location Management**:
   - Uses Google Places (New) API for venue search and selection
   - Stores precise geographic coordinates for each venue
   - Captures detailed address components including locality/neighborhood
   - Stores Google Places ID for creating direct links to Google Maps
   - Restricts venue search to a 2km radius around the user's location for better relevance

3. **Location-Based Features**:
   - Shows events near the user's current location
   - Displays distance information for venues
   - Provides neighborhood context for better local discovery
   - Enables future features like map-based event browsing and proximity alerts

## Event Approval Flow

1. User creates a new event through the create event form
2. The event is saved with `approval_status = 'pending'`
3. Admins can see all pending events in the admin dashboard
4. Admins can approve or reject events, providing optional notes
5. When an event is approved, a shareable poster image is automatically generated
6. Only approved events are visible to general users
7. Event creators can see their own events regardless of approval status

## Organization Management System

TheLocalAdda includes a comprehensive organization management system that allows users to create and manage organizations for hosting events:

### Organization Registration
1. **Organization Creation**: Users can register new organizations with detailed profiles
2. **Organization Information**: Name, description, website, contact details, and address
3. **Google Places Integration**: Address autocomplete for accurate location data
4. **Admin Approval**: Organizations require admin approval before becoming active

### Role-Based Access Control
The system supports the following roles with distinct permissions. When inviting new members, the available roles for assignment are currently limited to 'Owner' and 'Member' to simplify initial setup.
- **Owner**: Full control over organization settings and member management
- **Admin**: Can manage members but cannot delete the organization. (Note: Cannot select the organization from the 'Host an Event' dropdown to create new events).
- **Event Manager**: Can manage events created by others within the organization. (Note: Cannot select the organization from the 'Host an Event' dropdown to create new events).
- **Member**: Basic access to organization information. Can select the organization from the 'Host an Event' dropdown to create new events for approved organizations (if they are an 'owner' or 'member' of that organization).

### Event Creation Integration
- **Hosting Options**: Users can choose to host events as individuals or on behalf of organizations
- **Organization Selection**: Dropdown shows all organizations where user has event creation permissions
- **Add New Organization**: Direct link from event creation to organization registration
- **Seamless Flow**: After creating an organization, users return to event creation with the new organization pre-selected

### Organization Approval Workflow
1. User registers a new organization
2. Organization is saved with `approval_status = 'pending'`
3. Admins can view all pending organizations in the dedicated "Organizations" tab
4. Admins can approve or reject organizations with optional feedback notes
5. Only approved organizations can be used for event hosting
6. Organization creators can manage their organizations regardless of approval status

### Admin Organization Management
- **Dedicated Interface**: Separate "Organizations" tab in admin dashboard
- **Approval Actions**: Approve/reject buttons with confirmation dialogs
- **Organization Details**: View complete organization information and member lists
- **Management Access**: Admins can access organization management pages for approved organizations
- **Status Tracking**: Clear indicators for pending, approved, and rejected organizations

## Event Poster Generation and Sharing

TheLocalAdda automatically generates beautiful event posters for sharing on social media:

1. **Poster Generation**:
   - When an admin approves an event, a poster is automatically generated
   - The poster includes the event image, title, date, time, location, and category
   - Posters are styled with the event's category colors for consistent branding
   - Generated posters are stored in the Supabase Storage 'event-posters' bucket

2. **Sharing Functionality**:
   - Users can share events directly from the event details page
   - On supported devices, the Web Share API is used for native sharing
   - The share includes the event title, description, URL, and poster image (when available)
   - On devices without Web Share API support, users can copy the event link

3. **Technical Implementation**:
   - Posters are generated using a Supabase Edge Function with image processing capabilities
   - Tailwind CSS color classes are converted to hex and ARGB values for consistent styling
   - The poster filename is stored in the events table for easy retrieval
   - The Web Share API is used with fallbacks for cross-browser compatibility

## Authentication Flow

The platform features a seamless authentication experience:
1. Users sign in with Google OAuth
2. When a non-authenticated user attempts to access protected features (like event registration), they are redirected to the auth page
3. After successful authentication, users are redirected back to their original destination
4. Clear UI indicators show when authentication is required for certain actions

## Automation Testing

TheLocalAdda includes comprehensive end-to-end automation testing using Playwright to ensure robust functionality and user experience.

### Test Suite Overview

The automation testing suite covers:

#### 🔐 **Authentication & User Flow**
- **Google OAuth Integration**: Complete OAuth consent flow testing
- **Redirect Handling**: Authentication with proper redirect back to original page
- **Location Setup**: PIN code entry and location selection (Chittranjan Park)
- **Session Management**: Persistent authentication across test scenarios

#### 🏠 **Homepage Testing**
- **Component Presence**: Verification of all homepage elements
- **Hero Section**: Search functionality and popular categories
- **Event Carousel**: Navigation arrows and event display validation
- **Venue Carousel**: Popular venue display and interaction testing
- **Mobile Navigation**: Hamburger menu and responsive design testing

#### 🔍 **Search & Discovery**
- **Event Search**: Multi-keyword search with database validation
- **Category Filtering**: Popular categories and filter application
- **Venue Filtering**: Venue-based event discovery
- **Search Results**: Zero results handling and result validation
- **Filter Combinations**: Multiple filter application testing

#### 📝 **Event Creation & Management**
- **Form Validation**: Comprehensive form field testing
- **Section Expansion**: Step-by-step form completion
- **Mandatory Fields**: Required field validation
- **Date Selection**: Event date setting (4 days from current)
- **Venue Selection**: Location and venue dropdown testing
- **Terms & Conditions**: Agreement checkbox validation

#### 📊 **Data Validation**
- **Database Consistency**: UI data matches backend content
- **Popular Categories**: Dynamic category list validation
- **Event Counts**: Accurate event counting and display
- **Venue Popularity**: Venue ordering by active events

### Test Configuration
- **Centralized Test Data**: `tests/config/search-terms.ts` for managing test search terms
- **Reusable Test Steps**: Common authentication and location setup flows
- **Environment Setup**: Automated PIN code entry and location selection
- **Session Reuse**: Efficient test execution with shared authentication state

## Recent Improvements & Current Status

### ✅ **Recently Completed**
- **Enhanced Filter Panel**: Complete redesign with collapsible left-side panel
  - Blue filter toggle icon for better visibility
  - Subtle glass-like background with backdrop blur
  - Individual collapsible sections (Categories, Venues, Dates)
  - Applied filter pills with individual remove buttons
  - Mobile overlay that doesn't disrupt content layout
  - Fixed panel positioning that doesn't scroll with content
- **Smart Venue Filtering**: Shows only venues with active events, ordered by event count
- **Layout Optimization**: Perfect alignment between filter panel and main content
- **Mobile Navigation**: Fixed hamburger menu with home icon
- **Venue Discovery**: Clickable venue cards with proper navigation
- **Homepage Layout**: Improved carousel design with navigation arrows
- **Footer Enhancement**: Moved About link from header to footer
- **Test Coverage**: Comprehensive Playwright test suite implementation
- **UI Polish**: Consistent styling and responsive design improvements

### 🎯 **Current Features**
- **Enhanced Filter Panel**: Collapsible left-side panel with glass-like design and smart filtering
- **Applied Filter Management**: Visual filter pills with individual remove buttons
- **Smart Venue Discovery**: Active venues only with event count display
- **Stable Navigation**: All links and routes working properly
- **Event Discovery**: Advanced search and filtering capabilities with perfect layout alignment
- **User Authentication**: Seamless Google OAuth with redirect handling
- **Admin Dashboard**: Complete event approval and management system
- **Mobile Optimization**: Touch-friendly interface with overlay panels and responsive design

### 🔧 **Technical Highlights**
- **Advanced Filter Panel**:
  - Flexbox layout with sticky positioning for desktop
  - CSS transitions and backdrop-blur for modern glass effect
  - State management for collapsible sections and applied filters
  - Responsive design with mobile overlay and desktop fixed panel
  - Smart venue filtering using `useVenuesWithEvents` hook
- **Performance**: Infinite scrolling with optimized data loading
- **SEO Ready**: Proper meta tags and structured data
- **Analytics**: Google Analytics 4 integration for user tracking
- **Security**: Row Level Security (RLS) policies for data protection
- **Scalability**: Modular component architecture for easy expansion
  - Multi-keyword and edge case testing
  - Advanced search features and interactions
  - Real-time database validation
  - Performance and user experience testing
- **🎯 Host Event Workflow**: Complete event creation testing with systematic validation (NEW)
  - Section-by-section form filling and validation
  - Advanced UI component interaction (switches, sliders, checkboxes)
  - Dynamic event naming with timestamps
  - Venue selection and organizer terms management
  - Comprehensive form submission testing

### Test Setup

The automation tests are configured to test the complete user journey including:
- Site navigation and location setup
- Homepage verification
- Google OAuth authentication flow
- Event creation and management workflows
- **Host event workflow with comprehensive form testing**
- **Search functionality with database validation**

### Prerequisites

1. **Install Playwright**:
   ```sh
   npm install
   npx playwright install
   ```

2. **Environment Configuration**:
   Ensure your `.env` file includes the test credentials:
   ```
   VITE_SUPABASE_URL=your_supabase_project_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
   ```

### Running Tests

#### Standard User Journey Test
Tests the complete user flow from site access to authentication:

```sh
# Run the comprehensive flow test
npm run test:tests -- tests/standard-setup.spec.ts --project=chromium --grep "should complete comprehensive flow"

# Run with headed browser (visible)
npm run test:tests -- tests/standard-setup.spec.ts --headed --project=chromium

# Run location setup only test
npm run test:tests -- tests/standard-setup.spec.ts --project=chromium --grep "should complete location setup"
```

#### Event Creation Tests
Tests event creation workflows with mandatory field validation:

```sh
# Run event creation tests
npm run test:tests -- tests/event-creation.spec.ts --project=chromium
```

#### Host Event Tests (NEW)
Comprehensive end-to-end testing of the complete event creation workflow:

```sh
# Run the comprehensive host event test
npm run test:host-event

# Run with visible browser for debugging
npm run test:host-event -- --headed

# Run with specific timeout
npm run test:host-event -- --timeout=120000

# Run specific test case
npm run test:host-event -- --grep "should systematically fill all sections"
```

#### Homepage Elements Tests
Comprehensive homepage verification with database validation and link testing:

```sh
# Run complete homepage verification test
npm run test:tests -- tests/homepage-elements.spec.ts --project=chromium

# Run specific homepage component tests
npm run test:tests -- tests/homepage-elements.spec.ts --grep "should verify all homepage components are present and functional"

# Debug homepage tests with visible browser
npm run test:tests -- tests/homepage-elements.spec.ts --headed --project=chromium

# Run with detailed debugging
npm run test:tests -- tests/homepage-elements.spec.ts --headed --debug --timeout=0
```

#### Search Functionality Tests
Comprehensive testing of search features with database validation:

```sh
# Run all search tests
npm run test:search-all

# Individual search test suites
npm run test:homepage-elements    # Homepage search functionality
npm run test:search              # Advanced search features
npm run test:search-validation   # Data validation tests

# Specific search test scenarios
npm run test:tests -- tests/homepage-elements.spec.ts --grep "should search for events and verify UI count matches database count"
npm run test:tests -- tests/search-functionality.spec.ts --grep "should handle search operations efficiently"
npm run test:tests -- tests/search-validation.spec.ts --grep "should display accurate event information"

# Debug search tests with visible browser
npm run test:search -- --headed
```

### Test Coverage

#### 1. **Standard Setup Test** (`tests/standard-setup.spec.ts`)
- **Complete User Journey**: Full flow from site opening to authentication
- **Location Setup Only**: Quick setup for tests that don't need authentication

**Test Steps**:
1. **Site Opening**: Navigate to TheLocalAdda homepage
2. **PIN Code Entry**: Enter PIN code (110019) and close location modal
3. **Location Selection**: Select Chittranjan Park from dropdown
4. **Homepage Verification**: Verify hero image, headings, description, and branding
5. **Google Authentication**: Complete OAuth flow with email and password
6. **OAuth Consent Handling**: Detect and click Continue button on consent screen
7. **Authentication Verification**: Look for "Successfully signed in" popup or fallback indicators
8. **Cleanup**: Sign out after test completion

#### 1.5. **Homepage Elements Test** (`tests/homepage-elements.spec.ts`) - **NEW COMPREHENSIVE TESTING**
Complete homepage verification with database integration and link validation:

**Homepage Component Verification**:
1. **Hero Section**: Hero image, "Find Local Events That Matter" heading, "Discover The Community Near You" subheading
2. **Description**: "Connect with families, discover new activities, and build a stronger community in your neighborhood."
3. **Search Interface**: Search textbox ("search for events" placeholder) + "Discover Events" button
4. **Popular Categories**: Section heading + database-validated category list (5/8 categories: Culture, Education, Families, Food, Sports)
5. **Discover Upcoming Events**: Section heading + "Find the perfect activities for you and your family near you" + "View All Events" button
6. **Events Grid**: 2x3 layout with 5 database-validated events + "Discover More Events" card + "Click here to explore" button
7. **Site Branding**: TheलोकलAdda logo and navigation

**Database Integration & Validation**:
- **Categories**: Queries `event_categories` table, validates 5/8 active categories displayed on homepage
- **Events**: Queries `events` table for upcoming approved events, achieves 100% match with homepage (5/5 events)
- **Real-time Verification**: All UI content validated against live database data

**Link Testing & 404 Detection**:
- **Total Links**: 25 links analyzed and categorized (Navigation: 4, Events: 5, Categories: 3, Internal: 13)
- **Working Links**: 14 functional links verified
- **Broken Links**: 6 correctly identified 404s (dashboard, guidelines, help, privacy, terms, cookies)
- **SPA 404 Detection**: Smart detection of Single Page Application 404s that return HTTP 200 but show homepage content
- **Content Analysis**: Validates page titles, content, and redirects to identify disguised 404 pages

**Key Achievements**:
- ✅ **Perfect Database Integration**: 100% match between UI and database content
- ✅ **Comprehensive Component Coverage**: All homepage sections verified
- ✅ **Smart Link Validation**: Proper 404 detection in SPA applications
- ✅ **Real-time Data Validation**: Live database queries ensure data consistency
- ✅ **Robust Element Detection**: Multiple selector strategies for UI changes

#### 2. **Event Creation Test** (`tests/event-creation.spec.ts`)
- **Mandatory Field Validation**: Tests that all required fields are properly validated
- **Form Submission Flow**: Tests the complete event creation process
- **Authentication Requirements**: Ensures event creation requires user authentication

#### 2.5. **Host Event Test** (`tests/host-event.spec.ts`) - **NEW COMPREHENSIVE EVENT CREATION TESTING**
Complete end-to-end testing of the event creation workflow with systematic section-by-section validation:

**Comprehensive Event Creation Flow**:
1. **Authentication Setup**: Complete Google OAuth authentication with consent handling
2. **Event Form Navigation**: Navigate to host event page and verify form accessibility
3. **Systematic Section Processing**: Fill all form sections in logical order with validation
4. **Field Detection & Interaction**: Advanced field detection with multiple selector strategies
5. **Form Submission**: Complete form submission with success verification

**Section-by-Section Testing**:
- **Section 1: Basic Event Information**: Event title (with timestamp), description, category, tags
- **Section 2: Date & Time**: Start date (4 days from current), start time, end time, max attendees
- **Section 3: Location & Venue**: Venue search and selection with "bipin" keyword, parking instructions
- **Section 4: Organizer Details**: Organizer name, email, phone, website URL
- **Section 5: Pricing & Tickets**: Free event slider verification (kept ON), pricing field skipping
- **Section 6: Organizer Terms Settings**: "Enable Organizer Terms and Conditions" switch (set to OFF)
- **Section 7: User Agreement Checkboxes**: "I Agree to Terms and Conditions" and "I Agree to Privacy Policy" (both set to TRUE)
- **Section 8: Final Submission**: Form validation and successful submission

**Advanced Features**:
- **Dynamic Event Naming**: Event titles include timestamp for unique identification (`Test Community Event - Systematic Section Test - 2025-01-14 15-30-45`)
- **Smart Field Detection**: Multiple selector strategies for robust element detection across UI changes
- **State Management**: Proper handling of checkbox states, slider positions, and form validation
- **Error Recovery**: Graceful handling of element interception, hidden elements, and timing issues
- **Venue Selection**: Automated venue search with "bipin" keyword and dropdown selection
- **Switch/Slider Handling**: Proper interaction with custom UI components (switches, sliders, checkboxes)
- **Label-Based Interaction**: Clicking labels for hidden checkboxes to ensure proper state changes

**Technical Achievements**:
- ✅ **Complete Form Coverage**: All form sections systematically tested and validated
- ✅ **Advanced Element Interaction**: Handles complex UI components (switches, hidden checkboxes, dropdowns)
- ✅ **State Verification**: Confirms all form elements are in correct states before submission
- ✅ **Robust Selector Strategy**: Multiple fallback selectors for reliable element detection
- ✅ **Comprehensive Logging**: Detailed step-by-step logging for debugging and verification
- ✅ **Error Handling**: Graceful handling of timing issues, element interception, and state conflicts

#### 3. **Homepage Elements Tests** (`tests/homepage-elements.spec.ts`)
Comprehensive homepage verification with database validation and link testing:

**Homepage Component Verification**:
- **Hero Section**: Verifies hero image, main heading "Find Local Events That Matter", subheading "Discover The Community Near You"
- **Description Text**: Validates "Connect with families, discover new activities, and build a stronger community in your neighborhood."
- **Search Interface**: Tests search textbox with "search for events" placeholder and "Discover Events" button
- **Popular Categories**: Verifies "Popular Categories" section with database validation (5/8 categories displayed)
- **Events Listing**: Validates 2x3 grid layout with 5 events + "Discover More Events" card
- **Discover More Section**: Tests "There are many more events happening in your area" text and "Click here to explore" button

**Database Integration**:
- **Categories Validation**: Queries `event_categories` table and verifies UI displays match database (Culture, Education, Families, Food, Sports)
- **Events Validation**: Queries `events` table for upcoming approved events and validates 100% match with homepage display
- **Real-time Verification**: Each test queries database to ensure UI content matches backend data exactly

**Link Testing & 404 Detection**:
- **Comprehensive Link Analysis**: Counts and categorizes all links (navigation, events, categories, internal, external)
- **Smart 404 Detection**: Detects SPA 404s that return HTTP 200 but show homepage content instead of actual pages
- **Broken Link Identification**: Identifies 6 broken links (dashboard, guidelines, help, privacy, terms, cookies)
- **Link Categories**: Navigation (4), Event links (5), Category links (3), Internal links (13)
- **Content Analysis**: Checks page content, titles, and redirects to identify disguised 404 pages

**Search Functionality Tests** (`tests/search-functionality.spec.ts`, `tests/search-validation.spec.ts`):
- **Database Count Verification**: Tests search for "cook" and verifies UI count matches database exactly
- **Multi-keyword Search**: Tests different search terms (music, workshop, food, art) with result validation
- **Edge Cases Handling**: Tests empty search, whitespace, non-existent terms, single characters, very long terms
- **Special Characters**: Tests search with café, music & dance, art@gallery, workshop-2024, event#1
- **Performance Testing**: Measures search response times and ensures performance within acceptable limits
- **Content Accuracy**: Validates search results display correct event information matching database

**Key Features**:
- **Perfect Database Integration**: 100% match rate between UI events and database content
- **SPA-Aware Link Testing**: Properly detects 404s in Single Page Applications that return HTTP 200
- **Comprehensive Coverage**: Tests all homepage sections from hero to events grid to footer links
- **Real-time Database Validation**: Each test queries Supabase database to verify UI results match expected data
- **Smart Element Detection**: Uses multiple selector strategies for robust component detection
- **Debug Support**: Detailed logging, screenshots, and error context for troubleshooting

### Test Configuration

#### Playwright Configuration (`playwright.config.ts`)
- **Browsers**: Chromium, Firefox, WebKit support
- **Base URL**: Configured for local development (`http://localhost:5173`)
- **Timeouts**: Optimized for OAuth flows and network requests
- **Screenshots**: Automatic screenshots on test failures
- **Video Recording**: Available for debugging failed tests

#### Test Helpers (`tests/helpers/standard-setup.ts`)
Reusable functions for common test operations:
- `openSite()`: Navigate to the homepage
- `setLocationWithPinCode()`: Handle PIN code entry and location modal
- `selectChitranjanPark()`: Select location from dropdown
- `verifyHomepageElements()`: Verify all homepage elements are loaded
- `authenticateWithGoogle()`: Complete Google OAuth authentication
- `handleConsentScreens()`: Handle OAuth consent screens
- `verifyAuthentication()`: Verify successful authentication
- `signOut()`: Clean up by signing out

#### Database Test Helper (`tests/helpers/test-supabase-client.ts`)
Node.js compatible Supabase client for test database operations:
- **Environment Compatibility**: Uses `process.env` instead of `import.meta.env` for Node.js test environment
- **Database Queries**: Enables real-time database validation in search tests
- **Type Safety**: Maintains full TypeScript support with Supabase database types
- **Error Handling**: Provides clear error messages for database connection issues
- **Configuration Logging**: Logs connection details for debugging (without exposing sensitive keys)

### Authentication Testing

The automation includes robust Google OAuth testing:

#### OAuth Flow Handling
1. **Email Entry**: Automatically enters test email (`<EMAIL>`)
2. **Password Entry**: Securely handles password input
3. **Consent Screen Detection**: Detects various OAuth consent page formats
4. **Continue Button Clicking**: Finds and clicks consent buttons using multiple selectors
5. **Redirect Handling**: Waits for successful redirect back to the application
6. **Success Verification**: Looks for "Successfully signed in" popup or authentication indicators

#### Consent Screen Robustness
The test handles multiple consent screen scenarios:
- `/signin/oauth/consent` - Standard consent page
- `/signin/oauth/id` - Alternative consent page format
- Auto-navigation detection between different Google OAuth pages
- Fallback to traditional authentication indicators when popup is not found

### Test Data and Credentials

#### Test Account
- **Email**: `<EMAIL>`
- **Location**: Chittranjan Park, Delhi (PIN: 110019)
- **Purpose**: Dedicated test account for automation testing

#### Location Data
- **Default PIN**: 110019 (Chittranjan Park)
- **Test Location**: Chittranjan Park, Delhi
- **Coordinates**: Automatically detected via Google Places API

### Running Tests in CI/CD

For continuous integration, tests can be run headlessly:

```sh
# Headless execution for CI/CD
npm run test:tests -- --project=chromium

# Generate HTML report
npx playwright show-report
```

### Test Debugging

#### Screenshots and Videos
- **Automatic Screenshots**: Taken on test failures
- **Video Recording**: Available for debugging complex flows
- **HTML Reports**: Comprehensive test reports with timeline and traces

#### Debug Mode
```sh
# Run tests in debug mode
npx playwright test --debug

# Run specific test with debugging
npx playwright test tests/standard-setup.spec.ts --debug

# Debug search tests specifically
npm run test:search -- --headed --debug
npm run test:tests -- tests/search-functionality.spec.ts --grep "performance" --headed --timeout=0
```

#### Search Test Debugging
For search-specific debugging and troubleshooting:

```sh
# Run search tests with maximum debugging
npm run test:tests -- tests/homepage-elements.spec.ts --headed --debug --timeout=0

# Test specific search scenarios
npm run test:tests -- tests/search-validation.spec.ts --grep "no results" --headed

# Generate detailed HTML report for search tests
npm run test:search-all && npx playwright show-report
```

**Common Search Test Issues**:
- **Selector Not Found**: Check `search-results-debug.png` screenshot in test results
- **Count Mismatch**: Review database query logs and UI element detection
- **Environment Variables**: Verify `.env` file contains correct Supabase credentials
- **Database Connection**: Check test console for Supabase client configuration logs

### Test Maintenance

#### Updating Test Selectors
When UI changes occur, update selectors in:
- `tests/helpers/standard-setup.ts` - For reusable functions
- Individual test files - For test-specific selectors
- **Search Tests**: Update event card selectors in all three search test files when EventCard component changes

#### Adding New Tests
1. Create new test file in `tests/` directory
2. Import helper functions from `tests/helpers/standard-setup.ts`
3. Follow existing patterns for authentication and setup
4. Use descriptive test names and console logging for debugging

#### Adding New Host Event Tests
For event creation workflow tests:
1. **Use `tests/host-event.spec.ts`** as the primary file for event creation testing
2. **Follow section-by-section approach**: Use `processSection()` function for systematic form filling
3. **Include comprehensive logging**: Add detailed console.log statements for each step
4. **Handle complex UI components**: Use appropriate selectors for switches, sliders, and hidden checkboxes
5. **Verify form states**: Confirm all elements are in correct states before proceeding
6. **Test error scenarios**: Include tests for validation errors and edge cases

#### Adding New Search Tests
For search functionality tests:
1. **Choose appropriate test file**:
   - `tests/homepage-elements.spec.ts` - Basic search functionality
   - `tests/search-functionality.spec.ts` - Advanced features and interactions
   - `tests/search-validation.spec.ts` - Data validation and accuracy
2. **Use `performLocationSetupOnly(page)`** for consistent setup without authentication
3. **Include database validation** using `testSupabase` client for data consistency checks
4. **Add comprehensive logging** with console.log statements for debugging
5. **Follow selector patterns**: Use `div.group.w-full.rounded-2xl[class*="shadow-smooth"]` for event cards

#### Search Test Maintenance
- **Database Schema Changes**: Update queries in test files when event/category schema changes
- **UI Component Updates**: Update selectors when EventCard or search components change
- **Performance Thresholds**: Adjust performance expectations in `search-functionality.spec.ts` as needed
- **Search Logic Changes**: Update database queries to match frontend search implementation

### Best Practices

#### Test Structure
- **Setup**: Use `performStandardSetup()` or `performLocationSetupOnly()`
- **Test Logic**: Focus on specific functionality being tested
- **Cleanup**: Always sign out or clean up test data
- **Assertions**: Use meaningful assertions with clear error messages

#### Reliability
- **Wait Strategies**: Use appropriate waits for dynamic content
- **Error Handling**: Graceful handling of network timeouts and page changes
- **Retry Logic**: Built-in retry for flaky network operations
- **Isolation**: Each test runs independently without dependencies

#### Search Test Best Practices
- **Database Validation**: Always verify UI results against database queries for data integrity
- **Multiple Selectors**: Use fallback selectors for robust element detection across UI changes
- **Performance Monitoring**: Include timing measurements for search operations
- **Edge Case Coverage**: Test empty searches, special characters, and non-existent terms
- **User Experience**: Test complete user journeys including result interaction and navigation

### Additional Resources

#### Search Testing Guide
For comprehensive search testing documentation, see [`SEARCH_TESTING_GUIDE.md`](./SEARCH_TESTING_GUIDE.md):
- **Detailed test scenarios** and expected outcomes
- **Debugging tips** for common search test issues
- **Performance benchmarks** and optimization guidelines
- **Maintenance procedures** for search test updates
- **Troubleshooting guide** for database connection and selector issues

## Building for Production

To create a production build:

```sh
npm run build
```

The production-ready files will be generated in the `dist` directory.

## Deployment

The application can be deployed to any static hosting service. For Supabase integration, ensure that you:

1. Configure the correct Site URL and Redirect URLs in Supabase Auth Settings
2. Set up the appropriate environment variables for your production environment

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## Support

For support or questions, please open an issue in the repository or contact the project maintainers.
