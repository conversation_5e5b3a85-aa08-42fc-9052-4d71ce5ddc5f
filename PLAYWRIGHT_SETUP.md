# Playwright Test Setup - Fixed Issues

## What Was Wrong with homepage.spec.ts

### The Problem
Your `homepage.spec.ts` stopped working because of a configuration conflict:

1. **Hard-coded URLs**: The test was using `https://thelocaladda.com` (production)
2. **Local dev server config**: Playwright config was set to start local dev server on `http://localhost:5173`
3. **Conflicting baseURL**: The config had `baseURL: 'http://localhost:5173'` but tests used production URLs
4. **WebServer mismatch**: The webServer was trying to start local dev but tests didn't use it

### The Fix
1. **Flexible URL handling**: Tests now use `page.goto('/')` which respects the `baseURL` from config
2. **Environment-based configuration**: Added `BASE_URL` environment variable support
3. **Conditional webServer**: Only starts local dev server when testing locally
4. **TypeScript fixes**: Added node types to tsconfig.json

## How to Run Tests Now

### Test Against Production (Default)
```bash
# Run all tests against production
npm run test:tests

# Run specific tests against production
npm run test:homepage
npm run test:event-creation
```

### Test Against Local Development
```bash
# Start your dev server first
npm run dev

# Then in another terminal, run tests against local
npm run test:local

# Or set the environment variable directly
BASE_URL=http://localhost:5173 npx playwright test
```

### Test Specific Files
```bash
# Homepage tests only
npm run test:homepage

# Event creation tests only
npm run test:event-creation

# Run with browser UI visible
npx playwright test tests/homepage.spec.ts --headed

# Run in debug mode
npx playwright test tests/homepage.spec.ts --debug
```

## Configuration Details

### Environment Variables
- `BASE_URL`: Controls which URL to test against
  - Default: `https://thelocaladda.com` (production)
  - Local: `http://localhost:5173`

### Playwright Config Changes
1. **Dynamic baseURL**: Uses `process.env.BASE_URL` or defaults to production
2. **Conditional webServer**: Only starts when `BASE_URL` is localhost
3. **Added node types**: Fixed TypeScript errors

### Test File Changes
1. **Relative URLs**: Changed from `page.goto('https://thelocaladda.com')` to `page.goto('/')`
2. **Wait for load**: Added `page.waitForLoadState('networkidle')` for better reliability
3. **Better test names**: Updated test describe block name

## Current Test Status

### ✅ Working Tests
- **homepage.spec.ts**: Tests site name display and PIN code location setting
- **event-creation.spec.ts**: Tests event creation form validation

### 🔧 Test Features
- **Authentication handling**: Tests gracefully handle auth redirects
- **Form validation**: Comprehensive mandatory field validation
- **Cross-browser**: Tests run on Chromium, Firefox, and WebKit
- **Screenshots/Videos**: Captured on failure for debugging

## Troubleshooting

### If homepage tests still fail:

1. **Check the site is accessible**:
   ```bash
   curl -I https://thelocaladda.com
   ```

2. **Test locally first**:
   ```bash
   npm run dev
   # In another terminal:
   npm run test:local
   ```

3. **Run with debug info**:
   ```bash
   npx playwright test tests/homepage.spec.ts --headed --reporter=line
   ```

### If event creation tests fail:

1. **Check authentication**: Tests will skip if redirected to auth
2. **Verify form structure**: Selectors might need updating if form changed
3. **Run against local**: Some features might work differently locally vs production

### Common Issues

1. **"Cannot find name 'process'"**: Fixed by adding node types to tsconfig
2. **"webServer timeout"**: Only happens when testing locally, increase timeout if needed
3. **"Element not found"**: Form structure might have changed, update selectors

## Next Steps

### For Local Development Testing
1. Always start dev server first: `npm run dev`
2. Run tests: `npm run test:local`
3. Check console for any errors

### For Production Testing
1. Just run: `npm run test:production` or `npm run test:tests`
2. Tests will run against live site

### For CI/CD
Set environment variables in your CI:
```bash
# For local testing in CI
BASE_URL=http://localhost:5173

# For production testing in CI (default)
BASE_URL=https://thelocaladda.com
```

## Test Development Tips

1. **Use page.goto('/')** instead of full URLs
2. **Add wait conditions** for dynamic content
3. **Handle authentication gracefully** with conditional skips
4. **Use environment variables** for different test environments
5. **Test locally first** before running against production

Your tests should now work reliably in both local and production environments! 🎉
